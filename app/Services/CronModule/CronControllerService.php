<?php
namespace App\Services\CronModule;

use Carbon\Carbon;
use App\Model\User;
use App\Model\Causes;
use App\Model\Analyse;
use App\Model\Submenu;
use App\Model\Language;
use App\Model\BodyImage;
use App\Model\CronSetup;
use App\Model\CronSetting;
use App\Model\MentalImage;
use App\Model\UserSubMenu;
use App\Model\CronSetupTable;
use App\Model\CronsetupTimes;
use App\Model\EmailPlaceholder;
use App\Model\CronsettingOption;
use App\Model\CronsetupAnalysis;
use Yajra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Gloudemans\Shoppingcart\Facades\Cart;


class CronControllerService{

    public function index($request){
        $query = CronSetupTable::with('cronsetuptimes')->withCount('cronsetuptimes')
            ->select('cron_setups.*', DB::raw("CONCAT(first_name, ' ', last_name) AS user_full_name"), "cronsetup_options.cal_type")
            ->join('users', 'users.id', '=', 'cron_setups.selected_user')
            ->join("cronsetup_options", "cronsetup_options.cron_setup_id", "=", "cron_setups.id")
            ->where('user_id', getAuthID());

            $date = Carbon::today()->subDays(90)->format('Y-m-d');

            if ($request->archive != "archive") $query->where("added", ">=", $date);
            else $query->where("added", "<",  $date);

            if($request->filled('status')){
                $__status = (int) $request->status;
                if($__status === 2) $query->where('is_stop', 1);
                elseif($__status === 1) $query->where('status', 2);
                // elseif($__status === 3) $query->where('as_draft', true);
                else $query->where('status','!=', 2);
            }

            if($request->filled('type')){
              ($request->type == 1) ? $query->where('length', 1) : $query->where('length', '>', 1);
            }

            if($request->filled('user')){
                $query->where('selected_user', $request->user);
            }
            if($request->filled('range')){
                // Your date range string
                $dateRange = $request->range;

                // Split the date range into start and end dates
                list($startDate, $endDate) = explode(" to ", $dateRange);

                // Format the dates in a way suitable for a database query (e.g., for MySQL)
                $formattedStartDate = date('Y-m-d', strtotime($startDate));
                $formattedEndDate = ($endDate)?date('Y-m-d', strtotime($endDate)):date('Y-m-d', strtotime($startDate));

                // Apply the WHERE condition to the cronsetuptimes table
                $query->whereHas('cronsetuptimes', function ($query) use ($formattedStartDate, $formattedEndDate) {
                    $query->where(function ($query) use ($formattedStartDate, $formattedEndDate) {
                        $query->where(function ($query) use ($formattedStartDate, $formattedEndDate) {
                            $query->whereDate('start_time', '>=', $formattedStartDate)
                                ->whereDate('start_time', '<=', $formattedEndDate);
                        })->orWhere(function ($query) use ($formattedStartDate, $formattedEndDate) {
                            $query->whereDate('end_time', '>=', $formattedStartDate)
                                ->whereDate('end_time', '<=', $formattedEndDate);
                        });
                    });
                });
            }

            if($request->order[0]['dir'] === 'ASC' || $request->order[0]['dir'] === 'asc') $query->orderBy('cron_setups.created_at', 'desc');

        $results = $query->where('is_deleted', 0);
        return Datatables::of($results)
            ->filter(function ($query) use ($request) {
                if ($request->search['value']) $query->where(DB::raw('CONCAT_WS(" ", users.first_name, users.last_name)'), 'like', '%' . $request->search['value'] . '%');
            })->setRowId(function ($cron) {
                return "cron_" . $cron->id;
            })->editColumn('user_full_name', function ($cron) {
                return $cron->user_full_name;
            })->editColumn('note', function ($cron) {
                return '<span class="note">'. $cron->note.'</span><br> <span class="unique-id">'. $cron->session_id.'</span>';
            })
            ->editColumn('start_date_time', function ($row) {
                // return date_change(Carbon::parse($row->cronsetuptimes[0]->start_time));
                // return date_change(date('d-m-Y', strtotime($row->added)));
                return date_change_with_time($row->cronsetuptimes->first()->start_time ?? '');
            })->editColumn('end_date_time', function ($row) {
                // return Carbon::parse($row->cronsetuptimes->first()->start_time)->format('H:i');
                return date_change_with_time($row->cronsetuptimes->last()->start_time ?? '');
            })
            // ->editColumn('end_date', function ($row) {
            //     // $lastdate = $row->cronsetuptimes->last();
            //     // return date_change(Carbon::parse($row->cronsetuptimes->last()->end_time));
            //     // return date_change(date('d-m-Y', strtotime($lastdate->end_time)));
            //     return date_change(date('d-m-Y', strtotime($row->cronsetuptimes->last()->end_time)));
            // })
            // ->editColumn('end_time', function ($row) {
            //     // $lastdate = $row->cronsetuptimes->last();
            //     // return date('H:i', strtotime($lastdate->end_time));
            //     // return Carbon::parse($row->cronsetuptimes->last()->end_time)->format('H:i');
            //     return date('H:i', strtotime($row->cronsetuptimes->last()->end_time));
            // })
            // ->editColumn('total_length', function ($row) {
            //     $totalTime = 0;
            //     $row->cronsetuptimes->map(function($time) use(&$totalTime){
            //         $startTimestamp = strtotime($time->start_time);
            //         $endTimestamp = strtotime($time->end_time);
            //         // Calculate the time difference in seconds
            //         $totalTime += $endTimestamp - $startTimestamp;


            //     });
            //     return $totalTime;
            // })
            // ->editColumn('start_server_date', function ($row) {
            //     return date('d-m-Y', strtotime($row->cronsetuptimes->first()->start_send_mail));
            // })->editColumn('start_server_time', function ($row) {
            //     return date('H:i', strtotime($row->cronsetuptimes->first()->start_send_mail));
            // })->editColumn('end_server_date', function ($row) {
            //     return date('d-m-Y', strtotime($row->cronsetuptimes->first()->end_send_mail));
            // })->editColumn('end_server_time', function ($row) {
            //     return date('H:i', strtotime($row->cronsetuptimes->first()->end_send_mail));
            // })
            // ->editColumn('timezone', function ($row) {
            //     return $row->cronsetuptimes->first()->timezone;
            // })
            ->editColumn('cron_type', function ($row) {
                if ($row->cron_type == 0) $cron_type = trans('action.normal');
                else $cron_type = trans('action.cart');
                if ($row->length == 0) return trans('action.due_power') . '(' . $cron_type . ')';
                return ($row->length > 1) ? trans('action.series_cron_view') . '(' . $cron_type . ')' : trans('action.single_cron_view') . '(' . $cron_type . ')';
            })->editColumn('run_time', function ($row) {
                if ($row->length > 1) {
                    return $row->length . ' ' . trans('action.cron_days');
                } else {
                    $lastdate = $row->cronsetuptimes->last();
                    $totalTime = totalTime($row->cronsetuptimes->first()->start_time ?? '', $lastdate->end_time ?? '');
                    return ($row->length > 1) ? $row->length . ' ' . trans('action.cron_days') : $totalTime;
                }
            })->editColumn('status', function ($row) {
                $today   = date("Y-m-d");
                $lastday = date('Y-m-d', strtotime($row->added . '+' . $row->length . ' days'));

                if ($today > $lastday && ($row->status == 0 ||  $row->status == 1)) {
                    DB::table('cron_setups')->where('id', $row->id)->update(['status' => 2]);
                    // $row->status = 1;
                }

                if ($row->as_draft == 1) return trans('action.draft');
                else if ($row->is_stop == 1) return trans('action.stop');
                else return ($row->status == 2) ? trans('action.done') : trans('action.online');
                // else return ($row->status == 1) ? trans('action.done') : trans('action.online');
            })->editColumn('open_status_by_user', function ($row) {
                $totalCron = count($row->cronsetuptimes);
                $cronOpen = 0;
                foreach ($row->cronsetuptimes as $ocron) if ($ocron->email_open_status == 1) $cronOpen++;

                return ($cronOpen > 0) ? trans('action.cron_open') . " (" . $cronOpen . "/" . $totalCron . ")" : trans('action.cron_not_open') . " (" . $cronOpen . "/" . $totalCron . ")";
            })->addColumn('action', function ($row) {

                $hasCronSetup = $row->cronsetuptimes && $row->cronsetuptimes->count() > 0;
                return '
                        <div class="crontable-action-btn">
                            <ul>
                                ' .($hasCronSetup
                                ? '<li>
                                    <a class="actn-btn" data-id="' . $row->id . '" id="show_cron" href="#!" title="' . trans('action.show_data_cron_view') . '">
                                        <i class="fas fa-eye"></i>&nbsp;<span>' . trans('action.show_data_cron_view') . '</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="share-toltip-box share_cron actn-btn" data-uniqueid="'.$row->unique_id.'" data-id="' . $row->id . '" id="share_cron-'.$row->id.'">
                                        <i class="fas fa-share"></i>&nbsp;<span>' . trans('action.share_cron') . '</span>
                                    </a>
                                    <span class="share-tooltip-content"></span>
                                </li>
                                <li>
                                    <a title="' . trans('action.cron_duplicate') . '" href="javascript:void()" data-url="' . route('cron.duplicate', [$row->id]) . '" class="actn-btn cron-duplicate">
                                        <i class="fas fa-clone"></i>
                                        &nbsp;
                                        <span>' . trans('action.cron_duplicate') . '</span>
                                    </a>
                                </li>'
                                : '') . '
                                <li>
                                    <a href="javascript:void()" title="' . trans('action.delete') . '" data-url="' . route("cron.deleteCronSetup", [$row->id]) . '" class="sweet_confirm text-danger actn-btn" >
                                        <i class="fas fa-trash-alt danger-color"></i>&nbsp;
                                        <span>' . trans('action.delete') . '</span>
                                    </a>
                                </li>
                            </ul>

                            <input type="hidden" class="cron-progress" value="' . $row->setup_status . '" data-id="' . $row->id . '" id="cp_' . $row->id . '">
                        </div>
                ';
            })
            ->rawColumns(['action', 'note'])
            ->make(true);

    }

    public function getCronById($id,$with = []){
        return  CronSetupTable::with($with)->find($id);
    }

    public function getTrush($request){
        $cron = CronSetupTable::with('cronsetuptimes:id,cronsetup_id,start_time,end_time')
        ->join('users','users.id','cron_setups.selected_user')
        ->select('cron_setups.id','cron_setups.added','cron_setups.length','cron_setups.selected_user', DB::raw("CONCAT(first_name, ' ', last_name) AS user_full_name"))
        ->where('is_deleted', 1)
        ->where('user_id', getAuthID());

        return Datatables::of($cron)
        ->filter(function ($query) use ($request) {
            if ($request->search['value']) {
                $query->where(DB::raw('CONCAT_WS(" ", users.first_name, users.last_name)'), 'like', '%' . $request->search['value'] . '%');
            }
        })
        ->editColumn('user_full_name', function ($row) {
            return $row->user_full_name;
        })->editColumn('start_date', function ($row) {
            return date_change(date('d-m-Y', strtotime($row->added)));
        })->editColumn('start_time', function ($row) {
            return date('H:i', strtotime($row->cronsetuptimes->first()->start_time ?? ''));
        })->editColumn('end_date', function ($row) {
            return date_change(date('d-m-Y', strtotime($row->added. "+".($row->length-1).trans('action.cron_days'))));
        })->editColumn('end_time', function ($row) {
            return date('H:i', strtotime($row->cronsetuptimes->first()->end_time ?? ''));
        })->editColumn('cron_type', function ($row) {
            return ($row->length > 1) ? trans('action.series_cron_view') : trans('action.single_cron_view');
        })->editColumn('run_time', function ($row) {
            if($row->length > 1) return $row->length. trans('action.cron_days');
            else{
                $lastdate = $row->cronsetuptimes->last();
                $totalTime = totalTime($row->cronsetuptimes->first()->start_time ?? '', $lastdate->end_time ?? '');
                return ($row->length > 1) ? $row->length . ' ' . trans('action.cron_days') : $totalTime;
            }
        })->editColumn('action', function ($row){
            return '<span class="d-inline-block cursor-pointer hover-primary button-demo">
                <a href="javascript:void(0)" id="trashrestore" data-url="'.route('cron.cronTrashRestore', $row->id).'" class="sweet_confirm">
                    <p class="text-primary ladda-button m-0" data-style="zoom-out">
                        <span class="ladda-label">
                        <i class="fas fa-window-restore"></i>&nbsp;<span class="hide-table-btn-text">
                        '.trans("action.restore_cron") .'</span></a>
                        </span>
                        <span class="ladda-spinner"></span>
                        <div class="ladda-progress" style="width: 0px;"></div>
                    </button>
            </span>';
        })
        ->escapeColumns([])
        ->make(true);
    }

    public function getCronSetting($userId){
        $cron = User::with(['smtp', 'cronsetting', 'cronsetting.preassignsubmenus'])->where('id', $userId)->first();
        $userlang = $cron->language_id;

        $daysOfNextMonth = Carbon::now()->addMonth()->daysInMonth;

        $languages  = Language::where('status',true)->orWhere('id',2)->get();

        $product = __getMenus();

        $menus = $product->whereIn('status',[1,2])->where('sp',false)->map(function($prod){

            return [
                'proId' => $prod->id,
                'product_name' => $prod->product_name,
                // 'submenu' => $prod->submenus,
                'submenus' => ($prod->status == 2 && $prod->submenus) ?
                $prod->submenus->map(function($submenu){
                    $submenu->product_id = $submenu->menu_id;
                    return $submenu;
                }) :
                $prod->submenus,
                'ownmenu' => ($prod->status == 2) ? 'yes':'no'
            ];
        });

        return [
            'cron' => $cron,
            'daysOfNextMonth' => $daysOfNextMonth,
            'languages' => $languages,
            'userlang' => $userlang,
            'menus' => $menus
        ];
    }

    public function storeCronSetting($request)
    {
        $userid = getAuthID();
        $cronSetting = CronSetting::firstOrNew(['user_id' => $userid]);

        #cron setting
        $cronSetting->from = (int)$request->from;
        $cronSetting->to = (int)$request->to;
        $cronSetting->red_value = $request->filled('redvalue') ? 1 : 0;
        $cronSetting->topic = $request->topic;
        $cronSetting->send_email_time = $request->filled('send_email_time') ? (int)$request->send_email_time : 0;

        $cronSetting->save();

        # Activity Log
        if (!$cronSetting->wasRecentlyCreated) {
            activityLogTrack(2, 'at Remote Treatment Setting', 'in Remote Treatment Settings', 'Remote Treatment', 'remote_treatment');
        } else {
            activityLogTrack(1, 'at Remote Treatment Setting', 'in Remote Treatment Settings added', 'Remote Treatment', 'remote_treatment');
        }
        #preselect submenus
        $cronSetting->preassignsubmenus()->delete();
        if ($request->has('preSubmenus')) {
            foreach ($request->preSubmenus as $key => $submenu) {
                $getId = explode('-', $submenu);
                $cronSetting->preassignsubmenus()->insert([
                    'cron_id' => $cronSetting->id,
                    'submenu_id' => (int)$getId[0],
                    'type' => str_contains($submenu, '-own') ? true : false
                ]);
            }
        }

        #cronsetup options
        $cronSetOpt = $cronSetting->cronsettingoption ?: new CronsettingOption;

        if(!$cronSetting->cronsettingoption) $cronSetOpt->cron_setting_id = $cronSetting->id;
        $cronSetOpt->due_power = $request->filled('due_power') ? 1 : 0;
        $cronSetOpt->days = $request->filled('treat_days') ? $request->treat_days : 0;
        $cronSetOpt->pdf_export = $request->filled('pdfexport') ? 1 : 0;
        $cronSetOpt->customer_link = $request->filled('customerlink') ? 1 : 0;
        $cronSetOpt->start_email = $request->filled('start_email_1x') ? 1 : 0;
        $cronSetOpt->end_email = $request->filled('end_email_1x') ? 1 : 0;
        $cronSetOpt->end_email_therapist = $request->filled('end_email_therapist') ? 1 : 0;
        $cronSetOpt->start_email_due_power = $request->filled('start_email_due_power') ? 1 : 0;
        $cronSetOpt->end_email_due_power = $request->filled('end_email_due_power') ? 1 : 0;
        $cronSetOpt->calculation_next_day = $request->filled('calculation_next_day') ? 1 : 0;
        $cronSetOpt->fixed_time = $request->filled('fixed_time') ? 1 : 0;
        $cronSetOpt->ra_status = $request->filled('ra_status') ? 1 : 0;
        $cronSetOpt->pdf_status = $request->filled('pdf_status') ? 1 : 0;
        $cronSetOpt->causes = $request->filled('causes') ? 1 : 0;

        $cronSetOpt->medium = $request->filled('medium') ? 1 : 0;
        $cronSetOpt->tipp = $request->filled('tipp') ? 1 : 0;
        $cronSetOpt->preview_status =  $request->filled($request->list_status) ? 1 : 0;
        $cronSetOpt->pre_time =  isset($request->pre_time) ? $request->pre_time : null;
        $cronSetOpt->save();
        # Activity Log
        if (!$cronSetOpt->wasRecentlyCreated) {
            activityLogTrack(1, 'at Remote Treatment Setting Option', 'im Fernbehandlung Einstellungen Options hinzugefügt', 'Remote Treatment', 'remote_treatment'); #
        } else {
           activityLogTrack(2, 'at Remote Treatment Setting Option', 'im Fernbehandlung Einstellungen Options', 'Remote Treatment', 'remote_treatment'); #AL
        }
        return true;
    }

    public function storeMailTemplates($request)
    {
        $userId = getAuthID();
        $language = Language::find($request->langid);
        $cronSetting = CronSetting::firstOrNew(['user_id' => $userId]);
        $cronSetting->user_id = $userId;
        $cronSetting->red_value = false;
        $cronSetting->send_email_time = false;
        $cronSetting->save();

        $templateType = [
            'start' => ['email_template_title_start', 'email_template_start'],
            'end' => ['email_template_title_stop', 'email_template_stop'],
            'red' => ['email_template_title_blank', 'email_template_blank'],
            'green' => ['email_template_title_green', 'email_template_green'],
            'resend' => ['email_template_title_resend', 'email_template_resend'],
            'footer' => ['email_template_footer', 'email_template_footer'],
        ];
        $templateKey = $templateType[$request->from] ?? null;
        if (!$templateKey) {
                return false;
        }

        $table = ($language->short_code == 'de') ? 'email_templates' : $language->short_code.'_email_templates';
        $emailTemplate = DB::table($table)->where('cron_setting_id', $cronSetting->id);
        if(!$emailTemplate->exists()){
            $emailTemplate->insert([
                "cron_setting_id" => $cronSetting->id,
                $templateKey[0] => $request->subject,
                $templateKey[1] => $request->content,
            ]);
        } else $emailTemplate->update([
            $templateKey[0] => $request->subject,
            $templateKey[1] => $request->content,
        ]);

        return $emailTemplate;
    }

    public function getTemplate($request)
    {
        $userid = getAuthID();
        $language = Language::find($request->langid);
        $shortCode = $language->short_code;
        $table = ($request->langid == '2') ? 'email_templates' : $language->short_code.'_email_templates';
        $cronSetting = CronSetting::select('id')->where('user_id', $userid)->first();
        $template = DB::table($table)->where('cron_setting_id', $cronSetting->id)->first();
        if(Cache::has('defaultPlaceHolder')){
            $defaultPlaceHolder = Cache::get('defaultPlaceHolder');
        }else{
            $defaultPlaceHolder = EmailPlaceholder::get();
            Cache::forever('defaultPlaceHolder',$defaultPlaceHolder);
        }

        switch ($request->from) {
            case 'start':
                $subject = $template->email_template_title_start ??
                    $defaultPlaceHolder->where('type', "start_treatment_title_".$shortCode)->first()->message ?? '';
                $content = $template->email_template_start ??
                    $defaultPlaceHolder->where('type', "start_treatment_".$shortCode)->first()->message ?? '';
                break;
            case 'end':
                $subject = $template->email_template_title_stop ??
                    $defaultPlaceHolder->where('type', "end_treatment_title_".$shortCode)->first()->message ?? '';
                $content = $template->email_template_stop ??
                    $defaultPlaceHolder->where('type', "end_treatment_".$shortCode)->first()->message ?? '';
                break;
            case 'red':
                $subject = $template->email_template_title_blank ??
                    $defaultPlaceHolder->where('type', "blank_treatment_title_".$shortCode)->first()->message ?? '';
                $content = $template->email_template_blank ??
                    $defaultPlaceHolder->where('type', "blank_treatment_".$shortCode)->first()->message ?? '';
                break;
            case 'green':
                $subject = $template->email_template_title_green ??
                    $defaultPlaceHolder->where('type', "green_treatment_title_" . $shortCode)->first()->message ?? '';
                $content = $template->email_template_green ??
                    $defaultPlaceHolder->where('type', "green_treatment_" . $shortCode)->first()->message ?? '';
                break;
            case 'resend':
                $subject = $template->email_template_title_resend ??
                    $defaultPlaceHolder->where('type', "email_template_title_resend")
                        ->where('language', $shortCode)->first()->message ?? '';
                $content = $template->email_template_resend ??
                    $defaultPlaceHolder->where('type', "email_template_resend")
                        ->where('language', $shortCode)->first()->message ?? '';
                break;
            case 'footer':
                $content = $template->email_template_footer ??
                    $defaultPlaceHolder->where('type', "global_footer_".$shortCode)->first()->message ?? '';
                break;
        }

        return [
            'content' => $content,
            'subject' => isset($subject) ? $subject : null
        ];
    }

    public function updateCronOption($cron_setup_id)
    {
        $cron_option = CronSetup::with('cronsetupoption')->findOrFail($cron_setup_id);
        $cronUser = User::where('id', $cron_option->selecetd_user)->first();

        $data = [
            'due_power' => (request()->due_power == null) ? 0 : 1,
            'pdf_export' => (request()->pdf_export == null) ? 0 : 1,
            'customer_link' => (request()->customer_link == null) ? 0 : 1,
            'start_email' => (request()->start_email == null) ? 0 : 1,
            'end_email' => (request()->end_email == null) ? 0 : 1,
            'end_email_therapist' => (request()->end_email_therapist == null) ? 0 : 1,
            'start_email_duepower' => (request()->start_email_duepower == null) ? 0 : 1,
            'end_email_duepower' => (request()->end_email_duepower == null) ? 0 : 1,
            'calculation_day' => (request()->calculation_day == null) ? 0 : 1,
            'any_time_cron' => (request()->any_time_cron == null) ? 0 : 1,
            'ra_status' => (request()->ra_status == null) ? 0 : 1,
            'pdf_status' => (request()->pdf_status == null) ? 0 : 1,
        ];

        $cron_option->cronsetupoption()->update($data);

        activityLogTrack(2, "options at remote treatment for {$cronUser->first_name} {$cronUser->last_name}", "optionen bei Fernbehandlung für {$cronUser->first_name} {$cronUser->last_name}", 'Remote Treatment', 'remote_treatment'); #AL
        return true;
    }
    public function cleanTrush($userId)
    {
        $cronIds = CronSetup::select('id')->where('user_id', $userId)->where('is_deleted', 1)->pluck('id');
        if (count($cronIds) > 0) {
            DB::table('cronsetup_options')->whereIn('cron_setup_id', $cronIds)->delete();
            DB::table('cron_setup_submenus')->whereIn('cron_setup_id', $cronIds)->delete();
            DB::table('cronsetup_times')->whereIn('cronsetup_id', $cronIds)->delete();
            DB::table('cronsetup_analyses')->whereIn('cron_setup_id', $cronIds)->delete();
            DB::table('cron_setups')->whereIn('id', $cronIds)->delete();
            return true;
        }
        return false;
    }

    public function getIPAddress() {
        return $_SERVER['REMOTE_ADDR'] ?? ($_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['HTTP_CLIENT_IP']);
    }

    public function getPDFDatas($crondetails){
        $cron_analyses = CronsetupAnalysis::where("cron_setup_id", $crondetails->cronsetup_id)
            ->where("start_time", $crondetails->start_time)
            ->orderBy('id', 'asc')
            ->get();
        setServerLocal(Language::find($crondetails->cronsetup->selecteduser->language_id,['short_code'])->short_code);

        $langId = app()->getLocale()??'de';

        $pro_submenus = DB::table('cron_setup_submenus')->select(['submenu_id','submenu_type'])->where('cron_setup_id', $crondetails->cronsetup_id)->get();

        $sysSubIds = $pro_submenus->where('submenu_type',false)->pluck('submenu_id');
        $ownSubIds = $pro_submenus->where('submenu_type',true)->pluck('submenu_id');

        $system_submenus = $own_submenus = collect();

        if (!empty($sysSubIds)) {#System submenu details
            $selected_columns = ($langId == 'de') ? ['id','product_id','menu_name'] : ['id','product_id',$langId.'_menu_name as menu_name'];
            $system_submenus = Submenu::with(['product:id','product.product_setting:product_id,red_min,red_max,orange_min,orange_max,green_min,green_max,custom_min,custom_max'])
                ->whereIn("id", $sysSubIds)
                ->get($selected_columns);
        }
        if (!empty($ownSubIds)) {#Own submenu details
            $own_submenus = UserSubMenu::with(['menu_setting'])->whereIn("id", $ownSubIds)->get(['id','menu_id','name']);
        }

        $systemAnaQuery = DB::table('analyse_pools')#Get system analyses IDs user submenu
            ->join('pool_submenus', "pool_submenus.pool_id", "analyse_pools.pool_id")
            ->whereIn('pool_submenus.submenu_id', $sysSubIds)
            ->get(['analyse_id','submenu_id']);

        $ownAnaQuery = DB::table('analyse_user_submenus')#Get Own analyses IDs user submenu
            ->whereIn('user_submenu_id', $ownSubIds)
            ->get(['analyse_id','user_submenu_id']);

        $causes = array();

        $sys_ana_ids = $cron_analyses->where('status',false)->where('type_status',0)->pluck('analyse_id')->unique();#system analyses Ids from treatment
        $own_ana_ids = $cron_analyses->where('status',true)->where('type_status',0)->pluck('analyse_id')->unique();#Own analyses Ids from treatment
        #All Causes
        $causes = $cron_analyses->where('causes','>',0)->pluck('causes')->merge($cron_analyses->where('medium','>',0)->pluck('medium'))->merge($cron_analyses->where('tipp','>',0)->pluck('tipp'))->unique();

        if (!empty($sys_ana_ids)) {
            $all_sys_analyses = collect();#All system analyses from treatment;
            if($sys_ana_ids->count() > 200){
                $sys_ana_ids->chunk(200)->each(function ($chunk) use(&$all_sys_analyses){
                    Analyse::whereIn('id', $chunk)->get()->map(function($cause) use(&$all_sys_analyses){
                        $all_sys_analyses->push($cause);
                    });
                });
            }else{
                $all_sys_analyses = Analyse::whereIn('id', $sys_ana_ids)->get();
            }
        }
        if (!empty($own_ana_ids)) {
            $all_own_analyses = DB::table('analyses')->whereIn('id', $own_ana_ids)->get();#All own analyses from treatment
        }
        if (!empty($causes)) {
            $all_causes = collect();
            if($causes->count() > 200){
                $causes->chunk(200)->each(function ($chunk) use(&$all_causes){
                    Causes::select(['id','title','description'])->whereIn('id', $chunk)->get()->map(function($cause) use(&$all_causes){
                        $all_causes->push($cause);
                    });
                });
            }else{
                $all_causes = Causes::select(['id','title','description'])->whereIn('id', $causes)->get();
            }
        }

        // Initialize collections to prevent undefined variable errors
        $all_sys_analyses = collect();
        $all_own_analyses = collect();
        $all_causes = collect();
        
        if (!empty($sys_ana_ids)) {
            if($sys_ana_ids->count() > 200){
                $sys_ana_ids->chunk(200)->each(function ($chunk) use(&$all_sys_analyses){
                    Analyse::whereIn('id', $chunk)->get()->map(function($analyses) use(&$all_sys_analyses){
                        $all_sys_analyses->push($analyses);
                    });
                });
            }else{
                $all_sys_analyses = Analyse::whereIn('id', $sys_ana_ids)->get();
            }
        }
        
        if (!empty($own_ana_ids)) {
            $all_own_analyses = DB::table('analyses')->whereIn('id', $own_ana_ids)->get();
        }
        
        if (!empty($causes)) {
            if($causes->count() > 200){
                $causes->chunk(200)->each(function ($chunk) use(&$all_causes){
                    Causes::select(['id','title','description'])->whereIn('id', $chunk)->get()->map(function($cause) use(&$all_causes){
                        $all_causes->push($cause);
                    });
                });
            }else{
                $all_causes = Causes::select(['id','title','description'])->whereIn('id', $causes)->get();
            }
        }

        // Process analyses in database order - group by analysis_id to prevent duplicates
        $allSubmenus = collect();
        $topicnames = collect(); // Topics now processed inline, keep empty
        $processedAnalyses = []; // Track processed analysis IDs to prevent duplicates
        
        foreach ($cron_analyses as $analyse) {
            // Handle Topics (type_status == 6)
            if ($analyse->type_status == 6) {
                if ($analyse->male < 31) $male_img = "man-red.png";
                else $male_img = "man-gray.png";

                if ($analyse->heart >= 50) $heart_img = "heart-red.png";
                else $heart_img = "heart-gray.png";
                
                // Add topic to main analyses collection
                $allSubmenus->push(collect([
                    'id' => null,
                    'submenu_name' => 'Topic',
                    'analyses' => collect([[
                        'id' => $analyse->id,
                        'name' => $analyse->topic,
                        'cal' => $analyse->calculation,
                        'color' => $analyse->color,
                        'male_val' => $analyse->male,
                        'male' => $male_img,
                        'heart_val' => $analyse->heart,
                        'heart' => $heart_img,
                        'type' => $analyse->type_status,
                        'others' => null
                    ]]),
                    'type' => false
                ]));
                continue;
            }
            
            // Handle regular analyses (type_status == 0) - only process once per analysis_id
            if ($analyse->type_status == 0 && !isset($processedAnalyses[$analyse->analyse_id])) {
                if ($analyse->status == false) { // System analysis
                    $triggerAnalyse = $all_sys_analyses->where('id', $analyse->analyse_id)->first();
                } else { // Own analysis  
                    $triggerAnalyse = $all_own_analyses->where('id', $analyse->analyse_id)->first();
                }
                
                if ($triggerAnalyse) {
                    // Get all related records for this analysis (causes, medium, tipp, etc.)
                    $relatedRecords = $cron_analyses->where('analyse_id', $analyse->analyse_id);
                    $this->createAnalysisForPDF($analyse, $triggerAnalyse, $all_causes, $allSubmenus, $relatedRecords);
                    $processedAnalyses[$analyse->analyse_id] = true;
                }
                continue;
            }
            
            // Handle standalone causes (analyse_id = 0)
            if (in_array($analyse->type_status, [1,2,3,4,5]) && $analyse->analyse_id == 0) {
                $triggerCause = null;
                if ($analyse->type_status == 1 || $analyse->type_status == 4 || $analyse->type_status == 5) {
                    $triggerCause = $all_causes->where('id', $analyse->causes)->first();
                } elseif ($analyse->type_status == 2) {
                    $triggerCause = $all_causes->where('id', $analyse->medium)->first();
                } elseif ($analyse->type_status == 3) {
                    $triggerCause = $all_causes->where('id', $analyse->tipp)->first();
                }
                
                if ($triggerCause) {
                    $this->createCauseForPDF($analyse, $triggerCause, $allSubmenus);
                }
            }
        }
        
        return ['allSubmenus' => $allSubmenus, 'allTopics' => $topicnames];
    }

    private function createDatasCollection($analyse, &$_analyse, $all_causes,$triggerAnalyse){
        $causes = $medium = $tipp = null;
        if(Carbon::parse($analyse->created_at)->lessThan(Carbon::parse('2023-5-16'))){
            if($analyse->causes) $causes = $all_causes->where('id',$analyse->causes)->first();
            if($analyse->medium) $medium = $all_causes->where('id',$analyse->medium)->first();
            if($analyse->tipp) $tipp = $all_causes->where('id',$analyse->tipp)->first();
        }

        if ($analyse->male < 31) $male_img = "man-red.png";
        else $male_img = "man-gray.png";

        if ($analyse->heart >= 50) $heart_img = "heart-red.png";
        else $heart_img = "heart-gray.png";
        // if(!$_analyse->contains('id',$triggerAnalyse->id)) {
            $_analyse->push([
                'id'        => $triggerAnalyse->id,
                'name'      => $triggerAnalyse->name ?? $triggerAnalyse->title,
                'cal'       => $analyse->calculation,
                'color'     => $analyse->color,
                'male_val'  => $analyse->male,
                'male'      => $male_img,
                'heart_val' => $analyse->heart,
                'heart'     => $heart_img,
                'desc'      => $triggerAnalyse->description,
                'body'      => isset($triggerAnalyse->body_desc) ? $triggerAnalyse->body_desc : null,
                'mental'    => isset($triggerAnalyse->mental_desc) ? $triggerAnalyse->mental_desc : null,
                'cau'       => ($causes) ? $causes : null,
                'mid'       => ($medium) ? $medium : null,
                'tip'       => ($tipp) ? $tipp : null,
                'ein'       => null,
                'foc'       => null,
                'type'      => $analyse->type_status,
                'others'    => $analyse->others
            ]);
        // }

        // if($analyse->type_status == 2) dd($_analyse,"Test",$causes,$medium,$tipp);#test
        return $_analyse;
    }
    
    private function createAnalysisForPDF($analyse, $triggerAnalyse, $all_causes, &$allSubmenus, $relatedRecords = null)
    {
        if ($analyse->male < 31) $male_img = "man-red.png";
        else $male_img = "man-gray.png";

        if ($analyse->heart >= 50) $heart_img = "heart-red.png";
        else $heart_img = "heart-gray.png";
        
        // Find related causes, medium, tipp, etc. for this analysis
        $cau = $mid = $tip = $ein = $foc = null;
        
        if ($relatedRecords) {
            foreach ($relatedRecords as $related) {
                if ($related->type_status == 1 && $related->causes > 0) {
                    $cau = $all_causes->where('id', $related->causes)->first();
                } elseif ($related->type_status == 2 && $related->medium > 0) {
                    $mid = $all_causes->where('id', $related->medium)->first();
                } elseif ($related->type_status == 3 && $related->tipp > 0) {
                    $tip = $all_causes->where('id', $related->tipp)->first();
                } elseif ($related->type_status == 4 && $related->causes > 0) {
                    $ein = $all_causes->where('id', $related->causes)->first();
                } elseif ($related->type_status == 5 && $related->causes > 0) {
                    $foc = $all_causes->where('id', $related->causes)->first();
                }
            }
        }
        
        $analysisData = [
            'id' => $triggerAnalyse->id,
            'name' => $triggerAnalyse->name,
            'cal' => $analyse->calculation,
            'color' => $analyse->color,
            'male_val' => $analyse->male,
            'male' => $male_img,
            'heart_val' => $analyse->heart,
            'heart' => $heart_img,
            'desc' => $triggerAnalyse->description,
            'body' => $triggerAnalyse->body_desc ?? null,
            'mental' => $triggerAnalyse->mental_desc ?? null,
            'cau' => $cau,
            'mid' => $mid,
            'tip' => $tip,
            'ein' => $ein,
            'foc' => $foc,
            'type' => $analyse->type_status,
            'others' => $analyse->others
        ];
        
        $allSubmenus->push(collect([
            'id' => $analyse->analyse_id,
            'submenu_name' => $triggerAnalyse->name,
            'analyses' => collect([$analysisData]),
            'type' => $analyse->status
        ]));
    }
    
    private function createCauseForPDF($analyse, $triggerCause, &$allSubmenus)
    {
        if ($analyse->male < 31) $male_img = "man-red.png";
        else $male_img = "man-gray.png";

        if ($analyse->heart >= 50) $heart_img = "heart-red.png";
        else $heart_img = "heart-gray.png";
        
        $causeData = [
            'id' => $triggerCause->id,
            'name' => $triggerCause->title,
            'cal' => $analyse->calculation,
            'color' => $analyse->color,
            'male_val' => $analyse->male,
            'male' => $male_img,
            'heart_val' => $analyse->heart,
            'heart' => $heart_img,
            'desc' => $triggerCause->description,
            'body' => null,
            'mental' => null,
            'cau' => null,
            'mid' => null,
            'tip' => null,
            'ein' => null,
            'foc' => null,
            'type' => $analyse->type_status,
            'others' => $analyse->others
        ];
        
        $allSubmenus->push(collect([
            'id' => $triggerCause->id,
            'submenu_name' => $triggerCause->title,
            'analyses' => collect([$causeData]),
            'type' => false
        ]));
    }

    public function cronShowAnalysis($crondetails)
    {
        $cron_analyses = CronsetupAnalysis::where("cron_setup_id", $crondetails->cronsetup_id)
            ->where("start_time", $crondetails->start_time)
            ->orderBy('id', 'asc')
            ->get();

        $langId = app()->getLocale()??'de';
        // dd($langId,DB::table('languages')->where('id',$crondetails->cronsetup->selecteduser->language_id)->first());

        $pro_submenus = DB::table('cron_setup_submenus')->select(['submenu_id','submenu_type'])->where('cron_setup_id', $crondetails->cronsetup_id)->get();

        $sysSubIds = $pro_submenus->where('submenu_type',false)->pluck('submenu_id');
        $ownSubIds = $pro_submenus->where('submenu_type',true)->pluck('submenu_id');

        $system_submenus = $own_submenus = collect();

        if (!empty($sysSubIds)) {#System submenu details
            $selected_columns = ($langId == 'de') ? ['id','product_id','menu_name'] : ['id','product_id',$langId.'_menu_name as menu_name'];
            $system_submenus = Submenu::with(['product:id','product.product_setting:product_id,red_min,red_max,orange_min,orange_max,green_min,green_max,custom_min,custom_max'])
                ->whereIn("id", $sysSubIds)
                ->get($selected_columns);
        }

        if (!empty($ownSubIds)) {#Own submenu details
            $own_submenus = UserSubMenu::with(['menu_setting'])->whereIn("id", $ownSubIds)->get(['id','menu_id','name']);
        }

        $systemAnaQuery = DB::table('analyse_pools')#Get system analyses IDs user submenu
            ->join('pool_submenus', "pool_submenus.pool_id", "analyse_pools.pool_id")
            ->whereIn('pool_submenus.submenu_id', $sysSubIds)
            ->get(['analyse_id','submenu_id']);

        $ownAnaQuery = DB::table('analyse_user_submenus')#Get Own analyses IDs user submenu
            ->whereIn('user_submenu_id', $ownSubIds)
            ->get(['analyse_id','user_submenu_id']);

        $causes = array();

        $sys_ana_ids = $cron_analyses->where('status',false)->where('type_status',0)->pluck('analyse_id');#system analyses Ids from treatment
        $own_ana_ids = $cron_analyses->where('status',true)->where('type_status',0)->pluck('analyse_id');#Own analyses Ids from treatment

        #All Causes
        $causes = $cron_analyses->where('causes','>',0)->pluck('causes')->merge($cron_analyses->where('medium','>',0)->pluck('medium'))->merge($cron_analyses->where('tipp','>',0)->pluck('tipp'));

        $all_sys_analyses = collect(); // Initialize as empty collection
        if (!empty($sys_ana_ids)) {
            if($sys_ana_ids->count() > 200){
                $sys_ana_ids->chunk(200)->each(function ($chunk) use(&$all_sys_analyses){
                    Analyse::whereIn('id', $chunk)->get()->map(function($analyses) use(&$all_sys_analyses){
                        $all_sys_analyses->push($analyses);
                    });
                });
            }else{
                $all_sys_analyses = Analyse::whereIn('id', $sys_ana_ids)->get();
            }
        }

        $all_own_analyses = collect(); // Initialize as empty collection
        if (!empty($own_ana_ids)) {
            $all_own_analyses = DB::table('analyses')->whereIn('id', $own_ana_ids)->get();#All own analyses from treatment
        }

        $all_causes = collect(); // Initialize as empty collection
        if (!empty($causes)) {
            if($causes->count() > 200){
                $causes->chunk(200)->each(function ($chunk) use(&$all_causes){
                    Causes::select(['id','title','description'])->whereIn('id', $chunk)->get()->map(function($cause) use(&$all_causes){
                        $all_causes->push($cause);
                    });
                });
            }else{
                $all_causes = Causes::select(['id','title','description'])->whereIn('id', $causes)->get();
            }
        }

        #Topic collection - topics are now handled in main analyses list
        $topicnames = collect();
        
        // Process all analyses in database order without submenu grouping
        $allAnalyses = collect();
        
        // Process each analysis in database order
        $cron_analyses->each(function($analyse) use(&$allAnalyses, $all_sys_analyses, $all_own_analyses, $all_causes){
            if($analyse->type_status == 6) {
                // Handle topics - add them to main analyses list to maintain order
                if ($analyse->male < 31) $male_img = "man-red.png";
                else $male_img = "man-gray.png";

                if ($analyse->heart >= 50) $heart_img = "heart-red.png";
                else $heart_img = "heart-gray.png";

                $allAnalyses->push([
                    'id' => 1,
                    'ana_name' => $analyse->topic,
                    'calculation' => $analyse->calculation,
                    'ana_val' => $analyse->time,
                    'cau_title' => null,
                    'cau_price' => null,
                    'mid_title' => null,
                    'mid_price' => null,
                    'tip_title' => null,
                    'tip_price' => null,
                    'ein_title' => null,
                    'ein_price' => null,
                    'foc_title' => null,
                    'foc_price' => null,
                    'type' => $analyse->type_status,
                    'others' => null,
                    'male_val' => $analyse->male,
                    'male' => $male_img,
                    'heart_val' => $analyse->heart,
                    'heart' => $heart_img
                ]);
            } elseif($analyse->type_status == 0) {
                // Handle analyses (both system and own)
                $triggerAnalyse = null;
                
                if($analyse->status == false) {
                    // System analysis
                    $triggerAnalyse = $all_sys_analyses->where('id',$analyse->analyse_id)->first();
                } elseif($analyse->status == true) {
                    // Own analysis  
                    $triggerAnalyse = $all_own_analyses->where('id',$analyse->analyse_id)->first();
                }
                
                if($triggerAnalyse) {
                    $allAnalyses->push([
                        'id' => $triggerAnalyse->id,
                        'ana_name' => $triggerAnalyse->name,
                        'calculation' => $analyse->calculation,
                        'ana_val' => $analyse->time,
                        'cau_title' => null,
                        'cau_price' => null,
                        'mid_title' => null,
                        'mid_price' => null,
                        'tip_title' => null,
                        'tip_price' => null,
                        'ein_title' => null,
                        'ein_price' => null,
                        'foc_title' => null,
                        'foc_price' => null,
                        'type' => $analyse->type_status,
                        'others' => $analyse->others ?? []
                    ]);
                }
            } elseif(in_array($analyse->type_status, [1,2,3,4,5])) {
                // Handle causes
                $triggerCause = null;
                $ein = $focus = $cau = $mid = $tip = null;
                
                if($analyse->type_status == 1 && $analyse->causes > 0) {
                    $triggerCause = $all_causes->where('id', $analyse->causes)->first();
                    $cau = $triggerCause ? $triggerCause->title : null;
                } elseif($analyse->type_status == 2 && $analyse->medium > 0) {
                    $triggerCause = $all_causes->where('id', $analyse->medium)->first();
                    $mid = $triggerCause ? $triggerCause->title : null;
                } elseif($analyse->type_status == 3 && $analyse->tipp > 0) {
                    $triggerCause = $all_causes->where('id', $analyse->tipp)->first();
                    $tip = $triggerCause ? $triggerCause->title : null;
                } elseif($analyse->type_status == 4 && $analyse->causes > 0) {
                    $triggerCause = $all_causes->where('id', $analyse->causes)->first();
                    $ein = $triggerCause ? $triggerCause->title : null;
                } elseif($analyse->type_status == 5 && $analyse->causes > 0) {
                    $triggerCause = $all_causes->where('id', $analyse->causes)->first();
                    $focus = $triggerCause ? $triggerCause->title : null;
                }
                
                if($triggerCause) {
                    $allAnalyses->push([
                        'id' => 0,
                        'ana_name' => $triggerCause->title,
                        'calculation' => $analyse->calculation,
                        'ana_val' => $analyse->time,
                        'cau_title' => $cau,
                        'cau_price' => $cau ? $analyse->time : null,
                        'mid_title' => $mid,
                        'mid_price' => $mid ? $analyse->time : null,
                        'tip_title' => $tip,
                        'tip_price' => $tip ? $analyse->time : null,
                        'ein_title' => $ein,
                        'ein_price' => $ein ? $analyse->time : null,
                        'foc_title' => $focus,
                        'foc_price' => $focus ? $analyse->time : null,
                        'type' => $analyse->type_status,
                        'others' => $analyse->others ?? []
                    ]);
                }
            }
        });

        // Create single submenu with all analyses in correct order
        $allSubmenus = collect();
        if($allAnalyses->count()) {
            $allSubmenus->push(collect([
                'id' => null,
                'submenu_name' => '',
                'analyses' => $allAnalyses,
                'type' => false
            ]));
        }

        return ['allSubmenus' => $allSubmenus, 'allTopics' => $topicnames];
    }

    private function createShowAnalysesCollection(&$_analyse, $triggerAnalyse, $analyse, $all_causes){
        $causes = $medium = $tipp = null;
        if(Carbon::parse($analyse->created_at)->lessThan(Carbon::parse('2023-5-16'))){
            if($analyse->causes) $causes = $all_causes->where('id',$analyse->causes)->first()->title;
            if($analyse->medium) $medium = $all_causes->where('id',$analyse->medium)->first()->title;
            if($analyse->tipp) $tipp = $all_causes->where('id',$analyse->tipp)->first()->title;
        }else{
            $causes = ($analyse->type_status == 1) ? $triggerAnalyse->name:null;
            $analyse->cau_price = ($analyse->type_status == 1) ? $analyse->time:null;
            $medium = ($analyse->type_status == 2) ? $triggerAnalyse->name:null;
            $analyse->mid_price = ($analyse->type_status == 2) ? $analyse->time:null;
            $tipp = ($analyse->type_status == 3) ? $triggerAnalyse->name:null;
            $analyse->tip_price = ($analyse->type_status == 3) ? $analyse->time:null;
        }

        $_analyse->push([
            'id'        => $triggerAnalyse->id,
            'ana_name'  => $triggerAnalyse->name,
            'calculation' => $analyse->calculation,
            'ana_val'     => $analyse->time,
            'cau_title' => ($causes) ? $causes : null,
            'cau_price' => ($causes) ? $analyse->cau_price : null,
            'mid_title' => ($medium) ? $medium : null,
            'mid_price' => ($medium) ? $analyse->mid_price : null,
            'tip_title' => ($tipp) ? $tipp : null,
            'tip_price' => ($tipp) ? $analyse->tip_price : null,
            'ein_title' => ($analyse->type_status == 4) ? $triggerAnalyse->name:null,
            'ein_price' => ($analyse->type_status == 4) ? $analyse->time:null,
            'foc_title' => ($analyse->type_status == 5) ? $triggerAnalyse->name:null,
            'foc_price' => ($analyse->type_status == 5) ? $analyse->time:null,
            'type'      => $analyse->type_status,
            'others'    => $analyse->others
        ]);
        return $_analyse;
    }


    function cronTreatment($id, $timeId, $uniqueId){
        $data = [];

        $crondetails = CronsetupTimes::select("cron_setups.id","cronsetup_times.id as timeid","cron_setups.status","cron_setups.open_status","selected_user","unique_id","start_time","end_time","pdf_export","red_day","causes","medium","tipp","any_time_cron","length","ra_status","pdf_status")
        ->join("cron_setups", "cron_setups.id", "=", "cronsetup_times.cronsetup_id")
        ->join("cronsetup_options", "cronsetup_options.cron_setup_id", "=", "cronsetup_times.cronsetup_id")
        ->where("cronsetup_times.id", $timeId)
            ->orderBy('id', 'asc')
        ->first();

        $data['userDetails'] = User::with('useroption:user_id,sound')->find($crondetails->selected_user); # treatable user
        #language show as user language
        $locale = (auth()->check()) ? App::getLocale() : Language::find($data["userDetails"]->language_id,['short_code'])->short_code; #language
        $data['locale'] = $locale;
        setServerLocal($locale);

        if ($crondetails->unique_id !== $uniqueId) {
            $data['status'] = false;// invalid url
            return $data;
        }
        elseif ($crondetails->red_day === true){
            $data['status'] = 1; //red-day no treatment
            return $data;
        }

        $timezone = "Europe/Vienna"; #"Asia/Dhaka";

        if(request()->segment(2) == 'open_treat'){
            $lastTreatment = DB::table("cronsetup_times")->select(['id','end_time'])->where("cronsetup_id", $id)->latest('id')->first();

            $status = $crondetails->status;
            if($this->getTimeDifference($lastTreatment->end_time, $timezone) <= 0) $status = 0;
            if(!$crondetails->open_status) DB::table("cron_setups")->where("id", $id)->update(["status" => $status, "open_status" => 1]);
        }

        $endDateTime = Carbon::parse($crondetails->end_time);

        if ($this->getTimeDifference($crondetails->start_time, $timezone) > 0 && !$crondetails->any_time_cron) {
            $data['startTime'] = $crondetails->start_time;
            $data['endTime'] = $crondetails->end_time;
            $data['zone'] = $timezone;
            $data['status'] = 2; # Start time not now ,, Also cron anytime view false
            return $data;
        }elseif(!$endDateTime->gte(Carbon::now()) && !$crondetails->any_time_cron){
            $data['status'] = 3; # Treatment time exit
            return $data;
        }elseif($crondetails->any_time_cron && !$endDateTime->gte(Carbon::today())){
            $data['status'] = 3; # Treatment time exit
            return $data;
        }

        $analyses_table_name = ($locale == '' || $locale == 'de') ? 'analyses' : $locale.'_analyses';
        $causes_table_name = ($locale == '' || $locale == 'de') ? 'causes' : $locale.'_causes';

        $getAllTreatAnalyses = CronsetupAnalysis::where('cron_setup_id', $crondetails->id ?? $id)
        ->select('id','analyse_id','causes','medium','tipp','calculation','cau_price','mid_price','tipp_price','male','heart','time','color','type_status','topic')
        ->where('start_time', $crondetails->start_time)
        ->orderBy('id', 'asc')
        ->get(); # All treatment analyses

        if(!$getAllTreatAnalyses->toArray()){
            $data['status'] = 1; //red-day no treatment
            return $data;
        }
        $data['analyses'] = DB::table($analyses_table_name)
        ->select('id','name','description','desc_image')
        ->whereIn('id', $getAllTreatAnalyses->pluck('analyse_id')->unique())
        ->get(); # Get Analyses


        if (!empty($anaIds)) {
            $data['bodyImages']   = BodyImage::whereIn('analyse_id', $getAllTreatAnalyses->pluck('analyse_id')->unique())->select('analyse_id', 'image')->get() ?? collect();
            $data['mentalImages'] = MentalImage::whereIn('analyse_id', $getAllTreatAnalyses->pluck('analyse_id')->unique())->select('analyse_id', 'image')->get() ?? collect();
        }
        $causesIds = $getAllTreatAnalyses->whereIn('type_status',[1,4,5])->pluck('causes')
        ->merge($getAllTreatAnalyses->where('type_status',2)->pluck('medium'))
        ->merge($getAllTreatAnalyses->where('type_status',3)->pluck('tipp'))
        ->unique();

        $data['causes'] = DB::table($causes_table_name)
        ->whereIn("id", $causesIds)
        ->get(['id', 'title as name', 'description']); # Get All Causes

        $data['topics'] = $getAllTreatAnalyses->where('type_status',6); # Get Topics

        $data['crondetails'] = $crondetails;

        $getPlayableDatas = collect();

        $getAllTreatAnalyses->map(function($treat) use($data, &$getPlayableDatas){
            $types = [
                '0'  => $data['analyses']->where('id',$treat->analyse_id)->first(),
                '1'  => $data['causes']->where('id',$treat->causes)->first(),
                '2'  => $data['causes']->where('id',$treat->medium)->first(),
                '3'  => $data['causes']->where('id',$treat->tipp)->first(),
                '4'  => $data['causes']->where('id',$treat->causes)->first(),
                '5'  => $data['causes']->where('id',$treat->causes)->first(),
                '6'  => "Note"
            ];
            $content = $types[$treat->type_status];
            $play = clone $treat;
            if($treat->type_status == 6){
                $play->name = $play->topic;
                $play->description = null;
            }else{
                $play->name = $content->name;
                $play->description = $content->description;
            }
            $getPlayableDatas->push($this->create_cart($play,$data['userDetails']));
        });

        $data['cartRecords'] = $getPlayableDatas;

        unset($data['causes'],$data['analyses'],$data['topics']);
        if ($crondetails->length == 0) {

            $data['start_time'] = strtotime($crondetails->start_time);
            $data['end_time'] = strtotime($crondetails->end_time);
            $data['status'] = 4; # Treatment on Duer-Power player
            return $data;
        } else {
            $data['status'] = 5; # Treatment on single player
            return $data;
        }
    }

    function cronTreatmentAnyTime($id, $timeId, $uniqueId){
        $data = [];

        $crondetails = CronsetupTimes::select("cron_setups.id","cronsetup_times.id as timeid","cron_setups.status","cron_setups.open_status","selected_user","unique_id","start_time","end_time","pdf_export","red_day","causes","medium","tipp","any_time_cron","length","ra_status","pdf_status")
        ->join("cron_setups", "cron_setups.id", "=", "cronsetup_times.cronsetup_id")
        ->join("cronsetup_options", "cronsetup_options.cron_setup_id", "=", "cronsetup_times.cronsetup_id")
        ->where("cronsetup_times.id", $timeId)
        ->first();

        $data['userDetails'] = User::with('useroption:user_id,sound')->find($crondetails->selected_user); # treatable user
        #language show as user language
        $locale = (auth()->check()) ? App::getLocale() : Language::find($data["userDetails"]->language_id,['short_code'])->short_code; #language
        $data['locale'] = $locale;
        setServerLocal($locale);

        $analyses_table_name = ($locale == '' || $locale == 'de') ? 'analyses' : $locale.'_analyses';
        $causes_table_name = ($locale == '' || $locale == 'de') ? 'causes' : $locale.'_causes';

        $getAllTreatAnalyses = CronsetupAnalysis::where('cron_setup_id', $crondetails->id ?? $id)
        ->select('id','analyse_id','causes','medium','tipp','calculation','cau_price','mid_price','tipp_price','male','heart','time','color','type_status','topic')
        ->where('start_time', $crondetails->start_time)
        ->get(); # All treatment analyses

        if(!$getAllTreatAnalyses->toArray()){
            $data['status'] = 1; //red-day no treatment
            return $data;
        }
        $data['analyses'] = DB::table($analyses_table_name)
        ->select('id','name','description','desc_image')
        ->whereIn('id', $getAllTreatAnalyses->pluck('analyse_id')->unique())
        ->get(); # Get Analyses
        if (!empty($anaIds)) {
            $data['bodyImages']   = BodyImage::whereIn('analyse_id', $getAllTreatAnalyses->pluck('analyse_id')->unique())->select('analyse_id', 'image')->get() ?? collect();
            $data['mentalImages'] = MentalImage::whereIn('analyse_id', $getAllTreatAnalyses->pluck('analyse_id')->unique())->select('analyse_id', 'image')->get() ?? collect();
        }
        $causesIds = $getAllTreatAnalyses->whereIn('type_status',[1,4,5])->pluck('causes')
        ->merge($getAllTreatAnalyses->where('type_status',2)->pluck('medium'))
        ->merge($getAllTreatAnalyses->where('type_status',3)->pluck('tipp'))
        ->unique();

        $data['causes'] = DB::table($causes_table_name)
        ->whereIn("id", $causesIds)
        ->get(['id', 'title as name', 'description']); # Get All Causes

        $data['topics'] = $getAllTreatAnalyses->where('type_status',6); # Get Topics

        $data['crondetails'] = $crondetails;

        $getPlayableDatas = collect();

        $getAllTreatAnalyses->map(function($treat) use($data, &$getPlayableDatas){
            $types = [
                '0'  => $data['analyses']->where('id',$treat->analyse_id)->first(),
                '1'  => $data['causes']->where('id',$treat->causes)->first(),
                '2'  => $data['causes']->where('id',$treat->medium)->first(),
                '3'  => $data['causes']->where('id',$treat->tipp)->first(),
                '4'  => $data['causes']->where('id',$treat->causes)->first(),
                '5'  => $data['causes']->where('id',$treat->causes)->first(),
                '6'  => "Note"
            ];
            $content = $types[$treat->type_status];
            $play = clone $treat;
            if($treat->type_status == 6){
                $play->name = $play->topic;
                $play->description = null;
            }else{
                $play->name = $content->name;
                $play->description = $content->description;
            }
            $getPlayableDatas->push($this->create_cart($play,$data['userDetails']));
        });

        $data['cartRecords'] = $getPlayableDatas;

        unset($data['causes'],$data['analyses'],$data['topics']);
        if ($crondetails->length == 0) {

            $data['start_time'] = strtotime($crondetails->start_time);
            $data['end_time'] = strtotime($crondetails->end_time);
            $data['status'] = 4; # Treatment on Duer-Power player
            return $data;
        } else {
            $data['status'] = 5; # Treatment on single player
            return $data;
        }
    }

    private function getTimeDifference($cronTime, $timezone)
    {
        $dateNow = Carbon::now($timezone);
        $cronTime = Carbon::createFromTimestamp(strtotime($cronTime), $timezone);

        return $dateNow->diffInSeconds($cronTime, false);
    }

    private function create_cart($data, $userdetails, $instance_name = 'CronEMailCart')
    {
        $gsSetting = Cache::rememberForever('_globaSetting', function () {
            return DB::table('global_settings')->where('gs_type', 'other')->first();
        });
        $typeStatusMapping = [
            0 => 'Analysis',
            1 => 'Causes',
            2 => 'Medium',
            3 => 'Tipp',
            4 => 'Einfluss',
            5 => 'Fokus',
            6 => 'Topic',
        ];
        $randPrice  = rand($gsSetting->gs_min_price, $gsSetting->gs_max_price);
        $cart_data = array(
            'userID' => $userdetails->id,
            'user_id' => $userdetails->id,
            'analysisID' => $data->analyse_id,
            'analysisName' => $data->name,
            'submenu_id' => 0,
            'productID' => 0,
            'calculation' => $data->calculation,
            'male' => $data->male,
            'heart' => $data->heart,
            'price' => (int)($data->time == 0) ? $randPrice : $data->time,
            'causes_id' => $data->causes,
            'medium_id' => $data->medium,
            'tipp_id' => $data->tipp,
            'color' => $data->color,
            'type' => $typeStatusMapping[$data->type_status],
            'optimized' => false,
            'percentage' => $data->calculation,
            'op_status' => true,
            'frequency'   => $this->getCalculationFrequency($data->name),
            'time' => gmdate('i:s', $data->time == 0 ? $randPrice : $data->time)
        );

        if ($instance_name == 'wishlist') {
            return Cart::instance($instance_name)->add(['id' => $userdetails->id, 'name' => $typeStatusMapping[$data->type_status]??$data->name, 'qty' =>  1, 'price' =>  1, 'weight' => 1, 'options' => $cart_data]);
        } else
            return Cart::instance($instance_name)->add(['id' => $userdetails->id . 'RA', 'name' => $typeStatusMapping[$data->type_status]??$data->name, 'qty' =>  1, 'price' =>  1, 'weight' => 1, 'options' => $cart_data]);

    }

    public function optimizeCart($user_id){
        $cartRecords = getCartDataInstance(4, $user_id);
    }


    private function getCalculationFrequency($analysis) {

        /**
         * Clearing data V1.0 @original
         */
        $analysis = preg_replace ( '/[^a-z0-9A-ZßöäüÖÄÜ]/i', '', $analysis );

        /**
         * Using the matrix V1.1 @Changed
         */
        $analysis_matrix = sis_replace($analysis);

        /**
         * calculate the Crossfoot V1.0 @original
         */
        $analysis_crossfoot = crossfoot($analysis_matrix);

        /**
         * calculate the Frequency V1.0 @original
         * input Crossfoot output
         */
        $analysis_freq = frequency_calculator($analysis_crossfoot);

        return (int)$analysis_freq;
    }



}
