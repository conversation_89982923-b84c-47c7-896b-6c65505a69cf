<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Model\Analyse;
use App\Model\User;

class GoBatchCalcService extends GoCalcService
{
    private string $goBatchBinaryPath;
    private int $phpMemoryLimitMB;
    private int $maxDaysPerExecution = 60; // Maximum days to process in one Go execution

    public function __construct()
    {
        parent::__construct();

        // Path to compiled Go batch binary
        $this->goBatchBinaryPath = base_path('app/Services/Go/bin/batch_calc');

        // Get PHP memory limit in MB
        $memoryLimit = ini_get('memory_limit');
        $this->phpMemoryLimitMB = $this->parseMemoryLimit($memoryLimit);
    }

    /**
     * Execute analysis calculation for long day (batch processing)
     * This method signature matches the expected usage pattern
     *
     * @param mixed $poolId - Pool ID(s) to process
     * @param int $days - Number of days to calculate
     * @param string $startDate - Starting date for calculations
     * @param mixed $calculationWith - Can be user->calculation_with or similar
     * @param mixed $themaSpeichern - Can be user->thema_speichern or similar
     * @param mixed $filterId - Optional filter ID
     * @return array - Array of daily analysis results matching executeAnalysisCalculation format
     */
    public function executeAnalysisCalculationLongDay($poolId, $days, $startDate, $calculationWith = null, $themaSpeichern = null, $filterId = null)
    {
        // Map the parameters to match internal expectations
        // calculationWith and themaSpeichern might be proid and subid in some contexts
        $proid = is_numeric($calculationWith) ? $calculationWith : null;
        $subid = is_numeric($themaSpeichern) ? $themaSpeichern : null;

        // Call the internal method with proper parameters
        return $this->executeLongDayCalculation($poolId, $days, $startDate, $proid, $subid, $filterId);
    }

    /**
     * Internal method for executing long day calculations
     * Processes in segments to avoid memory issues
     */
    private function executeLongDayCalculation($poolId, $days, $startDate = null, $proid = null, $subid = null, $filterId = null)
    {
        $userid = session()->get('id');

        if ($filterId == null) {
            $filterId = $this->userFilterid($userid);
        }

        $user = $filterId;

        // Use provided start date or default to today
        if ($startDate === null) {
            $startDate = date('Y-m-d');
        }

        // Prepare initial data (done once for all segments)
        $initialData = $this->prepareInitialData($poolId, $proid, $subid, $user);

        if (!$initialData['success']) {
            // Return empty array to match expected format
            return [];
        }

        // Determine if we need to process in segments
        if ($days > $this->maxDaysPerExecution) {
            // Process in segments
            $result = $this->processInSegments(
                $days,
                $startDate,
                $initialData,
                $user,
                $filterId,
                $proid,
                $subid
            );
        } else {
            // Process normally for small batches
            $result = $this->processSingleSegment(
                $days,
                $startDate,
                $initialData,
                $user,
                $filterId,
                $proid,
                $subid
            );
        }

        // Return just the results array to match the expected format
        // Each element in the array represents one day's analysis results
        if ($result['success'] && isset($result['results'])) {
            // Return the properly formatted array
            return $result['results'];
        }

        // Return empty array on failure
        return [];
    }

    /**
     * Prepare initial data that's common for all segments
     */
    private function prepareInitialData($poolId, $proid, $subid, $user)
    {
        Log::info("Preparing initial data", [
            'poolId' => $poolId,
            'proid' => $proid,
            'subid' => $subid,
            'user_id' => $user->id
        ]);

        $productColors = null;
        if ($proid) {
            $productColors = DB::table('product_settings')->where('product_id', $proid)->first();
        }

        // Get pool IDs
        $pool_ids_to_query = is_array($poolId) ? $poolId : [$poolId];

        if (empty(array_filter($pool_ids_to_query))) {
            if ($subid) {
                $pool_ids_to_query = $this->getPoolIdsBySubID($subid) ?? [];
            } else {
                $pool_ids_to_query = [];
            }
        }

        Log::info("Pool IDs to query", ['pool_ids' => $pool_ids_to_query]);

        // Early return if no pool IDs
        if (empty(array_filter($pool_ids_to_query))) {
            return [
                'success' => false,
                'error' => 'No pool IDs available',
                'results' => []
            ];
        }

        // Get analyses - optimize memory by selecting only needed fields
        $analyses = $this->getOptimizedAnalyses($pool_ids_to_query);

        Log::info("Retrieved analyses", ['count' => count($analyses)]);

        if (count($analyses) == 0) {
            return [
                'success' => false,
                'error' => 'No analyses found',
                'results' => []
            ];
        }

        $all_analysis_id = $analyses->pluck('id')->toArray();

        // Get supporting data
        $body_images = $this->getBodyImage($all_analysis_id);
        $mental_images = $this->getMentalImage($all_analysis_id);

        // Get causes data
        $todayCause = [];
        $randomCauses = ['causes' => collect(), 'medium' => collect(), 'tipp' => collect()];
        if ($subid) {
            $todayCause = $this->savedCauses($subid, $all_analysis_id, $user->id);
            $randomCauses = $this->randomCauses($subid);
        }

        $random_values = $this->randomValue($user->id);

        return [
            'success' => true,
            'analyses' => $analyses,
            'product_colors' => $productColors,
            'pool_ids' => $pool_ids_to_query,
            'all_analysis_id' => $all_analysis_id,
            'body_images' => $body_images,
            'mental_images' => $mental_images,
            'today_cause' => $todayCause,
            'random_causes' => $randomCauses,
            'random_values' => $random_values
        ];
    }

    /**
     * Process large batches in segments
     */
    private function processInSegments($totalDays, $startDate, $initialData, $user, $filterId, $proid, $subid)
    {
        $allResults = [];
        $totalProcessTime = 0;
        $allErrors = [];
        $segments = ceil($totalDays / $this->maxDaysPerExecution);

        Log::info("Processing {$totalDays} days in {$segments} segments");

        // Process each segment
        for ($segment = 0; $segment < $segments; $segment++) {
            $segmentStart = $segment * $this->maxDaysPerExecution;
            $segmentDays = min($this->maxDaysPerExecution, $totalDays - $segmentStart);

            // Calculate segment start date
            $segmentStartDate = date('Y-m-d', strtotime($startDate . " +{$segmentStart} days"));

            Log::info("Processing segment " . ($segment + 1) . " of {$segments}: {$segmentDays} days starting from {$segmentStartDate}");

            // Process this segment
            $segmentResult = $this->executeSegment(
                $segmentDays,
                $segmentStartDate,
                $segmentStart, // Offset for day indices
                $initialData,
                $user,
                $filterId,
                $proid,
                $subid
            );

            if (!$segmentResult['success']) {
                Log::error("Segment {$segment} failed", ['error' => $segmentResult['error']]);
                $allErrors[] = "Segment {$segment}: " . $segmentResult['error'];
                continue;
            }

            // Merge results
            if (isset($segmentResult['results'])) {
                // Results are already in the correct format from executeSegment
                $allResults = array_merge($allResults, $segmentResult['results']);
            }

            $totalProcessTime += $segmentResult['process_time_ms'] ?? 0;

            if (isset($segmentResult['errors'])) {
                $allErrors = array_merge($allErrors, $segmentResult['errors']);
            }

            // Small delay between segments to allow memory cleanup
            if ($segment < $segments - 1) {
                usleep(100000); // 100ms delay

                // Force garbage collection
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            }
        }

        Log::info("Completed processing {$totalDays} days in {$segments} segments");

        return [
            'success' => true,
            'results' => $allResults,
            'total_days' => $totalDays,
            'process_time_ms' => $totalProcessTime,
            'errors' => $allErrors,
            'segments_processed' => $segments
        ];
    }

    /**
     * Process a single segment
     */
    private function executeSegment($days, $startDate, $dayOffset, $initialData, $user, $filterId, $proid, $subid)
    {
        Log::info("Executing segment", [
            'days' => $days,
            'start_date' => $startDate,
            'day_offset' => $dayOffset,
            'analyses_count' => count($initialData['analyses'] ?? [])
        ]);

        // Check if we have analyses
        if (empty($initialData['analyses'])) {
            Log::error("No analyses available for segment");
            return [
                'success' => false,
                'error' => 'No analyses available',
                'results' => []
            ];
        }

        // Prepare base input for this segment
        $baseInput = $this->prepareOptimizedInputData(
            $initialData['analyses'],
            $user,
            $filterId,
            $initialData['product_colors'],
            $subid,
            $proid,
            $initialData['today_cause'],
            $initialData['random_causes'],
            $initialData['random_values']
        );

        // Log base input structure
        Log::info("Base input prepared", [
            'has_analyses' => !empty($baseInput['analyses']),
            'analyses_count' => count($baseInput['analyses'] ?? []),
            'user_id' => $baseInput['user']['id'] ?? null
        ]);

        // Prepare batch input for this segment
        $batchInput = [
            'mode' => 'batch',
            'long_day' => intval($days),
            'start_date' => $startDate,
            'base_input' => $baseInput,
            'pool_ids' => array_map('intval', $initialData['pool_ids']),
            'concurrent_ops' => $this->determineMemoryEfficientWorkers($days),
            'chunk_size' => 0, // Disable chunking for segments
            'stream_mode' => false, // Don't use stream mode for segments
            'memory_limit_mb' => $this->phpMemoryLimitMB
        ];

        // Ensure proper type handling
        $this->ensureProperTypes($batchInput);

        // Execute batch Go program for this segment
        $result = $this->executeBatchGoProgram($batchInput);

        if (!$result['success']) {
            Log::error("Segment execution failed", ['error' => $result['error']]);
            return [
                'success' => false,
                'error' => $result['error'],
                'results' => []
            ];
        }

        $batchOutput = $result['data'];

        Log::info("Segment output received", [
            'has_results' => isset($batchOutput['results']),
            'results_count' => isset($batchOutput['results']) ? count($batchOutput['results']) : 0,
            'mode' => $batchOutput['mode'] ?? 'unknown',
            'total_days' => $batchOutput['total_days'] ?? 0
        ]);

        // Process results
        $formattedResults = $this->formatBatchResults(
            $batchOutput,
            $initialData['body_images'],
            $initialData['mental_images']
        );

        Log::info("Segment formatted results", [
            'formatted_count' => count($formattedResults)
        ]);

        return [
            'success' => true,
            'results' => $formattedResults,
            'process_time_ms' => $batchOutput['process_time_ms'] ?? 0,
            'errors' => $batchOutput['errors'] ?? []
        ];
    }

    /**
     * Process a single segment (for batches <= maxDaysPerExecution)
     */
    private function processSingleSegment($days, $startDate, $initialData, $user, $filterId, $proid, $subid)
    {
        return $this->executeSegment(
            $days,
            $startDate,
            0, // No offset for single segment
            $initialData,
            $user,
            $filterId,
            $proid,
            $subid
        );
    }

    /**
     * Get optimized analyses with minimal memory footprint
     */
    private function getOptimizedAnalyses($pool_ids_to_query)
    {
        $locale = app()->getLocale();

        // Select only essential fields to reduce memory
        if ($locale != '' && $locale != 'de') {
            $analyses = DB::table($locale . '_analyses')
                ->select(
                    $locale . '_analyses.id',
                    $locale . '_analyses.name',
                    $locale . '_analyses.bioryth',
                    'analyses.name as de_name'
                )
                ->join('analyses', 'analyses.id', '=', $locale . '_analyses.id')
                ->join('analyse_pools', $locale . '_analyses.id', '=', 'analyse_pools.analyse_id')
                ->whereIn('analyse_pools.pool_id', $pool_ids_to_query)
                ->get();

            // Add minimal pool data
            $analyses = $analyses->map(function ($item) {
                $analysis = new \stdClass();
                $analysis->id = $item->id;
                $analysis->name = $item->name;
                $analysis->de_name = $item->de_name;
                $analysis->bioryth = $item->bioryth;
                return $analysis;
            });
        } else {
            // Use query builder for better memory efficiency
            $analyses = DB::table('analyses')
                ->select('analyses.id', 'analyses.name', 'analyses.bioryth')
                ->join('analyse_pools', 'analyses.id', '=', 'analyse_pools.analyse_id')
                ->whereIn('analyse_pools.pool_id', $pool_ids_to_query)
                ->get();
        }

        return $analyses;
    }

    /**
     * Prepare optimized input data with minimal memory usage
     */
    private function prepareOptimizedInputData($analyses, $user, $filterId, $productColors, $subid, $proid, $todayCause, $randomCauses, $random_values)
    {
        // Log analyses count for debugging
        Log::info("Preparing input data", [
            'analyses_count' => count($analyses),
            'user_id' => $user->id,
            'subid' => $subid,
            'proid' => $proid
        ]);

        // Prepare color settings
        $colorSettings = $this->prepareColorSettings($productColors, $user->id);

        // Build minimal analyses data
        $analysesData = [];
        $locale = app()->getLocale();

        foreach ($analyses as $analysis) {
            if ($locale == '' || $locale == 'de') {
                $calcName = $analysis->name;
                $displayName = $analysis->name;
            } else {
                $calcName = isset($analysis->de_name) ? $analysis->de_name : $analysis->name;
                $displayName = $analysis->name;
            }

            // Only include essential fields
            $analysesData[] = [
                'id' => intval($analysis->id),
                'name' => $calcName,
                'display_name' => $displayName,
                'bioryth' => intval($analysis->bioryth ?? 28),
                'pool_id' => 0, // Simplified
                'body_desc' => '',
                'mental_desc' => '',
                'description' => '',
                'desc_image' => '',
                'url_name' => '',
                'url_link' => '',
            ];
        }

        Log::info("Prepared analyses data", ['count' => count($analysesData)]);

        // Prepare minimal price values
        $priceValues = new \stdClass();
        if ($proid && $subid) {
            foreach ($analyses as $analysis) {
                $priceValue = $this->priceValue($proid, $subid, $analysis->id);
                if ($priceValue) {
                    $key = strval($analysis->id);
                    $priceValues->$key = [
                        'min_price' => isset($priceValue->min_price) ? floatval($priceValue->min_price) : 10.0,
                        'max_price' => isset($priceValue->max_price) ? floatval($priceValue->max_price) : 40.0,
                    ];
                }
            }
        }

        // Format causes data
        $formattedSavedCauses = $this->formatCausesForGo($todayCause);
        $formattedRandomCauses = $this->formatRandomCausesForGo($randomCauses);

        $inputData = [
            'user' => [
                'id' => intval($user->id),
                'first_name' => $user->first_name ?? '',
                'last_name' => $user->last_name ?? '',
                'gebdatum' => $user->gebdatum ?? '',
                'gebort' => $user->gebort ?? '',
                'thema_speichern' => $user->thema_speichern ?? '',
                'datumcore' => $user->datumcore ?? date('Y-m-d'),
                'calculation_with' => intval($user->calculation_with ?? 0),
                'biorythm' => intval($user->biorythm ?? 0),
                'bioryths' => intval($user->bioryths ?? 0),
                'ran_ana' => isset($user->userOption->ran_ana) ? intval($user->userOption->ran_ana) : 0,
                'pattern_switch' => isset($user->userOption->pattern_switch) ? intval($user->userOption->pattern_switch) : 0,
            ],
            'analyses' => $analysesData,
            'bioryth_system' => $this->biorythSystem,
            'color_settings' => $colorSettings,
            'random_values' => $random_values ?? [],
            'saved_causes' => $formattedSavedCauses,
            'random_causes' => $formattedRandomCauses,
            'price_values' => $priceValues,
            'subid' => $subid,
            'proid' => $proid,
            'filter_type' => isset($filterId->userfilter->filter_type) ? intval($filterId->userfilter->filter_type) : 1,
            'locale' => app()->getLocale() ?? '',
            'current_date' => date('Y-m-d'),
        ];

        // Log the structure for debugging
        Log::info("Input data structure", [
            'has_user' => isset($inputData['user']),
            'has_analyses' => isset($inputData['analyses']),
            'analyses_count' => count($inputData['analyses']),
            'has_bioryth_system' => isset($inputData['bioryth_system']),
        ]);

        return $inputData;
    }

    /**
     * Parse memory limit string to MB
     */
    private function parseMemoryLimit($memoryLimit)
    {
        $memoryLimit = strtolower($memoryLimit);
        $value = intval($memoryLimit);

        if (strpos($memoryLimit, 'g') !== false) {
            return $value * 1024;
        } elseif (strpos($memoryLimit, 'm') !== false) {
            return $value;
        } elseif (strpos($memoryLimit, 'k') !== false) {
            return intval($value / 1024);
        }

        return 128; // Default to 128MB
    }

    /**
     * Determine memory-efficient number of workers
     */
    private function determineMemoryEfficientWorkers($days)
    {
        // Conservative worker allocation for segments
        if ($days <= 20) {
            return 4;
        } elseif ($days <= 40) {
            return 6;
        } elseif ($days <= 60) {
            return 8;
        } else {
            return 10;
        }
    }

    /**
     * Determine optimal chunk size for batch processing
     */
    private function determineChunkSize($days)
    {
        // Smaller chunks for segments
        if ($days <= 20) {
            return 10;
        } elseif ($days <= 40) {
            return 15;
        } elseif ($days <= 60) {
            return 20;
        } else {
            return 25;
        }
    }

    /**
     * Ensure proper types in batch input
     */
    private function ensureProperTypes(&$batchInput)
    {
        if (isset($batchInput['base_input']['subid'])) {
            if ($batchInput['base_input']['subid'] !== null) {
                $batchInput['base_input']['subid'] = intval($batchInput['base_input']['subid']);
            }
        }

        if (isset($batchInput['base_input']['proid'])) {
            if ($batchInput['base_input']['proid'] !== null) {
                $batchInput['base_input']['proid'] = intval($batchInput['base_input']['proid']);
            }
        }
    }

    /**
     * Execute batch Go program with input data
     */
    private function executeBatchGoProgram(array $input): array
    {
        $jsonInput = json_encode($input);

        // Check JSON size
        $jsonSizeMB = strlen($jsonInput) / 1024 / 1024;
        Log::info("JSON input size: {$jsonSizeMB} MB for {$input['long_day']} days");

        // Write to temp file for debugging
        $debugFile = storage_path('logs/batch_input_' . time() . '.json');
        file_put_contents($debugFile, json_encode($input, JSON_PRETTY_PRINT));
        Log::info("Debug input saved to: {$debugFile}");

        // Write to temp file
        $tempFile = tempnam(sys_get_temp_dir(), 'gobatchcalc_');
        file_put_contents($tempFile, $jsonInput);

        try {
            // Execute Go batch binary
            $command = escapeshellcmd($this->goBatchBinaryPath) . ' < ' . escapeshellarg($tempFile);

            // Execute with output buffering to prevent memory issues
            $output = '';
            $handle = popen($command . ' 2>&1', 'r');

            if ($handle) {
                while (!feof($handle)) {
                    $output .= fread($handle, 8192); // Read in chunks
                }
                pclose($handle);
            } else {
                return ['success' => false, 'error' => 'Failed to execute Go batch program'];
            }

            if (empty($output)) {
                return ['success' => false, 'error' => 'No output from Go batch program'];
            }

            // Check output size
            $outputSizeMB = strlen($output) / 1024 / 1024;
            Log::info("JSON output size: {$outputSizeMB} MB");

            // Save output for debugging
            $debugOutputFile = storage_path('logs/batch_output_' . time() . '.json');
            file_put_contents($debugOutputFile, $output);
            Log::info("Debug output saved to: {$debugOutputFile}");

            $result = json_decode($output, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error("JSON decode error", [
                    'error' => json_last_error_msg(),
                    'output_sample' => substr($output, 0, 500)
                ]);
                return ['success' => false, 'error' => 'Invalid JSON response: ' . json_last_error_msg()];
            }

            // Log result structure
            Log::info("Go program result", [
                'mode' => $result['mode'] ?? 'unknown',
                'total_days' => $result['total_days'] ?? 0,
                'has_results' => isset($result['results']),
                'results_count' => isset($result['results']) ? count($result['results']) : 0,
                'has_chunks' => isset($result['chunks']),
                'chunks_count' => isset($result['chunks']) ? count($result['chunks']) : 0,
            ]);

            return ['success' => true, 'data' => $result];

        } catch (\Exception $e) {
            Log::error("Exception in executeBatchGoProgram", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return ['success' => false, 'error' => $e->getMessage()];
        } finally {
            // Clean up temp file
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    /**
     * Format batch results for output
     * Returns array in the same format as executeAnalysisCalculation would
     */
    private function formatBatchResults($batchOutput, $bodyImages, $mentalImages)
    {
        $formattedResults = [];

        Log::info("formatBatchResults called", [
            'has_results' => isset($batchOutput['results']),
            'results_count' => isset($batchOutput['results']) ? count($batchOutput['results']) : 0,
            'mode' => $batchOutput['mode'] ?? 'unknown'
        ]);

        // Process results from Go program
        if (isset($batchOutput['results']) && !empty($batchOutput['results'])) {
            foreach ($batchOutput['results'] as $dayResult) {
                // Skip days with errors
                if (isset($dayResult['error']) && !empty($dayResult['error'])) {
                    Log::warning("Day had error", ['day' => $dayResult['date'] ?? '', 'error' => $dayResult['error']]);
                    continue;
                }

                // Format to match executeAnalysisCalculation output
                $dayData = [
                    'analyses' => $dayResult['output']['analyses'] ?? [],
                    'bodyimages' => $bodyImages,
                    'mentalimages' => $mentalImages,
                    'red_count' => $dayResult['output']['red_count'] ?? 0,
                    'orange_count' => $dayResult['output']['orange_count'] ?? 0,
                    'green_count' => $dayResult['output']['green_count'] ?? 0,
                ];

                $formattedResults[] = $dayData;
            }

            Log::info("Formatted results", ['count' => count($formattedResults)]);
        } else {
            Log::warning("No results found in batch output");
        }

        return $formattedResults;
    }

    /**
     * Format a single day result
     * Removed - no longer needed as we format directly in formatBatchResults
     */

    /**
     * Alternative method name for backwards compatibility
     */
    public function executeAnalysisCalculation($poolId, $proid = null, $subid = null, $filterId = null)
    {
        // This delegates to the parent class for single day calculations
        return parent::productAnalyses($proid, $subid, $filterId, $poolId);
    }

    /**
     * Format causes for Go program
     */
    protected function formatCausesForGo($savedCauses)
    {
        $formatted = [
            'causes' => new \stdClass(),
            'medium' => new \stdClass(),
            'tipp' => new \stdClass()
        ];

        if (empty($savedCauses)) {
            return $formatted;
        }

        foreach (['causes', 'medium', 'tipp'] as $type) {
            if (isset($savedCauses[$type])) {
                $formatted[$type] = new \stdClass();

                if (is_array($savedCauses[$type]) || $savedCauses[$type] instanceof \Traversable) {
                    foreach ($savedCauses[$type] as $analysisId => $cause) {
                        $key = strval($analysisId);
                        $formatted[$type]->$key = is_object($cause) ? intval($cause->id) : intval($cause);
                    }
                }
            }
        }

        return $formatted;
    }

    /**
     * Format random causes for Go program
     */
    protected function formatRandomCausesForGo($randomCauses)
    {
        $formatted = [
            'causes' => new \stdClass(),
            'medium' => new \stdClass(),
            'tipp' => new \stdClass()
        ];

        if (empty($randomCauses)) {
            return $formatted;
        }

        foreach (['causes', 'medium', 'tipp'] as $type) {
            if (isset($randomCauses[$type])) {
                $formatted[$type] = new \stdClass();

                $items = $randomCauses[$type];
                if ($items instanceof \Illuminate\Support\Collection) {
                    foreach ($items as $index => $item) {
                        $id = is_object($item) ? intval($item->id) : intval($item);
                        $key = strval($id);
                        $formatted[$type]->$key = $id;
                    }
                } elseif (is_array($items)) {
                    foreach ($items as $index => $item) {
                        $id = is_object($item) ? intval($item['id']) : intval($item);
                        $key = strval($id);
                        $formatted[$type]->$key = $id;
                    }
                }
            }
        }

        return $formatted;
    }
}