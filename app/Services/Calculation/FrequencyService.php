<?php

namespace App\Services\Calculation;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use App\Http\Controllers\Ajax\SajaxController;
use Illuminate\Http\Request;
/**
 * FrequencyService - Centralized frequency calculation service
 * 
 * This service provides a unified interface for all frequency-related calculations
 * across dashboard widgets and components. It handles caching, rate limiting,
 * validation, and business logic transformations.
 */
class FrequencyService
{
    protected CalculationService $calculationService;

    public function __construct(CalculationService $calculationService)
    {
        $this->calculationService = $calculationService;
    }

    /**
     * Calculate frequency for a given topic with full error handling and caching
     *
     * @param string $topic The topic text to calculate frequency for
     * @param array $options Configuration options
     * @return int The calculated frequency (0 if calculation fails)
     */
    public function calculateFrequency(string $topic, array $options = []): int
    {
        return $this->calculationService->calculateFrequency($topic, $options);
    }

    /**
     * Calculate frequency with rate limiting for user interactions
     *
     * @param string $topic The topic text
     * @param int $userId User ID for rate limiting
     * @param array $options Configuration options
     * @return array Result with frequency and rate limit info
     */
    public function calculateFrequencyWithRateLimit(string $topic, int $userId, array $options = []): array
    {
        return $this->calculationService->calculateFrequencyWithRateLimit($topic, $userId, $options);
    }

    /**
     * Calculate frequency safely with comprehensive error handling
     * This method is designed to replace the calculateFrequencySafely methods in widgets
     *
     * @param string $topic Topic text
     * @param array $options Configuration options
     * @return int Calculated frequency
     */
    public function calculateFrequencySafely(string $topic, array $options = []): int
    {
        // Set default options for safe calculation
        $safeOptions = array_merge([
            'cache_ttl' => 3600,
            'min_frequency' => 250,
            'max_frequency' => 10000,
            'apply_transformations' => true,
            'log_errors' => true,
            'fallback_frequency' => 0
        ], $options);

        try {
            $frequency = $this->calculateFrequency($topic, $safeOptions);
            
            // Return fallback if calculation failed
            if ($frequency <= 0) {
                return $safeOptions['fallback_frequency'];
            }
            
            return $frequency;
        } catch (\Exception $e) {
            if ($safeOptions['log_errors']) {
                Log::error('Safe frequency calculation failed', [
                    'error' => $e->getMessage(),
                    'topic' => substr($topic, 0, 50) . '...'
                ]);
            }
            return $safeOptions['fallback_frequency'];
        }
    }

    /**
     * Update frequency from topic with rate limiting (for real-time updates)
     *
     * @param string $topic Topic text
     * @param int $userId User ID
     * @param callable|null $successCallback Callback for successful calculation
     * @param callable|null $errorCallback Callback for errors
     * @return array Result array
     */
    public function updateFrequencyFromTopic(
        string $topic, 
        int $userId, 
        ?callable $successCallback = null,
        ?callable $errorCallback = null
    ): array {
        $result = $this->calculateFrequencyWithRateLimit($topic, $userId);

        if ($result['success'] && $result['frequency'] > 0) {
            if ($successCallback) {
                $successCallback($result['frequency']);
            }
        } else {
            if ($errorCallback) {
                $errorCallback($result);
            }
        }

        return $result;
    }

    /**
     * Transform frequency based on business rules
     *
     * @param float $frequency Original frequency
     * @return float Transformed frequency
     */
    public function transformFrequency(float $frequency): float
    {
        return $this->calculationService->transformFrequency($frequency);
    }

    /**
     * Generate harmonic frequencies for a given fundamental frequency
     *
     * @param float $fundamental The fundamental frequency
     * @param int $harmonicCount Number of harmonics to generate
     * @return array Array of harmonic data
     */
    public function generateHarmonics(float $fundamental, int $harmonicCount = 5): array
    {
        return $this->calculationService->generateHarmonics($fundamental, $harmonicCount);
    }

    /**
     * Convert frequency to musical note
     *
     * @param float $frequency Frequency in Hz
     * @return string Musical note representation
     */
    public function frequencyToNote(float $frequency): string
    {
        return $this->calculationService->frequencyToNote($frequency);
    }

    /**
     * Generate random time within biorhythm bounds
     *
     * @param object|null $biorythDetails Biorhythm configuration
     * @return int Random time in seconds
     */
    public function generateRandomTime(?object $biorythDetails = null): int
    {
        return $this->calculationService->generateRandomTime($biorythDetails);
    }

    /**
     * Validate business rules for frequency and time
     *
     * @param string $topic Topic text
     * @param int $time Time value
     * @param object|null $biorythDetails Biorhythm configuration
     * @return array Validation result
     */
    public function validateBusinessRules(
        string $topic, 
        int $time, 
        ?object $biorythDetails = null
    ): array {
        return $this->calculationService->validateBusinessRules(
            $topic,
            $time,
            $biorythDetails
        );
    }

    /**
     * Sanitize input to prevent XSS and normalize text
     *
     * @param string $input Input text
     * @return string Sanitized text
     */
    public function sanitizeInput(string $input): string
    {
        return $this->calculationService->sanitizeInput($input);
    }

    /**
     * Clear frequency calculation cache for a specific topic
     *
     * @param string $topic Topic to clear cache for
     * @return bool True if cache was cleared
     */
    public function clearFrequencyCache(string $topic): bool
    {
        return $this->calculationService->clearFrequencyCache($topic);
    }

    /**
     * Get frequency calculation statistics for monitoring
     *
     * @param int $userId User ID
     * @return array Statistics array
     */
    public function getCalculationStats(int $userId): array
    {
        $key = 'frequency_calc_' . $userId;
        $attempts = RateLimiter::attempts($key);
        $remaining = RateLimiter::remaining($key, 10);
        $availableIn = RateLimiter::availableIn($key);

        return [
            'attempts' => $attempts,
            'remaining' => $remaining,
            'available_in' => $availableIn,
            'rate_limited' => $remaining <= 0
        ];
    }

    public function submitToSajaxController(Request $request): \Illuminate\Http\JsonResponse
    {
        $originalRequest = app('request');
        app()->instance('request', $request);

        try {
            return app(SajaxController::class)->add2Cart();
        } finally {
            app()->instance('request', $originalRequest);
        }
    }
}
