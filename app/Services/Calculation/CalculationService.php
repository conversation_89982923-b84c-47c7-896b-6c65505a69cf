<?php

namespace App\Services\Calculation;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;

class CalculationService
{
    private static ?bool $calculationWidth = null;

    function calcPersonForDays($user, $data, $days) {
        // Clean user data and generate matrix
        $userData = preg_replace('/[^a-z0-9A-ZßöäüÖÄÜ]/i', '', $user->first_name . $user->last_name . $user->gebdatum . $user->gebort . $user->thema_speichern);
        $matrix = sis_replace($userData);
    
        // Clean and generate data
        $data = preg_replace('/[^a-z0-9A-ZßöäüÖÄÜ]/i', '', $data);
        $data = sis_replace($data);
    
        // Determine the biorhythm period based on the user's bioryth setting
        $bioryth = ($user->biorythm) ? 28 : 365;
        // $bioryth = 365;


        // Calculate and generate output
        $output = [];
        $datumcore = \Carbon\Carbon::parse($user->datumcore)->subDays(3);

        for ($c = 0; $c <= $days; $c++) {

            $date = $datumcore->copy()->addDays($c);
            $day = $date->format('d');

            $dayMonth = $date->format('d M');
            $diffInDays = $date->diffInDays($datumcore);

            #total count of day alive
            $livedDays   = $this->lived_days($user->gebdatum, $date->format('Y-m-d'));

            $dayShift = $this->biorhytm_shift($bioryth, $livedDays);

            $result = round(100 * sin(crossfoot($matrix) * (pi() + $livedDays) + $dayShift));
            $resultData = round(100 * sin(crossfoot($data) * (pi() + $livedDays) + $dayShift));

            
            $output[] = [
                'day' => $day,
                'dayMonth' => $dayMonth,
                'value' => $result,
                'symp' => $resultData
            ];
        }
    
        return $output;
    }


    private function taguser($user)
    {

        date_default_timezone_set('Europe/Vienna');
        $userdate = date_create($user->datumcore);

        if ($user->datumcore == "" or $user->datumcore == "0000-00-00")
            $userdate = date_create('Today');

        $datetime1 = date_create($user->gebdatum);
        $datetime2 = $userdate;
        $interval1 = date_diff($datetime1, $datetime2);
        $taguser   = $interval1->format('%a');
        $taguser   = (int) $taguser; // Tage als Zahl wandeln

        return $taguser;
    }

    private function lived_days($user_birthdate,$datumcore)
    {
        date_default_timezone_set('Europe/Vienna');

        $cal_date = date_create($datumcore);

        if ($datumcore == "" or $datumcore == "0000-00-00") $cal_date = date_create('Today');

        $birthdate = date_create($user_birthdate);
        $interval = date_diff($birthdate, $cal_date);

        return (int) $interval->format('%a');
        
    }

    private function biorhytm_shift($bioryth, $livedDays)
    {
        // $calc   = ($livedDays + $day) / $bioryth;
        $calc   = $livedDays / $bioryth;
        $array  = explode(".", $calc);
        
        $shift  = ($livedDays - $array[0]) * $bioryth;

        return $shift;
    }


    public function calculationFromUserData($req){
        // Clean user data and generate matrix
        $userData = preg_replace('/[^a-z0-9A-ZßöäüÖÄÜ]/i', '', $req->first_name . $req->last_name . $req->birthdate . $req->birthplace . $req->topic);
        $matrix = sis_replace($userData);
    
        // Clean and generate data
        $analyses = preg_replace('/[^a-z0-9A-ZßöäüÖÄÜ]/i', '', $req->analyses);
        $text = sis_replace($analyses);
    
        // Determine the biorhythm period based on the user's bioryth setting
        $bioryth = ($req->cal_for_month) ? 28 : 365;
        // $bioryth = 365;
        

        // Calculate and generate output
        $output = [];

        $date = \Carbon\Carbon::parse($req->cal_day);
        $day = $date->format('d');

        $dayMonth = $date->format('d M');
        $diffInDays = $date->diffInDays($date);

        #total count of day alive
        $livedDays   = $this->lived_days($req->birthdate, $date->format('Y-m-d'));

        $dayShift = $this->biorhytm_shift($bioryth, $livedDays);

        $userReport = round(100 * sin(crossfoot($matrix) * (pi() + $livedDays) + $dayShift));
        
        $textAnalysesReport = round(100 * sin(crossfoot($text) * (pi() + $livedDays) + $dayShift));

        $beob  = abs(abs($userReport) - abs($textAnalysesReport)) * 100;

        $analyses_val = 100 - ($beob / (abs($userReport) + abs($textAnalysesReport)));
        
        $output[] = [
            'day' => $day,
            'dayMonth' => $dayMonth,
            'user_report' => $userReport,
            'analyses_report' => $textAnalysesReport,
            'analyses_val' => $analyses_val
        ];
    
        return $output;
    }


    public function calculationFromUser($req){
        $allAnalyses = json_decode($req->analyses);
        // Clean user data and generate matrix
        $userData = preg_replace('/[^a-z0-9A-ZßöäüÖÄÜ]/i', '', $req->first_name . $req->last_name . $req->birthdate . $req->birthplace . $req->topic);
        $matrix = sis_replace($userData);
        

        $setting = global_settings(getAuthId());

        $result = collect();
        
        foreach ($allAnalyses as $key => $analyse) {
            // Clean and generate data
            $analyses = preg_replace('/[^a-z0-9A-ZßöäüÖÄÜ]/i', '', $analyse);
            $text = sis_replace($analyses);

            $analyses_val = $this->calculation($req, $matrix, $text);
            
            if (intval($analyses_val) >= $setting->gs_red_min && intval($analyses_val) <= $setting->gs_red_max) {
                $class_skill = "#E84E1B";
            }
            elseif (intval($analyses_val) >= $setting->gs_orange_min && intval($analyses_val) <= $setting->gs_orange_max) {
                $class_skill = "#F8B133";
            }
            elseif (intval($analyses_val) >= $setting->gs_green_min && intval($analyses_val) <= $setting->gs_green_max) {
                $class_skill = "#2FAB66";
            }
            $result->push([
                'name' => $analyse,
                'val' => intval($analyses_val),
                'color' => $class_skill
            ]);
        }

        return $result;
    }

    private function calculation($req, $strMreplace, $strPreplace)
    {
        $bioryth = ($req->cal_for_month == 'true') ? 28 : 365;
        $days = 0;
        if(isset($req->cal_for)){
            if ($req->cal_for <= 1) {
                $days = 1; // Set the value to 1 if it's less than 1
            } elseif ($req->cal_for < 8) {
                $days = 7; // Set the value to 7 if it's less than 8
            } elseif ($req->cal_for < 32) {
                $days = 28; // Set the value to 28 if it's less than 32
            } elseif ($req->cal_for < 95) {
                $days = 90; // Set the value to 90 if it's less than 95
            } elseif ($req->cal_for < 189) {
                $days = 180; // Set the value to 180 if it's less than 189
            } elseif ($req->cal_for < 369) {
                $days = 365; // Set the value to 365 if it's less than 369
            }
        }

        $calcount = 0;
        $date = \Carbon\Carbon::parse($req->cal_day);
        if($days > 0){
            for($i = 0; $i < $days; $i++) {
            
                $currentDate = $date->addDays($i);
                #total count of day alive
                $livedDays   = $this->lived_days($req->birthdate, $currentDate->format('Y-m-d'));
                
                $shift = $this->biorhytm_shift($bioryth,$livedDays);
                $calcount += $this->calculateLifeTrendAccuracy($strMreplace, $strPreplace, $livedDays, $shift, $bioryth);
            }
            return $calcount/$days;
        }else {            
            #total count of day alive
            $livedDays   = $this->lived_days($req->birthdate, $date->format('Y-m-d'));
            
            $shift = $this->biorhytm_shift($bioryth,$livedDays);
            $calcount += $this->calculateLifeTrendAccuracy($strMreplace, $strPreplace, $livedDays, $shift, $bioryth);
        }
        return $calcount;
    }
    private function calculateLifeTrendAccuracy($strMreplace, $strPreplace, $livedDays, $shift, $bioryth)
    {
        // Shift in months
        $monthsShift = $shift;
        // Total lived days
        $totalDays = $livedDays;
        // Calculate years based on bioryth
        $years = $totalDays / $bioryth;
        // Split years into integer and fractional parts
        $yearArray = explode(".", $years);        
        // Calculate the fractional part of the years
        $remainingDays = ($years - $yearArray[0]) * $bioryth;
        // Calculate the first component
        $component1 = 100 * sin((crossfoot($strMreplace)) * (pi() + $totalDays) + $monthsShift);
        // Calculate the second component
        $component2 = 100 * sin((crossfoot($strPreplace)) * (pi() + $totalDays) + $remainingDays);
        // Calculate the absolute difference
        $absDiff = abs(abs($component1) - abs($component2)) * 100;
        // Calculate the final result
        $lifeTrendAccuracy = 100 - ($absDiff / (abs($component1) + abs($component2)));

        return $lifeTrendAccuracy;
    }



    public function calculationFromUserLTA($req){
        $allAnalyses = json_decode($req->analyses);
        // Clean user data and generate matrix
        $userData = preg_replace('/[^a-z0-9A-ZßöäüÖÄÜ]/i', '', $req->first_name . $req->last_name . $req->birthdate . $req->birthplace . $req->topic);
        $userMatrix = sis_replace($userData);
        
        $setting = global_settings(getAuthId());
        
        $date = \Carbon\Carbon::parse($req->cal_day);

        if(isset($req->cal_for)){
            if ($req->cal_for <= 1) {
                $days = 1; // Set the value to 1 if it's less than 1
            } elseif ($req->cal_for < 8) {
                $days = 7; // Set the value to 7 if it's less than 8
            } elseif ($req->cal_for < 32) {
                $days = 28; // Set the value to 28 if it's less than 32
            } elseif ($req->cal_for < 95) {
                $days = 90; // Set the value to 90 if it's less than 95
            } elseif ($req->cal_for < 189) {
                $days = 180; // Set the value to 180 if it's less than 189
            } elseif ($req->cal_for < 369) {
                $days = 365; // Set the value to 365 if it's less than 369
            }
        }
        // $bioryth = ($req->cal_for_month == 'true') ? 28 : 365;
        $bioryth = 28;
        $result = collect();
        foreach ($allAnalyses as $key => $analyse) {
            $analyseCount = 0;
            
            for($i = 0; $i < $days; $i++) {
                $currentDate = $date->addDays($i);

                #total count of day alive
                $livedDays   = $this->lived_days($req->birthdate, $currentDate->format('Y-m-d'));

                $dayShift = $this->biorhytm_shift($bioryth, $livedDays);

                
                // Clean and generate data
                $analyses = preg_replace('/[^a-z0-9A-ZßöäüÖÄÜ]/i', '', $analyse);
                $text = sis_replace($analyses);

                //get calculate value
                $analyseCount += intval($this->calculationLTA($userMatrix, $text, $livedDays, $dayShift, $bioryth));
            }

            $analyses_val = $analyseCount/$days;
            if($analyses_val <= 0) continue;
            if ($analyses_val >= $setting->gs_red_min && $analyses_val <= $setting->gs_red_max) {
                $class_skill = "#E84E1B";
            }
            elseif ($analyses_val >= $setting->gs_orange_min && $analyses_val <= $setting->gs_orange_max) {
                $class_skill = "#F8B133";
            }
            elseif ($analyses_val >= $setting->gs_green_min && $analyses_val <= $setting->gs_green_max) {
                $class_skill = "#2FAB66";
            }

            $result->push([
                'name' => $analyse,
                'val' => intval($analyses_val),
                'color' => $class_skill
            ]);
        }
        return $result;
    }
    private function calculationLTA($strMreplace, $strPreplace, $livedDays, $shift, $bioryth)
    {    
        $tagmm   = $shift;

        $tag     = $livedDays;
        $tagjahr = $tag / $bioryth;
        $array   = explode(".", $tagjahr);
        $tags    = ($tagjahr - $array[0]) * $bioryth;
        $ym    = 100 * sin((crossfoot($strMreplace)) * (pi() + $tag) + $tagmm);
        $yp    = 100 * sin((crossfoot($strPreplace)) * (pi() + $tag) + $tags);

        $beob  = abs(abs($ym) - abs($yp)) * 100;
        $beerg = 100 - ($beob / (abs($ym) + abs($yp)));

        return $beerg;
    }

    public static function setCalculationWidth(bool $width)
    {
        self::$calculationWidth = $width;
    }

    public static function getCalculationWidth(): ?bool
    {
        return self::$calculationWidth;
    }

    public static function isCalculationWidthSet(): bool
    {
        return !is_null(self::getCalculationWidth());
    }

    /**
     * Calculate frequency for a given topic with caching and error handling
     *
     * @param string $topic The topic text to calculate frequency for
     * @param array $options Configuration options
     * @return int The calculated frequency (0 if calculation fails)
     */
    public function calculateFrequency(string $topic, array $options = []): int
    {
        // Set default options
        $options = array_merge([
            'cache_ttl' => 3600,
            'min_frequency' => 250,
            'max_frequency' => 10000,
            'apply_transformations' => true,
            'log_errors' => true
        ], $options);

        try {
            // Sanitize input
            $sanitizedTopic = $this->sanitizeInput($topic);

            if (empty($sanitizedTopic)) {
                return 0;
            }

            // Cache calculation results
            $cacheKey = 'freq_calc_' . md5($sanitizedTopic);

            return Cache::remember($cacheKey, $options['cache_ttl'], function () use ($sanitizedTopic, $options) {
                // Use the core calculation function
                $frequency = calculationFrequency($sanitizedTopic);

                // Apply transformations if enabled
                if ($options['apply_transformations']) {
                    $frequency = $this->transformFrequency($frequency);
                }

                // Validate frequency is within acceptable range
                if ($frequency < $options['min_frequency'] || $frequency > $options['max_frequency']) {
                    return $options['min_frequency']; // Return minimum valid frequency
                }

                return (int)$frequency;
            });
        } catch (\Exception $e) {
            if ($options['log_errors']) {
                Log::error('Frequency calculation failed', [
                    'error' => $e->getMessage(),
                    'topic' => substr($topic, 0, 50) . '...',
                    'options' => $options
                ]);
            }
            return 0;
        }
    }

    /**
     * Calculate frequency with rate limiting for user interactions
     *
     * @param string $topic The topic text
     * @param int $userId User ID for rate limiting
     * @param array $options Configuration options
     * @return array Result with frequency and rate limit info
     */
    public function calculateFrequencyWithRateLimit(string $topic, int $userId, array $options = []): array
    {
        $options = array_merge([
            'rate_limit_key' => 'frequency_calc_',
            'rate_limit_max' => 10,
            'rate_limit_window' => 60
        ], $options);

        $key = $options['rate_limit_key'] . $userId;

        // Check rate limit
        if (RateLimiter::tooManyAttempts($key, $options['rate_limit_max'])) {
            $seconds = RateLimiter::availableIn($key);
            return [
                'success' => false,
                'frequency' => 0,
                'rate_limited' => true,
                'retry_after' => $seconds,
                'message' => trans('action.too_many_calculations', ['seconds' => $seconds])
            ];
        }

        // Hit the rate limiter
        RateLimiter::hit($key, $options['rate_limit_window']);

        // Calculate frequency
        $frequency = $this->calculateFrequency($topic, $options);

        return [
            'success' => true,
            'frequency' => $frequency,
            'rate_limited' => false,
            'retry_after' => 0,
            'message' => ''
        ];
    }

    /**
     * Transform frequency based on business rules
     *
     * @param float $frequency Original frequency
     * @return float Transformed frequency
     */
    public function transformFrequency(float $frequency): float
    {
        if ($frequency > 0 && $frequency < 120) {
            $frequency *= 3;
        } else if ($frequency > 120 && $frequency < 240) {
            $frequency *= 2;
        }

        if ($frequency < 250) {
            $frequency = 250;
        }

        return $frequency;
    }

    /**
     * Generate harmonic frequencies for a given fundamental frequency
     *
     * @param float $fundamental The fundamental frequency
     * @param int $harmonicCount Number of harmonics to generate
     * @return array Array of harmonic data
     */
    public function generateHarmonics(float $fundamental, int $harmonicCount = 5): array
    {
        $harmonics = [];

        // Add subharmonic
        $harmonics[] = [
            'name' => 'Subharmonic (F/2)',
            'value' => $fundamental / 2,
            'note' => $this->frequencyToNote($fundamental / 2)
        ];

        // Add fundamental
        $harmonics[] = [
            'name' => 'Fundamental (F)',
            'value' => $fundamental,
            'note' => $this->frequencyToNote($fundamental)
        ];

        // Add harmonics
        for ($i = 2; $i <= $harmonicCount + 1; $i++) {
            $harmonics[] = [
                'name' => "{$i}" . ($i == 2 ? 'nd' : ($i == 3 ? 'rd' : 'th')) . " Harmonic ({$i}F)",
                'value' => $fundamental * $i,
                'note' => $this->frequencyToNote($fundamental * $i)
            ];
        }

        return $harmonics;
    }

    /**
     * Convert frequency to musical note
     *
     * @param float $frequency Frequency in Hz
     * @return string Musical note representation
     */
    public function frequencyToNote(float $frequency): string
    {
        if ($frequency <= 0) return '';

        // A4 = 440Hz reference
        $a4 = 440;
        $c0 = $a4 * pow(2, -4.75);

        $notes = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];

        if ($frequency > $c0) {
            $halfSteps = 12 * log($frequency / $c0) / log(2);
            $octave = floor($halfSteps / 12);
            $noteIndex = round($halfSteps) % 12;

            return $notes[$noteIndex] . $octave;
        }

        return '';
    }

    /**
     * Sanitize input to prevent XSS and normalize text
     *
     * @param string $input Input text
     * @return string Sanitized text
     */
    public function sanitizeInput(string $input): string
    {
        return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Generate random time within biorhythm bounds
     *
     * @param object|null $biorythDetails Biorhythm configuration
     * @return int Random time in seconds
     */
    public function generateRandomTime(?object $biorythDetails = null): int
    {
        if (!$biorythDetails) {
            $biorythDetails = biorythVisibleDetails();
        }

        if (!$biorythDetails) {
            return 30; // Default fallback
        }

        $minTime = max(5, (int)($biorythDetails->gs_min_price ?? 5));
        $maxTime = min(3600, (int)($biorythDetails->gs_max_price ?? 3600));

        return rand($minTime, $maxTime);
    }

    /**
     * Validate business rules for time and topic
     *
     * @param string $topic Topic text
     * @param int $time Time value
     * @param object|null $biorythDetails Biorhythm configuration
     * @return array Validation result
     */
    public function validateBusinessRules(
        string $topic, 
        int $time, 
        ?object $biorythDetails = null
    ): array {
        $errors = [];
        $warnings = [];
        
        // Default values if biorythDetails is null
        $minTime = $biorythDetails?->gs_min_price ?? 5;
        $maxTime = $biorythDetails?->gs_max_price ?? 3600;

        // Validate time
        $timeValid = $time >= $minTime && $time <= $maxTime;
        if (!$timeValid) {
            $errors[] = "Time must be between {$minTime} and {$maxTime} seconds";
        }

        // Validate topic
        if (empty(trim($topic))) {
            $warnings[] = "Topic should not be empty";
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
            'time_valid' => $timeValid
        ];
    }

    /**
     * Clear frequency calculation cache for a specific topic
     *
     * @param string $topic Topic to clear cache for
     * @return bool True if cache was cleared
     */
    public function clearFrequencyCache(string $topic): bool
    {
        $sanitizedTopic = $this->sanitizeInput($topic);
        $cacheKey = 'freq_calc_' . md5($sanitizedTopic);

        return Cache::forget($cacheKey);
    }
}