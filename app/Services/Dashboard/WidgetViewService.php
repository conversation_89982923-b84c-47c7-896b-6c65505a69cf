<?php

namespace App\Services\Dashboard;

use App\Enums\ActionType;
use App\Model\AllSingleAction;
use App\Model\User;
use App\Services\GoCalcService;
use App\Statics\Calculation;
use App\Model\Dashboards\Dashboard;
use App\Traits\PoolAnalysesGeneralFunctions;
use App\Model\Dashboards\ModuleHeadDashboard;
use App\Services\GoBatchCalcService;


class WidgetViewService
{
    use PoolAnalysesGeneralFunctions;

    public function processWidgetView($poolId,$diagramType,$filters = [], $customSorting = []){

        $userId = getUserDetails()->id;
        $user = User::with('userfilter:user_id,filter_type')->find($userId, [
                    'id',
                    'language_id',
                    'first_name',
                    'last_name',
                    'gebort',
                    'gebdatum',
                    'datumcore',
                    'calculation_with',
                    'thema_speichern'
                ]);
        $sorting =  $user->userfilter ? $user->userfilter->filter_type : 3;
        $langBaseAnalyses = $this->poolIdToUserLngBaseAnalysesGet($poolId);
        #need all time de formated analyses name
        $deAnalysesForCalculation = $langBaseAnalyses->pluck(app()->getLocale() == 'de' ? 'name' : 'de_name', 'id')->toArray() ?? [];
        #According to the user's language 
        $targetedLangbaseAnalyses = $this->targetedLangbaseAnalysesData($langBaseAnalyses);
        #bussness logic
        if($filters['longDay']){
            $longDayAnalyses = [];
            $start_date = $user->datumcore;
            if (AllSingleAction::getFlag(ActionType::V3_ACTIVATE_GO_MODE)) {
                $longDayAnalyses = $this->goAlgorithm($start_date, $filters, $poolId);
            } else {
                $longDayAnalyses = $this->plainPHPAlgorithm($start_date, $filters, $poolId);
            }
            $analysisCalculationResponse = $this->analysesColorCountAlgorithm($longDayAnalyses);
        }else{
            $analysisCalculationResponse = $this->executeAnalysisCalculation($poolId);
        }

        $sortResult = $this->processAndSortLangBaseData($targetedLangbaseAnalyses, $deAnalysesForCalculation, $analysisCalculationResponse);
        // apply sorting for long day filters
        if($filters['longDay']){
            $sortResult = $this->sortLongDayArray($sortResult); // default sorting 100-1
        }else{
            $sortResult = $this->filterAndSortArray($sortResult, $sorting);
        }

        if($customSorting) {
            $sortResult = $this->sortArrayByAnalysisIdOrder($sortResult, $customSorting);
        }
        // Format the sorted result as expected for final response
        return $this->diagramTypeBaseFormat($sortResult, $diagramType, $filters['longDay'] ?? null);
    }

    private function sortArrayByAnalysisIdOrder(array $data, array $sorting)
    {
        // Create a map for quick lookup of the order index of each analysis_id
        $orderMap = array_flip($sorting); // analysis_id => position

        usort($data, function ($a, $b) use ($orderMap) {
            $posA = $orderMap[$a['analysis_id']] ?? PHP_INT_MAX; 
            $posB = $orderMap[$b['analysis_id']] ?? PHP_INT_MAX;
            return $posA <=> $posB;
        });

        return $data;
    }

    private function targetedLangbaseAnalysesData(object $analyses): array
    {
        // Build all arrays in ONE traversal
        $targetedAnalysesName = $description = $desc_img = $body_desc = $mental_desc = [];
        $body_desc_images = $mental_desc_images = [];

        foreach ($analyses as $analyse) {
            $id = $analyse->id;
            $targetedAnalysesName[$id] = $analyse->name ?? '';
            $description[$id]          = $analyse->description ?? '';
            $desc_img[$id]              = $analyse->desc_image ?? '';
            $body_desc[$id]             = $analyse->body_desc ?? '';
            $mental_desc[$id]           = $analyse->mental_desc ?? '';

            foreach ($analyse->bodyimages ?? [] as $img) {
                if (isset($img->analyse_id, $img->image)) {
                    $body_desc_images[$img->analyse_id][] = $img->image;
                }
            }

            foreach ($analyse->mentalimages ?? [] as $img) {
                if (isset($img->analyse_id, $img->image)) {
                    $mental_desc_images[$img->analyse_id][] = $img->image;
                }
            }
        }

        return compact(
            'targetedAnalysesName',
            'description',
            'desc_img',
            'body_desc',
            'mental_desc',
            'body_desc_images',
            'mental_desc_images'
        );
    }

    private function plainPHPAlgorithm($start_date, $filters, $poolId)
    {
        $longDayAnalyses = [];
        for ($i = 0; $i < $filters['longDay']; $i++) {
            $curdate = date('Y-m-d', strtotime($start_date . '+' . $i . ' days'));
            Calculation::setDatumcore($curdate);
            $longDayAnalyses[] = $this->executeAnalysisCalculation($poolId);
        }

        return $longDayAnalyses;
    }

    private function goAlgorithm($start_date, $filters, $poolId)
    {
        $longDayAnalyses = GoCalcService::getInstance()->productAnalyses(
            null,
            null,
            $filters['filterId'] ?? null,
            [$poolId],
            $filters['longDay'],
            $start_date
        );
        return array_map(function($analysis) {return $analysis['analyses'];}, $longDayAnalyses);
    }
}