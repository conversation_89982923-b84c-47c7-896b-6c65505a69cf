<?php

namespace App\Services\Dashboard;

use App\Enums\AnalysisColor;
use App\Model\Analyse;
use App\Model\User;
use App\Traits\LivewireGeneralFunctions;
use App\Traits\PoolAnalysesGeneralFunctions;

class ColorAnalysesAddToCartService
{
    use PoolAnalysesGeneralFunctions, LivewireGeneralFunctions;

    private $cartManager;

    public function __construct()
    {
        $this->cartManager = new CartManager();
    }

    public function addToCart(int $poolId, int $analysesId = null,string $color = '')
    {
        $color = $color ? AnalysisColor::getColorByKey($color) : $color;
        $user = User::with('userfilter:user_id,filter_type')->find(getUserDetails()->id, ['id', 'language_id', 'first_name', 'last_name', 'gebort', 'gebdatum', 'datumcore', 'calculation_with', 'thema_speichern']);
        $langBaseAnalyses = $this->poolIdToUserLngBaseAnalysesGet($poolId)->when($analysesId, fn ($query) => $query->where('id', $analysesId));
        #need all time de formated analyses name
        $deAnalysesForCalculation = $langBaseAnalyses->pluck(app()->getLocale() == 'de' ? 'name' : 'de_name', 'id')->toArray() ?? [];
        #According to the user's language
        $targetedLangbaseAnalysesName = [
            'targetedAnalysesName' => $langBaseAnalyses->pluck('name','id')->toArray() ?? [],
        ];
        $analysisCalculationResponse = $this->executeAnalysisCalculation($poolId, $analysesId);

        $sortResult = $this->processAndSortLangBaseData($targetedLangbaseAnalysesName, $deAnalysesForCalculation, $analysisCalculationResponse);

        $colorBaseAnalyses = $this->getColorBaseAnalysesFilter($sortResult, $deAnalysesForCalculation,$color);

       return $this->addToCartDashboardWidget($colorBaseAnalyses) ? $colorBaseAnalyses : null;
    }

    public function addToCartDashboardWidget($analyses)
    {
        try {
            if (empty($analyses)) return false;
            $type = 'Analysis';
            foreach ($analyses as $ana) {
                #Get Price for cart
                if (isset($ana['ana_min_price']) && isset($ana['ana_max_price']) && $ana['ana_min_price'] != '' && $ana['ana_max_price'] != '') {
                    $randPrice = rand($ana['ana_min_price'], $ana['ana_max_price']);
                } else {
                    #Random Price For Cart
                    $priceValue = biorythVisibleDetails();
                    $randPrice = rand($priceValue->gs_min_price, $priceValue->gs_max_price);
                }
                $submenuId = Analyse::find($ana['analysis_id'])?->defaultSubmenuId() ?? 0;
                $submenuRealId = ((empty(request()->submenu_id)) ? 0 : request()->submenu_id);
                $data['userID'] = getUserId();
                $data["analysisID"] = $ana['analysis_id'];
                $data["analysisName"] = $ana['name'];
                $data['submenu_id'] = $submenuRealId != 0 ? $submenuRealId : $submenuId;
                $data['productID'] =  (empty(request()->proID)) ? 0 : request()->proID;
                $data["calculation"] = round($ana['val'], 1);
                $data['male'] = (empty(request()->male)) ? 0 : request()->male;
                $data['heart'] = (empty(request()->heart)) ? 0 : request()->heart;
                $data['price'] = $randPrice;
                $data['causes_id'] = (empty(request()->causes_id)) ? 0 : request()->causes_id;
                $data['medium_id'] = (empty(request()->medium_id)) ? 0 : request()->medium_id;
                $data['tipp_id'] = (empty(request()->tipp_id)) ? 0 : request()->tipp_id;
                $data['color'] = $ana['color'];
                $data["desc"] = "";
                $data["desc_img"] = "";
                $data['type'] =  $type;
                $data['minute'] = gmdate('i:s', $randPrice);

                $this->cartManager->addToCart(getUserId(), $type, $data);
            }
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}