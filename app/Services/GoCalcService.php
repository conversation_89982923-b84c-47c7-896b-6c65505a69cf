<?php

namespace App\Services;

class GoCalcService
{
    private string $goBatchBinaryPath;
    protected array $biorythSystem;
    private int $phpMemoryLimitMB;
    private string $logPath;
    private \PDO $pdo;
    public int $userId;
    public string $locale;
    public ?string $envPath = null;
    private bool $debugMode = false;
    private static ?GoCalcService $instance = null;

    /**
     * Get a singleton instance of the GoCalcService.
     *
     * @param int|null $userId
     * @param string|null $locale
     * @param string|null $envPath
     * @return GoCalcService
     */
    public static function getInstance($userId = null, $locale = null, $envPath = null): GoCalcService
    {
        if (self::$instance === null) {
            if ($userId === null || $locale === null) {
                if (function_exists('auth') && auth()->check()) {
                    $userId = auth()->id();
                    $locale = app()->getLocale();
                }
            }

            if ($userId === null || $locale === null) {
                throw new \InvalidArgumentException("User ID and locale must be provided for the first instance creation.");
            }

            self::$instance = new self($userId, $locale, $envPath);
        }

        return self::$instance;
    }

    /**
     * GoCalcService constructor.
     *
     * @param int $userId
     * @param string $locale
     * @param string|null $envPath
     */
    public function __construct(int $userId, string $locale, ?string $envPath = null)
    {
        // default path is app dir app/Services/GoCalcService.php
        if ($envPath === null) {
            $envPath = __DIR__ . '/../../.env'; // Adjust the path as needed
        }
        // Get environment variables from .env file
        $this->loadEnv($envPath);

        // Path to compiled Go batch binary
        $this->goBatchBinaryPath = __DIR__ . '/Go/bin/batch_calc';
        $this->logPath = getenv('LOG_PATH');

        $memoryLimit = ini_get('memory_limit');
        $this->phpMemoryLimitMB = $this->parseMemoryLimit($memoryLimit);

        $this->userId = $userId;
        $this->locale = $locale;
        $this->debugMode = (bool)getenv('DEBUG_MODE_GO');

        // Initialize PDO connection
        $dsn = 'mysql:host=' . getenv('DB_HOST') . ';port=' . getenv('DB_PORT') . ';dbname=' . getenv('DB_DATABASE') . ';charset=utf8mb4';
        $username = getenv('DB_USERNAME');
        $password = getenv('DB_PASSWORD');

        try {
            $this->pdo = new \PDO($dsn, $username, $password, [\PDO::ATTR_EMULATE_PREPARES => false]);
            $this->pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);
            $this->pdo->setAttribute(\PDO::ATTR_DEFAULT_FETCH_MODE, \PDO::FETCH_OBJ);
        } catch (\PDOException $e) {
            $this->logError('Database connection failed: ' . $e->getMessage());
            die('Database connection failed.');
        }

        $biorythDetail = $this->biorythVisibleDetails();
        $this->biorythSystem = [
            'p' => $biorythDetail->gs_bioryth_p ?? 28,
            'm' => $biorythDetail->gs_bioryth_m ?? 28
        ];
    }

    /**
     * Loads environment variables from a .env file.
     *
     * @param string $envPath The path to the .env file.
     */
    private function loadEnv(string $envPath)
    {
        if (!file_exists($envPath)) {
            $this->logError('.env file does not exist at ' . $envPath);
            die('.env file does not exist');
        }

        $lines = file($envPath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos(trim($line), '#') === 0) {
                continue;
            }
            list($name, $value) = explode('=', $line, 2);
            $value = trim($value, "\"'");
            putenv("$name=$value");
        }
    }

    /**
     * Unified method for all calculations (single-day and batch).
     *
     * @param mixed $proid - Product ID.
     * @param mixed $subid - Submenu ID.
     * @param mixed $filterId - Optional filter ID.
     * @param array $target_pool_ids - Target pool IDs.
     * @param int $days - Number of days to calculate. Default to 1 for a single day.
     * @param string|null $startDate - Starting date for calculations (format YYYY-MM-DD).
     * @return array - Array of analysis results.
     */
    public function productAnalyses($proid = null, $subid = null, $filterId = null, array $target_pool_ids = [], int $days = 1, string $startDate = null): array
    {
        $this->logInfo("GoCalcService productAnalyses START", [
            'proid' => $proid, 'subid' => $subid, 'days' => $days, 'startDate' => $startDate
        ]);

        $userId = $this->userId;
        $this->logDebug("GoCalcService - Current userId: {$userId}");

        if ($filterId == null) {
            $user = $this->userFilterid($userId);
            $this->logDebug("GoCalcService - Fetched user object: " . json_encode($user));
        } else {
            $user = $filterId;
        }

        $productColors = null;
        if ($proid) {
            $stmt = $this->pdo->prepare("SELECT * FROM product_settings WHERE product_id = :proid");
            $stmt->execute([':proid' => $proid]);
            $productColors = $stmt->fetch();
            $this->logDebug("GoCalcService - Product colors fetched: " . json_encode($productColors));
        }

        $pool_ids_to_query = is_array($target_pool_ids) ? $target_pool_ids : [$target_pool_ids];
        $this->logDebug("GoCalcService - Initial pool_ids_to_query: " . json_encode($pool_ids_to_query));

        if (empty(array_filter($pool_ids_to_query))) {
            if ($subid) {
                $pool_ids_to_query = $this->getPoolIdsBySubID(intval($subid)) ?? [];
                $this->logDebug("GoCalcService - Pool IDs from subid: " . json_encode($pool_ids_to_query));
            } else {
                $pool_ids_to_query = [];
                $this->logDebug("GoCalcService - Pool IDs empty, no subid provided.");
            }
        }

        if (empty(array_filter($pool_ids_to_query))) {
            $this->logInfo("GoCalcService - No valid Pool IDs found, returning empty result.");
            return $this->getEmptyResult($days);
        }

        $analyses = $this->getAnalysesData($pool_ids_to_query);
        $this->logDebug("GoCalcService - Analyses data fetched (count): " . count($analyses));

        if (count($analyses) == 0) {
            $this->logInfo("GoCalcService - No analyses found for pools, returning empty result.");
            return $this->getEmptyResult($days);
        }

        $all_analysis_id = array_column($analyses, 'id');
        $this->logDebug("GoCalcService - All analysis IDs: " . json_encode($all_analysis_id));


        $body_images = $this->getBodyImage($all_analysis_id);
        $mental_images = $this->getMentalImage($all_analysis_id);
        $this->logDebug("GoCalcService - Body/Mental images fetched (counts): " . count($body_images) . "/" . count($mental_images));


        $todayCause = [];
        $randomCauses = ['causes' => [], 'medium' => [], 'tipp' => []];
        if ($subid) {
            $todayCause = $this->savedCauses(intval($subid), $all_analysis_id, $user->id);
            $randomCauses = $this->randomCauses(intval($subid));
            $this->logDebug("GoCalcService - Causes data fetched (saved/random): " . json_encode($todayCause) . " / " . json_encode($randomCauses));
        }

        $random_values = $this->randomValue($user->id);
        $this->logDebug("GoCalcService - Random values fetched: " . json_encode($random_values));


        $input = $this->prepareInputData(
            $analyses,
            $user,
            $filterId,
            $productColors,
            $subid,
            $todayCause,
            $randomCauses,
            $random_values,
            $proid
        );
        $this->logDebug("GoCalcService - Prepared input data for Go: " . json_encode($input));


        if ($startDate === null) {
            $startDate = date('Y-m-d');
            $this->logDebug("GoCalcService - Start date defaulted to current date: {$startDate}");
        }

        $batchInput = [
            'mode' => 'batch',
            'long_day' => intval($days),
            'start_date' => $startDate,
            'base_input' => $input,
            'pool_ids' => array_map('intval', $pool_ids_to_query),
            'concurrent_ops' => $this->determineMemoryEfficientWorkers($days),
            'chunk_size' => 0,
            'stream_mode' => false,
            'memory_limit_mb' => $this->phpMemoryLimitMB
        ];
        $this->logDebug("GoCalcService - Final batch input for Go program: " . json_encode($batchInput));


        $this->logInfo("Batch calculation input", [
            'days' => $days,
            'start_date' => $startDate,
            'analyses_count' => count($analyses),
            'user_id' => $user->id,
            'subid' => $subid,
            'proid' => $proid
        ]);

        $result = $this->executeBatchGoProgram($batchInput);
        $this->logDebug("GoCalcService - Result from Go program execution: " . json_encode($result));


        if (!$result['success']) {
            $this->logError("Go batch program execution failed", ['error' => $result['error']]);
            return $this->getEmptyResult($days);
        }

        $batchOutput = $result['data'];

        $formattedResults = $this->formatBatchResults(
            $batchOutput,
            $body_images,
            $mental_images,
            $subid
        );
        $this->logDebug("GoCalcService - Formatted results from Go program: " . json_encode($formattedResults));


        if ($days === 1 && isset($formattedResults[0])) {
            $this->logInfo("GoCalcService productAnalyses END (single day result)");
            return $formattedResults[0];
        }

        $this->logInfo("GoCalcService productAnalyses END (multi-day result)");
        return $formattedResults;
    }

    /**
     * Retrieves analysis data from the database.
     *
     * @param array $pool_ids_to_query
     * @return array
     */
    private function getAnalysesData(array $pool_ids_to_query): array
    {
        $this->logDebug("GoCalcService getAnalysesData START with pool_ids: " . json_encode($pool_ids_to_query));
        $locale = $this->locale;
        $inClause = implode(',', array_fill(0, count($pool_ids_to_query), '?'));
        $analyses = [];

        // For non-German locales, try localized table first
        if ($locale != '' && $locale != 'de') {
            $tableName = $locale . '_analyses';

            $stmt = $this->pdo->prepare("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?");
            $stmt->execute([$tableName]);
            $tableExists = $stmt->fetch();

            if ($tableExists) {
                $stmt = $this->pdo->prepare("
                    SELECT T1.*, T2.name as de_name
                    FROM {$tableName} T1
                    JOIN analyses T2 ON T2.id = T1.id
                    JOIN analyse_pools T3 ON T1.id = T3.analyse_id
                    WHERE T3.pool_id IN ({$inClause})
                ");
                $stmt->execute($pool_ids_to_query);
                $rawAnalyses = $stmt->fetchAll();

                foreach ($rawAnalyses as $item) {
                    $item->pools = $this->getPoolsForAnalysis(intval($item->id), $pool_ids_to_query);
                    $analyses[] = $item;
                }
            }
        }

        // Default case: use the base 'analyses' table (German/default) if no analyses found
        if (empty($analyses)) {
            $this->logDebug("GoCalcService - Using default analyses table");
            $stmt = $this->pdo->prepare("
                SELECT T1.* FROM analyses T1
                JOIN analyse_pools T2 ON T1.id = T2.analyse_id
                WHERE T2.pool_id IN ({$inClause})
            ");
            $stmt->execute($pool_ids_to_query);
            $rawAnalyses = $stmt->fetchAll();

            foreach ($rawAnalyses as $item) {
                $item->pools = $this->getPoolsForAnalysis(intval($item->id), $pool_ids_to_query);
                $analyses[] = $item;
            }
        }

        $this->logDebug("GoCalcService getAnalysesData END - returning " . count($analyses) . " analyses.");
        return $analyses;
    }

    /**
     * Retrieves pools associated with a specific analysis.
     *
     * @param int $analysisId
     * @param array $pool_ids_to_query
     * @return array
     */
    private function getPoolsForAnalysis(int $analysisId, array $pool_ids_to_query): array
    {
        $inClause = implode(',', array_fill(0, count($pool_ids_to_query), '?'));
        $stmt = $this->pdo->prepare("
            SELECT T1.* FROM pools T1
            JOIN analyse_pools T2 ON T1.id = T2.pool_id
            WHERE T2.analyse_id = ? AND T1.id IN ({$inClause})
        ");
        $params = array_merge([$analysisId], $pool_ids_to_query);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }

    /**
     * Prepares input data for the Go program.
     *
     * @param $analyses
     * @param $user
     * @param $filterId
     * @param $productColors
     * @param $subid
     * @param $todayCause
     * @param $randomCauses
     * @param $random_values
     * @param $proid
     * @return array
     */
    protected function prepareInputData($analyses, $user, $filterId, $productColors, $subid, $todayCause, $randomCauses, $random_values, $proid)
    {
        $this->logDebug("GoCalcService prepareInputData START");
        $colorSettings = $this->prepareColorSettings($productColors, $user->id);
        $analysesData = [];
        foreach ($analyses as $analysis) {
            $locale = $this->locale;
            $calcName = $analysis->name;
            $displayName = $analysis->name;

            if ($locale != '' && $locale != 'de') {
                $calcName = $analysis->de_name ?? $analysis->name;
            }

            $analysesData[] = [
                'id' => intval($analysis->id),
                'name' => $calcName,
                'display_name' => $displayName,
                'bioryth' => intval($analysis->bioryth ?? 28),
                'body_desc' => $analysis->body_desc ?? '',
                'mental_desc' => $analysis->mental_desc ?? '',
                'description' => $analysis->description ?? '',
                'desc_image' => $analysis->desc_image ?? '',
                'url_name' => $analysis->url_name ?? '',
                'url_link' => $analysis->url_link ?? '',
                'pool_id' => !empty($analysis->pools) ? intval($analysis->pools[0]->id) : 0,
            ];
        }

        $priceValues = (object)[];
        if ($proid && $subid) {
            foreach ($analyses as $analysis) {
                $priceValue = $this->priceValue(intval($proid), intval($subid), intval($analysis->id));
                $key = strval($analysis->id);
                $priceValues->$key = [
                    'min_price' => floatval($priceValue['min_price']),
                    'max_price' => floatval($priceValue['max_price']),
                ];
            }
        }
        $this->logDebug("prepareInputData - Price values for Go: " . json_encode($priceValues));

        $formattedSavedCauses = $this->formatCausesForGo($todayCause);
        $formattedRandomCauses = $this->formatRandomCausesForGo($randomCauses);

        $randomValuesObject = (object)[];
        if (!empty($random_values) && is_array($random_values)) {
            foreach ($random_values as $key => $value) {
                $randomValuesObject->{strval($key)} = intval($value);
            }
        }

        $input = [
            'user' => [
                'id' => intval($user->id),
                'first_name' => $user->first_name ?? '',
                'last_name' => $user->last_name ?? '',
                'gebdatum' => $user->gebdatum ?? '',
                'gebort' => $user->gebort ?? '',
                'thema_speichern' => $user->thema_speichern ?? '',
                'datumcore' => $user->datumcore ?? '',
                'calculation_with' => intval($user->calculation_with ?? 0),
                'biorythm' => intval($user->biorythm ?? 0),
                'bioryths' => intval($user->bioryths ?? 0),
                'ran_ana' => isset($user->useroption->ran_ana) ? intval($user->useroption->ran_ana) : 0,
                'pattern_switch' => isset($user->useroption->pattern_switch) ? intval($user->useroption->pattern_switch) : 0,
            ],
            'analyses' => $analysesData,
            'bioryth_system' => $this->biorythSystem,
            'color_settings' => $colorSettings,
            'random_values' => $randomValuesObject,
            'saved_causes' => $formattedSavedCauses,
            'random_causes' => $formattedRandomCauses,
            'price_values' => $priceValues,
            'subid' => intval($subid),
            'proid' => intval($proid),
            'filter_type' => isset($user->userfilter->filter_type) ? intval($user->userfilter->filter_type) : 1,
            'locale' => $this->locale,
            'current_date' => date('Y-m-d'),
            'debug_mode' => $this->debugMode,
        ];
        $this->logDebug("prepareInputData END - Final input array for Go: " . json_encode($input));
        return $input;
    }

    /**
     * Retrieves random values for a user.
     *
     * @param int $userid
     * @return array
     */
    protected function randomValue(int $userid): array
    {
        $this->logDebug("GoCalcService randomValue START for user: {$userid}");
        $stmt = $this->pdo->prepare("
            SELECT T1.ran_value, T2.analyse_id
            FROM save_calculations T1
            JOIN analyse_save_calculations T2 ON T2.save_calculation_id = T1.id
            WHERE T1.user_id = ?
        ");
        $stmt->execute([$userid]);
        $ranValue = $stmt->fetchAll();

        $ran_values = [];
        if (!empty($ranValue)) {
            foreach ($ranValue as $val) {
                $ran_values[intval($val->analyse_id)] = intval($val->ran_value);
            }
        }
        $this->logDebug("GoCalcService randomValue END - returning: " . json_encode($ran_values));
        return $ran_values;
    }

    /**
     * Executes the Go batch program.
     *
     * @param array $input
     * @return array
     */
    private function executeBatchGoProgram(array $input): array
    {
        $jsonInput = json_encode($input, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_PRESERVE_ZERO_FRACTION);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->logError("Failed to encode JSON input for Go program: " . json_last_error_msg());
            return ['success' => false, 'error' => 'Failed to encode JSON input: ' . json_last_error_msg()];
        }

        if (getenv('APP_DEBUG') === 'true') {
            $this->logDebug("Go program input JSON", ['input' => $jsonInput]);
        }

        $tempFile = tempnam(sys_get_temp_dir(), 'gobatchcalc_');
        if ($tempFile === false) {
            $this->logError('Failed to create temporary file for Go program input.');
            return ['success' => false, 'error' => 'Failed to create temporary file.'];
        }
        file_put_contents($tempFile, $jsonInput);
        $this->logDebug("GoCalcService - Input JSON written to temp file: {$tempFile}");

        try {
            $command = escapeshellcmd($this->goBatchBinaryPath) . ' < ' . escapeshellarg($tempFile);
            $this->logDebug("GoCalcService - Executing command: {$command}");

            $env = $_ENV;
            $env['LANG'] = 'en_US.UTF-8';
            $env['LC_ALL'] = 'en_US.UTF-8';

            $descriptorspec = [
                0 => ['file', $tempFile, 'r'],
                1 => ['pipe', 'w'],
                2 => ['pipe', 'w'],
            ];

            $process = proc_open($command, $descriptorspec, $pipes, null, $env);

            if (is_resource($process)) {
                $output = stream_get_contents($pipes[1]);
                $errors = stream_get_contents($pipes[2]);
                fclose($pipes[1]);
                fclose($pipes[2]);
                $return_value = proc_close($process);

                $this->logDebug("GoCalcService - Go program stdout: " . substr($output, 0, 500) . (strlen($output) > 500 ? '...' : ''));
                $this->logDebug("GoCalcService - Go program stderr: " . $errors);
                $this->logDebug("GoCalcService - Go program exit code: {$return_value}");


                if ($return_value !== 0) {
                    $this->logError("Go program returned non-zero exit code", [
                        'code' => $return_value,
                        'stderr' => $errors
                    ]);
                    return ['success' => false, 'error' => 'Go program execution failed: ' . $errors];
                }
            } else {
                $this->logError('Failed to open process for Go batch program.');
                return ['success' => false, 'error' => 'Failed to execute Go batch program.'];
            }

            $result = json_decode($output, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logError("JSON decode error from Go program output", [
                    'error' => json_last_error_msg(),
                    'output_sample' => substr($output, 0, 500)
                ]);
                return ['success' => false, 'error' => 'Invalid JSON response from Go program: ' . json_last_error_msg()];
            }

            $this->logInfo("Go program execution successful", [
                'mode' => $result['mode'] ?? 'unknown',
                'total_days' => $result['total_days'] ?? 0,
                'results_count' => isset($result['results']) ? count($result['results']) : 0,
            ]);

            return ['success' => true, 'data' => $result];

        } catch (\Exception $e) {
            $this->logError("Exception during Go program execution", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return ['success' => false, 'error' => $e->getMessage()];
        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
                $this->logDebug("GoCalcService - Deleted temporary file: {$tempFile}");
            }
        }
    }

    /**
     * Formats the batch results from the Go program.
     *
     * @param array $batchOutput
     * @param array $bodyImages
     * @param array $mentalImages
     * @param $subid
     * @return array
     */
    private function formatBatchResults(array $batchOutput, array $bodyImages, array $mentalImages, $subid = null): array
    {
        $this->logDebug("GoCalcService formatBatchResults START");
        $formattedResults = [];
        if (isset($batchOutput['results']) && !empty($batchOutput['results'])) {
            foreach ($batchOutput['results'] as $dayResult) {
                if (isset($dayResult['error']) && !empty($dayResult['error'])) {
                    $this->logWarning("Day calculation error from Go output", [
                        'date' => $dayResult['date'] ?? 'unknown',
                        'error' => $dayResult['error']
                    ]);
                    continue;
                }
                $allAnalyses = $dayResult['output']['analyses'] ?? [];
                $dayData = [
                    'analyses' => array_values($allAnalyses),
                    'bodyimages' => $bodyImages,
                    'mentalimages' => $mentalImages,
                    'red_count' => $dayResult['output']['red_count'] ?? 0,
                    'orange_count' => $dayResult['output']['orange_count'] ?? 0,
                    'green_count' => $dayResult['output']['green_count'] ?? 0,
                ];
                if ($subid && intval($subid) === 20) {
                    $dayData['anaids'] = array_column($allAnalyses, 'anaid');
                }
                $formattedResults[] = $dayData;
            }
        }
        $this->logDebug("GoCalcService formatBatchResults END - returning " . count($formattedResults) . " formatted results.");
        return $formattedResults;
    }

    /**
     * Returns an empty result array.
     *
     * @param int $days
     * @return array
     */
    private function getEmptyResult(int $days): array
    {
        $emptyDay = [
            'analyses' => [],
            'bodyimages' => [],
            'mentalimages' => [],
            'red_count' => 0,
            'orange_count' => 0,
            'green_count' => 0,
        ];

        if ($days === 1) {
            return $emptyDay;
        }

        return array_fill(0, $days, $emptyDay);
    }

    /**
     * Parses the PHP memory limit string into megabytes.
     *
     * @param string $memoryLimit
     * @return int
     */
    private function parseMemoryLimit(string $memoryLimit): int
    {
        $memoryLimit = strtolower($memoryLimit);
        $value = intval($memoryLimit);
        if (strpos($memoryLimit, 'g') !== false) {
            return $value * 1024;
        } elseif (strpos($memoryLimit, 'm') !== false) {
            return $value;
        } elseif (strpos($memoryLimit, 'k') !== false) {
            return intval($value / 1024);
        }
        return 128;
    }

    /**
     * Determines the number of concurrent workers based on the number of days.
     *
     * @param int $days
     * @return int
     */
    private function determineMemoryEfficientWorkers(int $days): int
    {
        if ($days <= 20) {
            return 4;
        } elseif ($days <= 40) {
            return 6;
        } elseif ($days <= 60) {
            return 8;
        } else {
            return 10;
        }
    }

    /**
     * Prepares color settings based on product or global settings.
     *
     * @param $productColors
     * @param int|null $userId
     * @return array
     */
    protected function prepareColorSettings($productColors, $userId = null)
    {
        $this->logDebug("GoCalcService prepareColorSettings START");
        if ($productColors) {
            $settings = [
                'type' => 'product',
                'red_min' => intval($productColors->red_min ?? 0),
                'red_max' => intval($productColors->red_max ?? 33),
                'orange_min' => intval($productColors->orange_min ?? 34),
                'orange_max' => intval($productColors->orange_max ?? 66),
                'green_min' => intval($productColors->green_min ?? 67),
                'green_max' => intval($productColors->green_max ?? 100),
                'custom_min' => $productColors->custom_min !== null ? intval($productColors->custom_min) : null,
                'custom_max' => $productColors->custom_max !== null ? intval($productColors->custom_max) : null,
                'custom_color' => $productColors->custom_color ?? null,
            ];
            $this->logDebug("GoCalcService prepareColorSettings END - Product settings: " . json_encode($settings));
            return $settings;
        }

        $globalSettings = $this->global_settings($userId);
        if ($globalSettings) {
            $settings = [
                'type' => 'global',
                'red_min' => intval($globalSettings->gs_red_min ?? 0),
                'red_max' => intval($globalSettings->gs_red_max ?? 33),
                'orange_min' => intval($globalSettings->gs_orange_min ?? 34),
                'orange_max' => intval($globalSettings->gs_orange_max ?? 66),
                'green_min' => intval($globalSettings->gs_green_min ?? 67),
                'green_max' => intval($globalSettings->gs_green_max ?? 100),
            ];
            $this->logDebug("GoCalcService prepareColorSettings END - Global settings: " . json_encode($settings));
            return $settings;
        }

        $settings = [
            'type' => 'default',
            'red_min' => 0,
            'red_max' => 33,
            'orange_min' => 34,
            'orange_max' => 66,
            'green_min' => 67,
            'green_max' => 100,
        ];
        $this->logDebug("GoCalcService prepareColorSettings END - Default settings: " . json_encode($settings));
        return $settings;
    }

    /**
     * Formats saved causes for the Go program.
     *
     * @param array $savedCauses
     * @return array
     */
    protected function formatCausesForGo(array $savedCauses): array
    {
        $this->logDebug("GoCalcService formatCausesForGo START");
        $formatted = [
            'causes' => (object)[],
            'medium' => (object)[],
            'tipp' => (object)[]
        ];

        if (empty($savedCauses)) {
            $this->logDebug("GoCalcService formatCausesForGo END - Empty saved causes.");
            return $formatted;
        }

        foreach (['causes', 'medium', 'tipp'] as $type) {
            if (isset($savedCauses[$type]) && is_array($savedCauses[$type])) {
                $formatted[$type] = (object)[];
                foreach ($savedCauses[$type] as $analysisId => $cause) {
                    $key = strval($analysisId);
                    // Check if $cause is an object and has an 'id' property, otherwise assume it's the ID itself.
                    $id = is_object($cause) ? intval($cause->id) : intval($cause);
                    $formatted[$type]->$key = $id;
                }
            }
        }
        $this->logDebug("GoCalcService formatCausesForGo END - Formatted: " . json_encode($formatted));
        return $formatted;
    }

    /**
     * Formats random causes for the Go program.
     *
     * @param array $randomCauses
     * @return array
     */
    protected function formatRandomCausesForGo(array $randomCauses): array
    {
        $this->logDebug("GoCalcService formatRandomCausesForGo START");
        $formatted = [
            'causes' => (object)[],
            'medium' => (object)[],
            'tipp' => (object)[]
        ];
        if (empty($randomCauses)) {
            $this->logDebug("GoCalcService formatRandomCausesForGo END - Empty random causes.");
            return $formatted;
        }

        foreach (['causes', 'medium', 'tipp'] as $type) {
            if (isset($randomCauses[$type]) && is_array($randomCauses[$type])) {
                $formatted[$type] = (object)[];
                $items = $randomCauses[$type];
                foreach ($items as $item) {
                    $id = is_object($item) ? intval($item->id) : intval($item);
                    $key = strval($id);
                    $formatted[$type]->$key = $id;
                }
            }
        }
        $this->logDebug("GoCalcService formatRandomCausesForGo END - Formatted: " . json_encode($formatted));
        return $formatted;
    }

    /**
     * Retrieves user and filter data.
     *
     * @param int $userid
     * @return object
     */
    protected function userFilterid(int $userid): object
    {
        $this->logDebug("GoCalcService userFilterid START for user: {$userid}");
        $stmt = $this->pdo->prepare("
            SELECT T1.*, T2.filter_type, T3.ran_ana, T3.pattern_switch
            FROM users T1
            LEFT JOIN user_filters T2 ON T1.id = T2.user_id
            LEFT JOIN user_options T3 ON T1.id = T3.user_id
            WHERE T1.id = ?
        ");
        $stmt->execute([$userid]);
        $user = $stmt->fetch();

        if (!$user) {
            // Handle case where user is not found
            $this->logError("User with ID {$userid} not found.");
            return (object)['id' => $userid, 'userfilter' => (object)['filter_type' => 1], 'useroption' => (object)['ran_ana' => 0, 'pattern_switch' => 0]];
        }

        // Manual object structure replication
        $userObj = (object)[];
        foreach($user as $key => $value) {
            $userObj->$key = $value;
        }

        $userObj->userfilter = (object)[ 'filter_type' => $user->filter_type ?? 1 ];
        $userObj->useroption = (object)[
            'ran_ana' => $user->ran_ana ?? 0,
            'pattern_switch' => $user->pattern_switch ?? 0
        ];
        $this->logDebug("GoCalcService userFilterid END - User object fetched: " . json_encode($userObj));
        return $userObj;
    }

    /**
     * Retrieves biorythm visible details from the database.
     *
     * @return object
     */
    private function biorythVisibleDetails(): object
    {
        $this->logDebug("GoCalcService biorythVisibleDetails START");
        $cacheFile = __DIR__ . '/../cache/global_settings.cache';
        if (file_exists($cacheFile) && (filemtime($cacheFile) > (time() - 3600))) {
            $settings = unserialize(file_get_contents($cacheFile));
            $this->logDebug("GoCalcService biorythVisibleDetails END - From cache: " . json_encode($settings));
            return $settings;
        }

        $stmt = $this->pdo->prepare("SELECT * FROM global_settings WHERE gs_type = 'other'");
        $stmt->execute();
        $settings = $stmt->fetch();

        if ($settings) {
            @mkdir(dirname($cacheFile), 0777, true);
            file_put_contents($cacheFile, serialize($settings));
        }
        $this->logDebug("GoCalcService biorythVisibleDetails END - From DB: " . json_encode($settings));
        return $settings;
    }

    /**
     * Retrieves pool IDs for a given submenu.
     *
     * @param int $subid
     * @return array
     */
    protected function getPoolIdsBySubID(int $subid): array
    {
        $this->logDebug("GoCalcService getPoolIdsBySubID START for subid: {$subid}");
        $stmt = $this->pdo->prepare("SELECT pool_id FROM pool_submenus WHERE submenu_id = ?");
        $stmt->execute([$subid]);
        $poolIds = $stmt->fetchAll(\PDO::FETCH_COLUMN, 0);
        $this->logDebug("GoCalcService getPoolIdsBySubID END - Pool IDs: " . json_encode($poolIds));
        return $poolIds;
    }

    /**
     * Retrieves body images for a list of analysis IDs.
     *
     * @param array $analysisIds
     * @return array
     */
    protected function getBodyImage(array $analysisIds): array
    {
        $this->logDebug("GoCalcService getBodyImage START for analysisIds count: " . count($analysisIds));
        if (empty($analysisIds)) {
            return [];
        }
        $inClause = implode(',', array_fill(0, count($analysisIds), '?'));
        $stmt = $this->pdo->prepare("SELECT * FROM body_images WHERE analyse_id IN ({$inClause})");
        $stmt->execute($analysisIds);
        $results = $stmt->fetchAll();
        $keyedResults = [];
        foreach ($results as $result) {
            $keyedResults[$result->analyse_id] = $result;
        }
        $this->logDebug("GoCalcService getBodyImage END - returning " . count($keyedResults) . " images.");
        return $keyedResults;
    }

    /**
     * Retrieves mental images for a list of analysis IDs.
     *
     * @param array $analysisIds
     * @return array
     */
    protected function getMentalImage(array $analysisIds): array
    {
        $this->logDebug("GoCalcService getMentalImage START for analysisIds count: " . count($analysisIds));
        if (empty($analysisIds)) {
            return [];
        }
        $inClause = implode(',', array_fill(0, count($analysisIds), '?'));
        $stmt = $this->pdo->prepare("SELECT * FROM mental_images WHERE analyse_id IN ({$inClause})");
        $stmt->execute($analysisIds);
        $results = $stmt->fetchAll();
        $keyedResults = [];
        foreach ($results as $result) {
            $keyedResults[$result->analyse_id] = $result;
        }
        $this->logDebug("GoCalcService getMentalImage END - returning " . count($keyedResults) . " images.");
        return $keyedResults;
    }

    /**
     * Retrieves saved causes for a user, submenu, and analyses.
     *
     * @param int $subid
     * @param array $anaid
     * @param int $userid
     * @return array
     */
    protected function savedCauses(int $subid, array $anaid, int $userid): array
    {
        $date = date("Y-m-d");
        $tableName = ($this->locale == '' || $this->locale == 'de') ? 'causes' : $this->locale . '_causes';
        $inClause = implode(',', array_fill(0, count($anaid), '?'));
        $params = array_merge([$userid, $date, $subid], $anaid);

        $stmt = $this->pdo->prepare("
            SELECT
                rc.*,
                arc.analyse_id
            FROM
                record_causes AS rc
            JOIN
                analyse_record_causes AS arc ON arc.record_cause_id = rc.id
            JOIN
                submenu_record_causes AS src ON src.record_cause_id = rc.id
            LEFT JOIN
                {$tableName} AS t ON t.id = rc.causes_id
            WHERE
                rc.user_id = ?
                AND rc.date = ?
                AND src.submenu_id = ?
                AND arc.analyse_id IN ({$inClause})
        ");
        $stmt->execute($params);
        $todayCause = $stmt->fetchAll(\PDO::FETCH_OBJ);

        $cauids = [];
        $midids = [];
        $tipids = [];

        foreach ($todayCause as $cause) {
            if (isset($cause->type)) {
                if ($cause->type == "Causes") {
                    $cauids[$cause->analyse_id] = $cause;
                } elseif ($cause->type == "Medium") {
                    $midids[$cause->analyse_id] = $cause;
                } elseif ($cause->type == "Tipp") {
                    $tipids[$cause->analyse_id] = $cause;
                }
            }
        }

        return [
            'causes' => $cauids,
            'medium' => $midids,
            'tipp' => $tipids
        ];
    }

    /**
     * Retrieves random causes for a submenu.
     *
     * @param int $subid
     * @return array
     */
    protected function randomCauses(int $subid): array
    {
        $this->logDebug("GoCalcService randomCauses START for subid: {$subid}");
        $tableName = ($this->locale == '' || $this->locale == 'de') ? 'causes' : $this->locale . '_causes';

        $stmtCau = $this->pdo->prepare("
            SELECT T1.*, T2.id FROM group_causes T1
            JOIN {$tableName} T2 ON T2.group_id = T1.group_id
            WHERE T1.submenu_id = ?
        ");
        $stmtCau->execute([$subid]);
        $randomCau = $stmtCau->fetchAll();

        $stmtMid = $this->pdo->prepare("
            SELECT T1.*, T2.id FROM group_mediums T1
            JOIN {$tableName} T2 ON T2.group_id = T1.group_id
            WHERE T1.submenu_id = ?
        ");
        $stmtMid->execute([$subid]);
        $randomMid = $stmtMid->fetchAll();

        $stmtTip = $this->pdo->prepare("
            SELECT T1.*, T2.id FROM group_tips T1
            JOIN {$tableName} T2 ON T2.group_id = T1.group_id
            WHERE T1.submenu_id = ?
        ");
        $stmtTip->execute([$subid]);
        $randomTip = $stmtTip->fetchAll();

        $this->logDebug("GoCalcService randomCauses END - returning: " . json_encode(['causes' => $randomCau, 'medium' => $randomMid, 'tipp' => $randomTip]));
        return [
            'causes' => $randomCau,
            'medium' => $randomMid,
            'tipp' => $randomTip
        ];
    }

    /**
     * Retrieves price values based on product, submenu, or analysis.
     *
     * @param int|null $proid
     * @param int|null $subid
     * @param int|null $anaid
     * @return array
     */
    protected function priceValue($proid = null, $subid = null, $anaid = null): array
    {
        $this->logDebug("GoCalcService priceValue START for proid: {$proid}, subid: {$subid}, anaid: {$anaid}");
        $anaPrice = [];
        $subPrice = [];
        $productPrice = [];
        $globalPrice = [];

        if ($anaid) {
            $stmt = $this->pdo->prepare("SELECT ana_min_price, ana_max_price FROM analyses WHERE id = ? AND ana_min_price != '' AND ana_max_price != ''");
            $stmt->execute([$anaid]);
            $anaPrice = $stmt->fetch();
            if ($anaPrice) {
                $this->logDebug("GoCalcService priceValue END - Found analysis price: " . json_encode($anaPrice));
                return ['min_price' => $anaPrice->ana_min_price, 'max_price' => $anaPrice->ana_max_price];
            }
        }

        if ($subid) {
            $stmt = $this->pdo->prepare("SELECT sub_min, sub_max FROM submenus WHERE id = ? AND sub_min != '' AND sub_max != ''");
            $stmt->execute([$subid]);
            $subPrice = $stmt->fetch();
            if ($subPrice) {
                $this->logDebug("GoCalcService priceValue END - Found submenu price: " . json_encode($subPrice));
                return ['min_price' => $subPrice->sub_min, 'max_price' => $subPrice->sub_max];
            }
        }

        if ($proid) {
            $stmt = $this->pdo->prepare("SELECT min_price, max_price FROM product_settings WHERE product_id = ? AND min_price != '' AND max_price != ''");
            $stmt->execute([$proid]);
            $productPrice = $stmt->fetch();
            if ($productPrice) {
                $this->logDebug("GoCalcService priceValue END - Found product price: " . json_encode($productPrice));
                return ['min_price' => $productPrice->min_price, 'max_price' => $productPrice->max_price];
            }
        }

        $stmt = $this->pdo->prepare("SELECT gs_min_price, gs_max_price FROM global_settings WHERE gs_type = 'other'");
        $stmt->execute();
        $globalPrice = $stmt->fetch();
        $this->logDebug("GoCalcService priceValue END - Found global price: " . json_encode($globalPrice));

        return ['min_price' => $globalPrice->gs_min_price ?? 10, 'max_price' => $globalPrice->gs_max_price ?? 40];
    }

    /**
     * Retrieves global settings.
     *
     * @param int $userId
     * @return object|false
     */
    private function global_settings(int $userId): object|false
    {
        $this->logDebug("GoCalcService global_settings START for userId: {$userId}");
        $stmt = $this->pdo->prepare("SELECT * FROM global_settings WHERE gs_userid = ? OR id = 1 LIMIT 1");
        $stmt->execute([$userId]);
        $settings = $stmt->fetch();
        $this->logDebug("GoCalcService global_settings END - returning: " . json_encode($settings));
        return $settings;
    }

    /**
     * Logs a message with a specific level.
     *
     * @param string $level
     * @param string $message
     * @param array $context
     */
    private function log(string $level, string $message, array $context = []): void
    {
        if (!$this->debugMode) {
            return;
        }

        if (!is_dir($this->logPath)) {
            @mkdir($this->logPath, 0777, true);
        }

        $logFile = $this->logPath . date('Y-m-d') . '-app.log';
        $logMessage = '[' . date('Y-m-d H:i:s.u') . '] ' . strtoupper($level) . ': ' . $message . ' ' . json_encode($context, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . PHP_EOL;
        file_put_contents($logFile, $logMessage, FILE_APPEND);
    }

    /**
     * Logs an info message.
     *
     * @param string $message
     * @param array $context
     */
    private function logInfo(string $message, array $context = []): void
    {
        $this->log('info', $message, $context);
    }

    /**
     * Logs a debug message.
     *
     * @param string $message
     * @param array $context
     */
    private function logDebug(string $message, array $context = []): void
    {
        $this->log('debug', $message, $context);
    }

    /**
     * Logs an error message.
     *
     * @param string $message
     * @param array $context
     */
    private function logError(string $message, array $context = []): void
    {
        $this->log('error', $message, $context);
    }

    /**
     * Logs a warning message.
     *
     * @param string $message
     * @param array $context
     */
    private function logWarning(string $message, array $context = []): void
    {
        $this->log('warning', $message, $context);
    }
}