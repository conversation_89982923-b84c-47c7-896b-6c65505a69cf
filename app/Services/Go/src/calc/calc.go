// Package calc provides calculation functionality for analyses
package calc

import (
	"encoding/json" // Added for formatAnalysisResultForLog
	"fmt"
	"log" // Import log package for file logging
	"math"
	"math/rand"
	"os" // Import os for file operations
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time" // Import the time package for date and time operations
)

// debugLoggingEnabled controls whether logging to file is active for the entire 'calc' package.
// It is set by the ProcessCalculation function based on the input.
var debugLoggingEnabled bool

// Input structures - exported for use by batch_calc
// These structs define the expected JSON input for the Go calculator.
type Input struct {
	Mode string `json:"mode"` // Calculation mode: "single", "frequency", or "batch" (default)
	User User `json:"user"` // User data for calculations
	Analyses []Analysis `json:"analyses"` // List of analyses to be processed
	BiorythSystem map[string]int `json:"bioryth_system"` // Default biorythm system values (e.g., {"m": 28, "p": 28})
	ColorSettings ColorSettings `json:"color_settings"` // Color range settings for analysis results
	RandomValues map[int]int `json:"random_values"` // Predefined random values for specific analysis IDs (if pattern_switch is on)
	SavedCauses CausesData `json:"saved_causes"` // Saved causes/medium/tips data
	RandomCauses CausesData `json:"random_causes"` // Random causes/medium/tips data
	PriceValues map[string]PriceValue `json:"price_values"` // Price ranges for products
	SubID *int `json:"subid"` // Submenu ID (pointer to allow nil for optionality)
	ProID *int `json:"proid"` // Product ID (pointer to allow nil for optionality)
	FilterType int `json:"filter_type"` // Filter type for sorting results
	Locale string `json:"locale"` // Locale string (e.g., "en", "de")
	CurrentDate string `json:"current_date"` // Current date string (YYYY-MM-DD), primarily for batch calculations
	UserString string `json:"user_string"` // Concatenated user string for single calculations
	AnalysisString string `json:"analysis_string"` // Analysis name string for single/frequency calculations
	Bioryth int `json:"bioryth"` // Biorythm value for single calculation mode
	DebugMode bool `json:"debug_mode"` // New: Flag to enable/disable detailed logging
}

// PriceValue defines the min/max prices for an analysis product.
type PriceValue struct {
	MinPrice *float64 `json:"min_price"` // Minimum price (pointer for optionality)
	MaxPrice *float64 `json:"max_price"` // Maximum price (pointer for optionality)
}

// User defines user-specific data used in calculations.
type User struct {
	ID int `json:"id"`
	FirstName string `json:"first_name"`
	LastName string `json:"last_name"`
	Gebdatum string `json:"gebdatum"` // Birth date (YYYY-MM-DD)
	Gebort string `json:"gebort"` // Birth place
	ThemaSpeichern string `json:"thema_speichern"` // User's personal theme string
	Datumcore string `json:"datumcore"` // Core date for calculation (YYYY-MM-DD, can be empty or "0000-00-00" for current date)
	CalculationWith int `json:"calculation_with"` // Flag for calculation type (e.g., 1 for monthly, 0 for yearly)
	Biorythm int `json:"biorythm"` // User's biorythm setting for 'm' (mental)
	Bioryths int `json:"bioryths"` // User's biorythm setting for 'p' (physical/symptom)
	RanAna int `json:"ran_ana"` // Flag to use random analysis values (1 for random, 0 for calculated)
	PatternSwitch int `json:"pattern_switch"` // Flag to use predefined random values (from RandomValues map)
}

// Analysis defines details for a specific analysis.
type Analysis struct {
	ID int `json:"id"`
	Name string `json:"name"` // Internal name of the analysis
	DisplayName string `json:"display_name"` // Display name for the analysis
	Bioryth int `json:"bioryth"` // Biorythm value specific to this analysis
	BodyDesc string `json:"body_desc"` // Description for the body aspect
	MentalDesc string `json:"mental_desc"` // Description for the mental aspect
	Description string `json:"description"` // General description
	DescImage string `json:"desc_image"` // Image URL for description
	URLName string `json:"url_name"` // URL friendly name
	URLLink string `json:"url_link"` // External URL link
	PoolID int `json:"pool_id"` // Associated pool ID
}

// ColorSettings defines the ranges for red, orange, green, and custom colors.
type ColorSettings struct {
	Type string `json:"type"` // Type of color settings: "product", "global", or "default"
	RedMin int `json:"red_min"`
	RedMax int `json:"red_max"`
	OrangeMin int `json:"orange_min"`
	OrangeMax int `json:"orange_max"`
	GreenMin int `json:"green_min"`
	GreenMax int `json:"green_max"`
	CustomMin *int `json:"custom_min"` // Custom range min (pointer for optionality)
	CustomMax *int `json:"custom_max"` // Custom range max (pointer for optionality)
	CustomColor *string `json:"custom_color"` // Custom color hex code (pointer for optionality)
}

// CausesData holds maps for different types of causes/tips.
type CausesData struct {
	Causes map[string]int `json:"causes"` // Map of analysis ID to cause ID
	Medium map[string]int `json:"medium"` // Map of analysis ID to medium ID
	Tipp map[string]int `json:"tipp"` // Map of analysis ID to tip ID
}

// Output structures - exported for use by batch_calc
// AnalysisResult represents the calculated output for a single analysis.
type AnalysisResult struct {
	AnaVal int `json:"anaVal"` // Main analysis calculated value (0-100)
	AnaID int `json:"anaid"` // Analysis ID
	AnaName string `json:"anaName"` // Analysis display name
	URLName *string `json:"url_name"` // URL friendly name (pointer for optionality)
	URLLink *string `json:"url_link"` // External URL link (pointer for optionality)
	Desc *string `json:"desc"` // Description (pointer for optionality)
	DescImg *string `json:"desc_img"` // Description image (pointer for optionality)
	BodyDesc *string `json:"bodyDesc"` // Body description (pointer for optionality)
	MentalDesc *string `json:"mentalDesc"` // Mental description (pointer for optionality)
	Bioyrth int `json:"bioyrth"` // Biorythm value used for this analysis
	AnaColor string `json:"anaColor"` // Determined color based on AnaVal
	MaleVal int `json:"maleVal"` // Calculated "male" value
	HeartVal int `json:"heartVal"` // Calculated "heart" value
	MaleColor string `json:"maleColor"` // Color for male value
	HeartColor string `json:"heartColor"` // Color for heart value
	Beergod int `json:"beergod"` // "Beergod" value
	Causes int `json:"causes"` // Cause ID
	Medium int `json:"medium"` // Medium ID
	Tipp int `json:"tipp"` // Tipp ID
	PoolID int `json:"poolid"` // Pool ID
	RandPrice int `json:"randPrice"` // Randomly generated price
	RanValStatus int `json:"ranValStatus"` // Status indicating if a random value was used (1 if used, 0 otherwise)
}

// Output represents the overall result structure, especially for batch mode.
type Output struct {
	Analyses []AnalysisResult `json:"analyses"` // List of all analysis results
	RedCount int `json:"red_count"` // Count of analyses in the red range
	OrangeCount int `json:"orange_count"` // Count of analyses in the orange range
	GreenCount int `json:"green_count"` // Count of analyses in the green range
	Value float64 `json:"value,omitempty"` // Result value for single calculation mode (omitempty hides if zero)
	Frequency int `json:"frequency,omitempty"` // Result frequency for frequency calculation mode (omitempty hides if zero)
}

// charMatrix is a mapping from characters (including German umlauts and ß) to their string representations for numerical conversion.
// This matrix is consistent with the PHP implementation (Matrix V1.1).
var charMatrix = map[rune]string{
	'a': "1", 'b': "2", 'c': "3", 'd': "4", 'e': "5", 'f': "6", 'g': "7", 'h': "8", 'i': "9",
	'j': "1", 'k': "2", 'l': "3", 'm': "4", 'n': "5", 'o': "6", 'p': "7", 'q': "8", 'r': "9",
	's': "1", 't': "2", 'u': "3", 'v': "4", 'w': "5", 'x': "6", 'y': "7", 'z': "8",
	'A': "1", 'B': "2", 'C': "3", 'D': "4", 'E': "5", 'F': "6", 'G': "7", 'H': "8", 'I': "9",
	'J': "1", 'K': "2", 'L': "3", 'M': "4", 'N': "5", 'O': "6", 'P': "7", 'Q': "8", 'R': "9",
	'S': "1", 'T': "2", 'U': "3", 'V': "4", 'W': "5", 'X': "6", 'Y': "7", 'Z': "8",
	'ß': "11", 'ä': "15", 'ü': "35", 'ö': "65", 'Ä': "15", 'Ü': "35", 'Ö': "65",
}

// replacer is initialized once to efficiently perform multiple string replacements.
var replacer *strings.Replacer

func init() {
	// Create a slice of strings for strings.NewReplacer.
	// This needs pairs of (old, new) strings.
	oldNew := make([]string, 0, len(charMatrix)*2)
	for r, s := range charMatrix {
		oldNew = append(oldNew, string(r), s)
	}
	replacer = strings.NewReplacer(oldNew...)
}

// CalcWorker struct holds shared resources for concurrent analysis processing.
type CalcWorker struct {
	wg *sync.WaitGroup // WaitGroup to synchronize goroutines
	input *Input // Pointer to the main input data
	userString string // Pre-calculated user string for efficiency
	tagUser int // Pre-calculated tagUser value
	results chan<- AnalysisResult // Channel to send results to (send-only)
	redCount *int32 // Atomic counter for red analyses
	orangeCount *int32 // Atomic counter for orange analyses
	greenCount *int32 // Atomic counter for green analyses
	mu *sync.Mutex // Mutex for shared resource protection (if needed, though atomic counters reduce need)
}

// logToFile appends a message to debug_log_go.txt with microsecond precision timestamp.
// It now checks the package-level debugLoggingEnabled flag.
func logToFile(level, message string) {
	if !debugLoggingEnabled { // Only log if debug mode is enabled
		return
	}

	f, err := os.OpenFile("debug_log_go.txt", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		log.Printf("Failed to open log file: %v", err)
		return
	}
	defer f.Close()

	timestamp := time.Now().Format("2006-01-02 15:04:05.000000")
	logMessage := fmt.Sprintf("[%s] %s: %s\n", timestamp, level, message)
	if _, err := f.WriteString(logMessage); err != nil {
		log.Printf("Failed to write to log file: %v", err)
	}
}

// ProcessCalculation is the main exported function for initiating calculations based on the input mode.
// It sets the package-level debugLoggingEnabled flag.
func ProcessCalculation(input *Input) (*Output, error) {
	// Set the package-level debug logging flag based on input
	debugLoggingEnabled = input.DebugMode

	logToFile("INFO", "ProcessCalculation START")
	// REMOVED: rand.Seed(time.Now().UnixNano())
	// Seeding the random number generator is now handled exclusively by the caller (e.g., batch_calc.go's main function).
	// This ensures that if the caller seeds with a fixed value, the results will be repeatable.
	// If the caller seeds with time.Now().UnixNano(), results will still vary between runs,
	// but the randomness will be consistent per batch execution.

	// Handle different calculation modes.
	switch input.Mode {
	case "single":
		return handleSingleCalculation(input)
	case "frequency":
		return handleFrequencyCalculation(input)
	default: // Default to batch mode if no specific mode or "batch" is provided
		return handleBatchCalculation(input)
	}
}

// handleBatchCalculation processes multiple analyses in parallel.
func handleBatchCalculation(input *Input) (*Output, error) {
	logToFile("INFO", "handleBatchCalculation START")
	logToFile("DEBUG", fmt.Sprintf("handleBatchCalculation inputs - Mode: %s, Analyses Count: %d, UserID: %d", input.Mode, len(input.Analyses), input.User.ID))

	// Generate user string and tag user value once for all analyses.
	userString := getUserString(&input.User)
	logToFile("DEBUG", fmt.Sprintf("handleBatchCalculation - strM (before clean): %s", userString))

	tagUser := calculateTagUser(&input.User)
	logToFile("DEBUG", fmt.Sprintf("handleBatchCalculation - tagUser: %d", tagUser))


	// Initialize atomic counters for color categorization.
	var redCount, orangeCount, greenCount int32
	var mu sync.Mutex // Mutex is declared but not strictly necessary with atomic counters for counts.

	// Create a buffered channel for analysis results to avoid blocking goroutines.
	results := make(chan AnalysisResult, len(input.Analyses))

	// Create a WaitGroup to wait for all goroutines to complete.
	var wg sync.WaitGroup

	// Process each analysis in a separate goroutine for parallel execution.
	for _, analysis := range input.Analyses {
		wg.Add(1) // Increment the WaitGroup counter
		go func(ana Analysis) {
			defer wg.Done() // Decrement the WaitGroup counter when the goroutine finishes

			// Create a new CalcWorker for each analysis.
			worker := CalcWorker{
				wg:          &wg,
				input:       input,
				userString:  userString,
				tagUser:     tagUser,
				results:     results,
				redCount:    &redCount,
				orangeCount: &orangeCount,
				greenCount:  &greenCount,
				mu:          &mu, // Mutex passed, but its primary use for counts is replaced by atomic ops.
			}

			// Process the individual analysis.
			worker.processAnalysis(ana)
		}(analysis) // Pass the analysis by value to the goroutine
	}

	// Wait for all analysis goroutines to complete.
	wg.Wait()
	close(results) // Close the results channel once all goroutines are done sending.

	// Collect all results from the channel.
	var analysisResults []AnalysisResult
	for result := range results {
		analysisResults = append(analysisResults, result)
	}

	// Apply sorting to the collected results based on filter type.
	sortResults(analysisResults, input.FilterType, input.SubID)

	logToFile("DEBUG", fmt.Sprintf("handleBatchCalculation - Final counts: Red: %d, Orange: %d, Green: %d", redCount, orangeCount, greenCount))
	logToFile("INFO", "handleBatchCalculation END")

	// Return the final output including analysis results and color counts.
	return &Output{
		Analyses:    analysisResults,
		RedCount:    int(redCount),
		OrangeCount: int(orangeCount),
		GreenCount:  int(greenCount),
	}, nil
}

// processAnalysis performs the core calculations for a single analysis.
func (w *CalcWorker) processAnalysis(analysis Analysis) {
	logToFile("DEBUG", fmt.Sprintf("--- Processing Analysis ID: %d ---", analysis.ID))

	strP := analysis.Name // Analysis name
	logToFile("DEBUG", fmt.Sprintf("Analysis ID %d - strP: %s", analysis.ID, strP))

	bioryth := analysis.Bioryth // Biorythm value for this analysis
	if bioryth <= 0 {
		bioryth = 28 // Default biorythm if not set
	}
	logToFile("DEBUG", fmt.Sprintf("Analysis ID %d - effective bioryth for analysis: %d", analysis.ID, bioryth))


	// Perform core calculations: PS (Seele), PK (Körper), OneDay, and the main calculation.
	beergS := w.calculationPS(w.userString, strP, bioryth)
	logToFile("DEBUG", fmt.Sprintf("Analysis ID %d - beergS (raw): %.20f", analysis.ID, beergS))

	beergK := w.calculationPK(w.userString, strP, bioryth)
	logToFile("DEBUG", fmt.Sprintf("Analysis ID %d - beergK (raw): %.20f", analysis.ID, beergK))

	beerg := 0.0 // Initialize with default
	// If `ran_ana` is 1, use a random value; otherwise, use the calculated value.
	if w.input.User.RanAna == 1 {
		beerg = float64(rand.Intn(101)) // Generate a random integer between 0 and 100
	} else {
		beerg = w.calculation(w.userString, strP, bioryth) // Main calculation
	}
	logToFile("DEBUG", fmt.Sprintf("Analysis ID %d - beerg (raw): %.20f", analysis.ID, beerg))


	beergod := w.calculationOneDay(w.userString, strP, bioryth)
	logToFile("DEBUG", fmt.Sprintf("Analysis ID %d - beergod (raw): %.20f", analysis.ID, beergod))


	// Round calculated values to the nearest integer.
	ma := int(math.Round(beerg))
	beergKRounded := int(math.Round(beergK))
	beergSRounded := int(math.Round(beergS))
	beergodRounded := int(math.Round(beergod))
	logToFile("DEBUG", fmt.Sprintf("Analysis ID %d - rounded ma: %d, beergK: %d, beergS: %d, beergod: %d", analysis.ID, ma, beergKRounded, beergSRounded, beergodRounded))


	ranValStatus := 0
	// If `pattern_switch` is 1 and a predefined random value exists, use it and update status.
	if w.input.User.PatternSwitch == 1 {
		if val, exists := w.input.RandomValues[analysis.ID]; exists {
			ma = val // Override `ma` with the predefined random value
			ranValStatus = 1 // Set status to indicate a random value was used
			logToFile("DEBUG", fmt.Sprintf("Analysis ID %d - ma overridden by random_values: %d", analysis.ID, ma))
		}
	}

	// Determine colors for Heart (beergS) and Male (beergK) values.
	heartColor := "color:gray"
	if beergSRounded >= 50 {
		heartColor = "color:red"
	}

	maleColor := "color:gray"
	if beergKRounded <= 30 {
		maleColor = "color:red"
	}
	logToFile("DEBUG", fmt.Sprintf("Analysis ID %d - heartColor: %s, maleColor: %s", analysis.ID, heartColor, maleColor))


	// Determine the main analysis color.
	anaColor := w.determineColor(ma)
	logToFile("DEBUG", fmt.Sprintf("Analysis ID %d - final anaColor: %s", analysis.ID, anaColor))


	// Get causes, medium, and tips.
	causes := w.getCause(analysis.ID, "causes")
	medium := w.getCause(analysis.ID, "medium")
	tipp := w.getCause(analysis.ID, "tipp")
	logToFile("DEBUG", fmt.Sprintf("Analysis ID %d - causes: %d, medium: %d, tipp: %d", analysis.ID, causes, medium, tipp))


	// Calculate the random price for the analysis.
	randPrice := w.calculatePrice(analysis.ID)
	logToFile("DEBUG", fmt.Sprintf("Analysis ID %d - randPrice: %d", analysis.ID, randPrice))


	// Initialize pointers for optional string fields.
	var urlName, urlLink, desc, descImg, bodyDesc, mentalDesc *string
	if analysis.URLName != "" {
		urlName = &analysis.URLName
	}
	if analysis.URLLink != "" {
		urlLink = &analysis.URLLink
	}
	if analysis.Description != "" {
		desc = &analysis.Description
	}
	if analysis.DescImage != "" {
		descImg = &analysis.DescImage
	}
	if analysis.BodyDesc != "" {
		bodyDesc = &analysis.BodyDesc
	}
	if analysis.MentalDesc != "" {
		mentalDesc = &analysis.MentalDesc
	}

	// Construct the AnalysisResult struct.
	result := AnalysisResult{
		AnaVal: ma,
		AnaID: analysis.ID,
		AnaName: analysis.DisplayName,
		URLName: urlName,
		URLLink: urlLink,
		Desc: desc,
		DescImg: descImg,
		BodyDesc: bodyDesc,
		MentalDesc: mentalDesc,
		Bioyrth: bioryth,
		AnaColor: anaColor,
		MaleVal: beergKRounded,
		HeartVal: beergSRounded,
		MaleColor: maleColor,
		HeartColor: heartColor,
		Beergod: beergodRounded,
		Causes: causes,
		Medium: medium,
		Tipp: tipp,
		PoolID: analysis.PoolID,
		RandPrice: randPrice,
		RanValStatus: ranValStatus,
	}

	logToFile("DEBUG", fmt.Sprintf("Analysis ID %d - allAnalyses entry: %s", analysis.ID, formatAnalysisResultForLog(result)))

	// Send the result to the results channel.
	w.results <- result
}

// tagReplicated calculates a replicated tag value based on a biorythm.
// This function is analogous to PHP's `tag` function.
func (w *CalcWorker) tagReplicated(bioryth int) float64 {
	tagm := float64(w.tagUser) // Cast tagUser to float64 for division
	tagmbe := tagm / float64(bioryth) // Perform division

	// Mimicking PHP's explode(".", $float) which implicitly truncates the float
	// to its integer part for the first array element.
	// This aims to align Go's behavior more closely with PHP's potential precision loss
	// when converting a float to a string and then taking only the integer part.
	intPart := math.Floor(tagmbe) // Use math.Floor to explicitly truncate towards negative infinity

	result := (tagm - intPart) * float64(bioryth) // Calculate the final replicated tag

	logToFile("DEBUG", fmt.Sprintf("tag(bioryth: %d) - tagm: %d, tagmbe: %.20f, intPart: %.20f, final tag: %.20f", bioryth, w.tagUser, tagmbe, intPart, result))

	return result
}

// calculation performs the main analysis calculation using sine functions.
// This is the core calculation logic for `beerg`.
func (w *CalcWorker) calculation(strM, strP string, bioryth int) float64 {
	logToFile("DEBUG", fmt.Sprintf("calculation input - strM: %s, strP: %s", strM, strP))

	// Clean input strings by removing non-alphanumeric and non-umlaut characters.
	strM = cleanString(strM)
	strP = cleanString(strP)

	// Replace characters with their numerical string equivalents using the charMatrix.
	strMreplace := sisReplace(strM)
	strPreplace := sisReplace(strP)
	logToFile("DEBUG", fmt.Sprintf("calculation - strMreplace: %s, strPreplace: %s", strMreplace, strPreplace))


	var biorythmVal, biorythsVal int // Use new variables to avoid shadowing/re-declaration issues
	// Determine biorythm periods based on user settings or system defaults.
	if w.input.User.CalculationWith == 1 { // Monthly calculation
		if w.input.User.Biorythm <= 0 {
			biorythmVal = w.input.BiorythSystem["m"] // System default 'm'
		} else {
			biorythmVal = w.input.User.Biorythm // User's 'm' setting
		}

		if w.input.User.Bioryths <= 0 {
			biorythsVal = w.input.BiorythSystem["p"] // System default 'p'
		} else {
			biorythsVal = w.input.User.Bioryths // User's 'p' setting
		}
	} else { // Yearly calculation
		biorythmVal = 365
		biorythsVal = 365
	}
	logToFile("DEBUG", fmt.Sprintf("calculation - effective biorythm: %d, bioryths: %d", biorythmVal, biorythsVal))


	// Calculate replicated tag for 'm' (mental) biorythm.
	tagmm := w.tagReplicated(biorythmVal)
	logToFile("DEBUG", fmt.Sprintf("calculation - tagmm: %.20f", tagmm))


	tag := float64(w.tagUser) // Main tagUser value as float
	tagjahr := tag / float64(biorythsVal) // Calculate yearly tag

	// Extract the fractional part for 'p' (physical) biorythm with high precision.
	// NOTE: This part retains Go's higher precision for consistency in this calculation chain.
	// Use math.Floor to explicitly truncate towards negative infinity for consistency with PHP's explode.
	intPartTagjahr := math.Floor(tagjahr)
	tags := (tagjahr - intPartTagjahr) * float64(biorythsVal) // Calculate the final physical tag
	logToFile("DEBUG", fmt.Sprintf("calculation - tag: %d, tagjahr: %.20f, intPartTagjahr: %.20f, final tags: %.20f", w.tagUser, tagjahr, intPartTagjahr, tags))


	crossfootM := crossfoot(strMreplace)
	crossfootP := crossfoot(strPreplace)
	logToFile("DEBUG", fmt.Sprintf("calculation - crossfootM: %d, crossfootP: %d", crossfootM, crossfootP))


	ym    := 100 * math.Sin(float64(crossfootM) * (math.Pi + tag) + tagmm)
	yp    := 100 * math.Sin(float64(crossfootP) * (math.Pi + tag) + tags)
	logToFile("DEBUG", fmt.Sprintf("calculation - ym: %.20f, yp: %.20f", ym, yp))


	beob  := math.Abs(math.Abs(ym)-math.Abs(yp)) * 100
	divisor := math.Abs(ym) + math.Abs(yp)

	beerg := 0.0
	if divisor == 0 {
		beerg = 0
	} else {
		beerg = 100 - (beob / divisor)
	}
	logToFile("DEBUG", fmt.Sprintf("calculation - beob: %.20f, divisor: %.20f, final beerg: %.20f", beob, divisor, beerg))

	return beerg
}

// calculationPS calculates the "Seele" (Soul) value.
func (w *CalcWorker) calculationPS(strM, strP string, bioryth int) float64 {
	strP   = strP + "Seele"
	beergs := w.calculation(strM, strP, bioryth)
	logToFile("DEBUG", fmt.Sprintf("calculationPS output: %.20f", beergs))
	return beergs
}

// calculationPK calculates the "Körper" (Body) value.
func (w *CalcWorker) calculationPK(strM, strP string, bioryth int) float64 {
	strP   = strP + "Körper"
	beergK := w.calculation(strM, strP, bioryth)
	logToFile("DEBUG", fmt.Sprintf("calculationPK output: %.20f", beergK))
	return beergK
}

// calculationOneDay calculates a specific "one day" value, similar to the main calculation but with fixed biorythm defaults.
func (w *CalcWorker) calculationOneDay(strM, strP string, biorythParam int) float64 {
	logToFile("DEBUG", fmt.Sprintf("calculationoneday input - strM: %s, strP: %s", strM, strP))

	strM = cleanString(strM)
	strP = cleanString(strP)

	strMreplace := sisReplace(strM)
	strPreplace := sisReplace(strP)
	logToFile("DEBUG", fmt.Sprintf("calculationoneday - strMreplace: %s, strPreplace: %s", strMreplace, strPreplace))


	//tendenz Variable
	tday := 2


	//Biorhythmus M
	var effectiveBiorythm int
	if w.input.User.Biorythm == 0 || w.input.User.Biorythm < 0 {
		effectiveBiorythm = 28
	} else {
		effectiveBiorythm = w.input.User.Biorythm
	}

	var effectiveBioryths int
	if w.input.User.Bioryths == 0 || w.input.User.Bioryths < 0 {
		effectiveBioryths = 28
	} else {
		effectiveBioryths = w.input.User.Bioryths
	}
	logToFile("DEBUG", fmt.Sprintf("calculationoneday - effective biorythm: %d, bioryths: %d", effectiveBiorythm, effectiveBioryths))


	// Berechnung Mensch
	tagmm := w.tagReplicated(effectiveBiorythm)
	logToFile("DEBUG", fmt.Sprintf("calculationoneday - tagmm: %.20f", tagmm))


	//Berechnung Symptom
	tag             := float64(w.tagUser)
	tagjahr         := tag + float64(tday) / float64(effectiveBioryths)
	// Use math.Floor to explicitly truncate towards negative infinity for consistency with PHP's explode.
	intPartTagjahr := math.Floor(tagjahr)
	tags             := (tagjahr - intPartTagjahr) * float64(effectiveBioryths)
	logToFile("DEBUG", fmt.Sprintf("calculationoneday - tag: %d, tagjahr: %.20f, intPartTagjahr: %.20f, final tags: %.20f", w.tagUser, tagjahr, intPartTagjahr, tags))


	ym      := 100 * math.Sin(float64(crossfoot(strMreplace)) * (math.Pi + tag) + tagmm)
	yp      := 100 * math.Sin(float64(crossfoot(strPreplace)) * (math.Pi + tag) + tags)
	logToFile("DEBUG", fmt.Sprintf("calculationoneday - ym: %.20f, yp: %.20f", ym, yp))


	beob    := math.Abs(math.Abs(ym) - math.Abs(yp)) * 100
	divisor := math.Abs(ym) + math.Abs(yp)

	beergod := 0.0
	if divisor == 0 {
		beergod = 0
	} else {
		beergod = 100 - (beob / divisor)
	}
	logToFile("DEBUG", fmt.Sprintf("calculationoneday - beob: %.20f, divisor: %.20f, final beergod: %.20f", beob, divisor, beergod))

	return beergod
}

// determineColor assigns a color (red, orange, green, or custom) based on the calculated analysis value (ma)
// and the defined color settings.
func (w *CalcWorker) determineColor(ma int) string {
	cs := w.input.ColorSettings // Get color settings from input
	var color string // Resulting color string

	// Check if product-specific color settings are provided (i.e., not all zeros or nil).
	isProductColorSet := cs.RedMin != 0 || cs.RedMax != 0 || cs.OrangeMin != 0 || cs.OrangeMax != 0 || cs.GreenMin != 0 || cs.GreenMax != 0 || cs.CustomMin != nil || cs.CustomMax != nil || cs.CustomColor != nil

	if isProductColorSet {
		// Apply product-specific color ranges.
		if ma >= cs.RedMin && ma <= cs.RedMax {
			color = "#E84E1B" // Red
		} else if ma >= cs.OrangeMin && ma <= cs.OrangeMax {
			color = "#F8B133" // Orange
		} else if ma >= cs.GreenMin && ma <= cs.GreenMax {
			color = "#2FAB66" // Green
		} else if cs.CustomMin != nil && cs.CustomMax != nil && cs.CustomColor != nil && ma >= *cs.CustomMin && ma <= *cs.CustomMax {
			color = *cs.CustomColor // Custom color
		}
	} else {
		// Apply default color ranges if no product-specific settings are active.
		if ma <= 33 {
			color = "#E84E1B" // Default Red
		} else if ma <= 66 {
			color = "#F8B133" // Default Orange
		} else {
			color = "#2FAB66" // Default Green
		}
	}

	// If color is still not set and the type is "global", apply global settings.
	// This appears to be a fallback logic to ensure a color is always assigned.
	if color == "" && cs.Type == "global" {
		if ma >= cs.RedMin && ma <= cs.RedMax {
			color = "#E84E1B"
		} else if ma >= cs.OrangeMin && ma <= cs.OrangeMax {
			color = "#F8B133"
		} else if ma >= cs.GreenMin && ma <= cs.GreenMax {
			color = "#2FAB66"
		}
	}

	// Final fallback: if no color has been determined yet, apply absolute default ranges.
	// This ensures `color` is never empty.
	if color == "" {
		if ma >= 0 && ma <= 33 {
			color = "#E84E1B"
		} else if ma >= 34 && ma <= 66 {
			color = "#F8B133"
		} else {
			color = "#2FAB66"
		}
	}

	// Increment atomic counters based on the determined color.
	if color == "#E84E1B" {
		atomic.AddInt32(w.redCount, 1)
	} else if color == "#F8B133" {
		atomic.AddInt32(w.orangeCount, 1)
	} else if color == "#2FAB66" {
		atomic.AddInt32(w.greenCount, 1)
	}

	return color
}

// getCause retrieves a cause, medium, or tip ID for a given analysis ID.
// It prioritizes saved causes, then random causes, otherwise returns 0.
func (w *CalcWorker) getCause(analysisID int, causeType string) int {
	if w.input.SubID == nil {
		return 0
	}

	analysisIDStr := strconv.Itoa(analysisID)

	var savedMap map[string]int
	var randomMap map[string]int

	switch causeType {
	case "causes":
		savedMap = w.input.SavedCauses.Causes
		randomMap = w.input.RandomCauses.Causes
	case "medium":
		savedMap = w.input.SavedCauses.Medium
		randomMap = w.input.RandomCauses.Medium
	case "tipp":
		savedMap = w.input.SavedCauses.Tipp
		randomMap = w.input.RandomCauses.Tipp
	}

	if val, exists := savedMap[analysisIDStr]; exists && val != 0 {
		return val
	}

	if len(randomMap) > 0 {
		values := make([]int, 0, len(randomMap))
		for _, v := range randomMap {
			values = append(values, v)
		}
		if len(values) > 0 {
			return values[rand.Intn(len(values))]
		}
	}

	return 0
}

// calculatePrice determines a random price within a defined range for a given analysis.
func (w *CalcWorker) calculatePrice(analysisID int) int {
	if w.input.ProID == nil || w.input.SubID == nil {
		return 0
	}

	analysisIDStr := strconv.Itoa(analysisID)
	priceValue, exists := w.input.PriceValues[analysisIDStr]
	if !exists {
		return 0
	}

	if priceValue.MinPrice == nil || priceValue.MaxPrice == nil {
		return 0
	}

	minPrice := int(*priceValue.MinPrice)
	maxPrice := int(*priceValue.MaxPrice)

	if minPrice > maxPrice {
		return minPrice
	}

	if minPrice == maxPrice {
		return minPrice
	}

	return minPrice + rand.Intn(maxPrice-minPrice+1)
}

func handleSingleCalculation(input *Input) (*Output, error) {
	logToFile("INFO", "handleSingleCalculation START")
	user := &input.User
	tagUser := calculateTagUser(user)

	worker := CalcWorker{
		input:   input,
		tagUser: tagUser,
	}

	value := worker.calculation(input.UserString, input.AnalysisString, input.Bioryth)
	logToFile("INFO", "handleSingleCalculation END")

	return &Output{
		Value: value,
	}, nil
}

func handleFrequencyCalculation(input *Input) (*Output, error) {
	logToFile("INFO", "handleFrequencyCalculation START")
	analysis := cleanString(input.AnalysisString)
	analysisMatrix := sisReplace(analysis)
	analysisCrossfoot := crossfoot(analysisMatrix)

	pi := math.Pi
	freq := float64(analysisCrossfoot*analysisCrossfoot) * pi

	freq = math.Round(freq*1000) / 1000

	if freq > 1000 {
		for freq > 1000 {
			freq /= 10
		}
	}
	logToFile("INFO", "handleFrequencyCalculation END")


	return &Output{
		Frequency: int(math.Round(freq)),
	}, nil
}

// Helper functions (not methods of CalcWorker)

// getUserString concatenates relevant user fields into a single string.
func getUserString(user *User) string {
	return user.FirstName + user.LastName + user.Gebdatum + user.Gebort + user.ThemaSpeichern
}

// calculateTagUser calculates the number of days between the birth date and the core date (or current date).
// This function aims to precisely mimic PHP's date handling for consistency.
func calculateTagUser(user *User) int {
	logToFile("DEBUG", fmt.Sprintf("taguser input - gebdatum: %s, datumcore: %s", user.Gebdatum, user.Datumcore))

	// Load Europe/Vienna timezone for consistent date calculations.
	loc, err := time.LoadLocation("Europe/Vienna")
	if err != nil {
		logToFile("ERROR", fmt.Sprintf("taguser - Failed to load Europe/Vienna timezone, falling back to UTC: %v", err))
		loc = time.UTC
	}

	// Parse gebdatum (birth date) - use "02.01.2006" for "DD.MM.YYYY" format
	var gebdatum time.Time
	// Attempt to parse with "DD.MM.YYYY" format first, as seen in logs
	gebdatum, err = time.ParseInLocation("02.01.2006", user.Gebdatum, loc)
	if err != nil {
		// Fallback to "YYYY-MM-DD" if "DD.MM.YYYY" fails (e.g., if datumcore is YYYY-MM-DD)
		gebdatum, err = time.ParseInLocation("2006-01-02", user.Gebdatum, loc)
		if err != nil {
			logToFile("ERROR", fmt.Sprintf("taguser - Failed to parse gebdatum: %s with both formats. Error: %v", user.Gebdatum, err))
			return 0 // Return 0 if birth date cannot be parsed.
		}
	}

	// Determine the userDate (core date or current date).
	var userDate time.Time
	if user.Datumcore == "" || user.Datumcore == "0000-00-00" {
		// PHP's date_create('Today') will be 00:00:00 in the default timezone.
		now := time.Now().In(loc)
		userDate = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc)
		logToFile("DEBUG", fmt.Sprintf("taguser - datumcore empty, using Today (normalized): %s", userDate.Format("2006-01-02 15:04:05.000000")))
	} else {
		// Parse datumcore (YYYY-MM-DD)
		parsedDate, parseErr := time.ParseInLocation("2006-01-02", user.Datumcore, loc)
		if parseErr != nil {
			logToFile("ERROR", fmt.Sprintf("taguser - Failed to parse datumcore: %s. Error: %v. Falling back to Today.", user.Datumcore, parseErr))
			now := time.Now().In(loc)
			userDate = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc)
		} else {
			// Explicitly set to 00:00:00 in the specified location for precise alignment with PHP's date_create behavior
			userDate = time.Date(parsedDate.Year(), parsedDate.Month(), parsedDate.Day(), 0, 0, 0, 0, loc)
		}
		logToFile("DEBUG", fmt.Sprintf("taguser - using datumcore (normalized): %s", userDate.Format("2006-01-02 15:04:05.000000")))
	}

	// Log parsed dates before diff
	logToFile("DEBUG", fmt.Sprintf("taguser - parsed gebdatum: %s, parsed userdate: %s", gebdatum.Format("2006-01-02 15:04:05.000000"), userDate.Format("2006-01-02 15:04:05.000000")))


	// Calculate the duration between the two dates.
	// PHP's date_diff calculates based on calendar days, not necessarily exact 24-hour periods,
	// especially around DST changes. However, by normalizing both to 00:00:00 in the same timezone,
	// Sub() should yield consistent results in terms of full days.
	duration := userDate.Sub(gebdatum)
	// Convert duration to days, rounding to the nearest integer.
	days := int(duration.Hours()/24 + 0.5)

	// Ensure the result is a positive number of days.
	if days < 0 {
		days = -days
	}
	logToFile("DEBUG", fmt.Sprintf("taguser output - gebdatum: %s, userdate: %s, days: %d", gebdatum.Format("2006-01-02 15:04:05.000000"), userDate.Format("2006-01-02 15:04:05.000000"), days))

	return days
}

// cleanString removes non-alphanumeric characters (excluding specific German umlauts) from a string.
func cleanString(s string) string {
	// Regex pattern to match anything *not* a letter (a-z, A-Z), digit (0-9), or German umlaut/ß.
	reg := regexp.MustCompile(`[^a-zA-Z0-9ßöäüÖÄÜ]`)
	return reg.ReplaceAllString(s, "")
}

// sisReplace converts characters in a text to their numerical string representations based on charMatrix.
// This function is analogous to PHP's sis_replace.
func sisReplace(text string) string {
	// Use the pre-initialized strings.Replacer for efficient and consistent replacement.
	result := replacer.Replace(text)
	logToFile("DEBUG", fmt.Sprintf("sis_replace(text: %s) -> %s", text, result))
	return result
}

// crossfoot calculates the sum of the digits in a string.
func crossfoot(digits string) int {
	sum := 0
	for _, char := range digits {
		// Convert each character to an integer and add to the sum.
		if num, err := strconv.Atoi(string(char)); err == nil {
			sum += num
		}
	}
	logToFile("DEBUG", fmt.Sprintf("crossfoot(digits: %s) -> %d", digits, sum))
	return sum
}

// sortResults sorts a slice of AnalysisResult based on filter type and SubID.
func sortResults(results []AnalysisResult, filterType int, subID *int) {
	switch filterType {
	case 1: // Sort by AnaName (ascending)
		sort.Slice(results, func(i, j int) bool {
			return results[i].AnaName < results[j].AnaName
		})
		logToFile("DEBUG", "Sorted by AnaName ASC.")
	case 2: // Sort by AnaName (ascending) if SubID is 20, else AnaName (descending)
		if subID != nil && *subID == 20 {
			sort.Slice(results, func(i, j int) bool {
				return results[i].AnaName < results[j].AnaName
			})
			logToFile("DEBUG", "Sorted by AnaName ASC (SubID 20).")
		} else {
			sort.Slice(results, func(i, j int) bool {
				return results[i].AnaName > results[j].AnaName
			})
			logToFile("DEBUG", "Sorted by AnaName DESC.")
		}
	case 3: // Sort by AnaVal (ascending) if SubID is not 20
		if subID != nil && *subID != 20 {
			sort.Slice(results, func(i, j int) bool {
				return results[i].AnaVal < results[j].AnaVal
			})
			logToFile("DEBUG", "Sorted by AnaVal ASC.")
		}
	case 4: // Sort by AnaVal (descending) if SubID is not 20
		if subID != nil && *subID != 20 {
			sort.Slice(results, func(i, j int) bool {
				return results[i].AnaVal > results[j].AnaVal
			})
			logToFile("DEBUG", "Sorted by AnaVal DESC.")
		}
	default: // Default sort by AnaName (ascending)
		sort.Slice(results, func(i, j int) bool {
			return results[i].AnaName < results[j].AnaName
		})
		logToFile("DEBUG", "Sorted by AnaName ASC (default).")
	}
}

// Helper to format AnalysisResult for logging
func formatAnalysisResultForLog(ar AnalysisResult) string {
    // Using a map to build the JSON string, then marshal it.
    // This provides a more readable and structured log entry for the analysis result.
    data := map[string]interface{}{
        "anaVal": ar.AnaVal,
        "anaid": ar.AnaID,
        "anaName": ar.AnaName,
        "url_name": ar.URLName,
        "url_link": ar.URLLink,
        "desc": ar.Desc,
        "desc_img": ar.DescImg,
        "bodyDesc": ar.BodyDesc,
        "mentalDesc": ar.MentalDesc,
        "bioyrth": ar.Bioyrth,
        "anaColor": ar.AnaColor,
        "maleVal": ar.MaleVal,
        "heartVal": ar.HeartVal,
        "maleColor": ar.MaleColor,
        "heartColor": ar.HeartColor,
        "beergod": ar.Beergod,
        "causes": ar.Causes,
        "medium": ar.Medium,
        "tipp": ar.Tipp,
        "poolid": ar.PoolID,
        "randPrice": ar.RandPrice,
        "ranValStatus": ar.RanValStatus,
    }
    // Using json.MarshalIndent for readability in log, but can use json.Marshal for compactness
    b, err := json.Marshal(data)
    if err != nil {
        return fmt.Sprintf("{Error marshalling AnalysisResult: %v}", err)
    }
    return string(b)
}
