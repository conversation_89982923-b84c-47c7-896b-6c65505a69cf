package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"runtime"
	"sync"
	"time"
	"calc"
)

// Simplified input/output structures
type BatchInput struct {
	Mode          string          `json:"mode"`
	LongDay       int             `json:"long_day"`
	StartDate     string          `json:"start_date"`
	BaseInput     json.RawMessage `json:"base_input"`
	PoolIDs       []int           `json:"pool_ids"`
	ConcurrentOps int             `json:"concurrent_ops"`
	DebugMode     bool            `json:"debug_mode,omitempty"`
}

type BatchOutput struct {
	Mode        string      `json:"mode"`
	Results     []DayResult `json:"results"`
	TotalDays   int         `json:"total_days"`
	ProcessTime float64     `json:"process_time_ms"`
	Errors      []string    `json:"errors,omitempty"`
}

type DayResult struct {
	Date     string      `json:"date"`
	DayIndex int         `json:"day_index"`
	Output   calc.Output `json:"output"`
	Error    string      `json:"error,omitempty"`
}

// Job represents a single calculation task
type Job struct {
	Index    int
	Date     string
	DateObj  time.Time
	BaseData *calc.Input
}

// Result represents the output of a job
type Result struct {
	Index  int
	Result DayResult
}

const (
	dateLayout    = "2006-01-02"
	maxWorkers    = 64  // Much more conservative
	channelBuffer = 100 // Reasonable buffer size
)

func main() {
	inputData, err := io.ReadAll(os.Stdin)
	if err != nil {
		outputError(fmt.Errorf("failed to read input: %w", err))
	}

	var in BatchInput
	if err := json.Unmarshal(inputData, &in); err != nil {
		outputError(fmt.Errorf("failed to parse input: %w", err))
	}

	switch {
	case in.LongDay == 1:
		processSingleDay(&in)
	case in.LongDay > 1:
		processMultipleDays(&in)
	default:
		outputError(fmt.Errorf("invalid LongDay value: %d", in.LongDay))
	}
}

func processSingleDay(in *BatchInput) {
	start := time.Now()

	var calcInput calc.Input
	if err := json.Unmarshal(in.BaseInput, &calcInput); err != nil {
		outputError(fmt.Errorf("failed to parse base_input: %w", err))
	}

	out, err := calc.ProcessCalculation(&calcInput)

	res := DayResult{
		Date:     calcInput.CurrentDate,
		DayIndex: 0,
	}
	if err != nil {
		res.Error = err.Error()
	} else {
		res.Output = *out
	}

	output(BatchOutput{
		Mode:        "batch",
		Results:     []DayResult{res},
		TotalDays:   1,
		ProcessTime: float64(time.Since(start).Nanoseconds()) / 1e6,
		Errors:      conditionalErrors(res.Error),
	})
}

func processMultipleDays(in *BatchInput) {
	start := time.Now()

	// Parse base input
	var baseInput calc.Input
	if err := json.Unmarshal(in.BaseInput, &baseInput); err != nil {
		outputError(fmt.Errorf("failed to parse base_input: %w", err))
	}

	startDate, err := time.Parse(dateLayout, in.StartDate)
	if err != nil {
		outputError(fmt.Errorf("invalid start date: %w", err))
	}

	n := in.LongDay
	if n <= 0 {
		outputError(errors.New("long_day must be > 0"))
	}

	// Calculate optimal worker count
	numWorkers := calculateOptimalWorkers(in.ConcurrentOps, n)

	// Pre-allocate results slice
	results := make([]DayResult, n)
	var errorList []string
	var errorMutex sync.Mutex

	// Create job and result channels with appropriate buffer sizes
	jobChan := make(chan Job, min(channelBuffer, n))
	resultChan := make(chan Result, min(channelBuffer, n))

	// Create context for cancellation
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Start workers
	var wg sync.WaitGroup
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			worker(ctx, workerID, jobChan, resultChan, &errorMutex, &errorList)
		}(i)
	}

	// Start result collector
	var resultWg sync.WaitGroup
	resultWg.Add(1)
	go func() {
		defer resultWg.Done()
		collectResults(resultChan, results, n)
	}()

	// Generate and send jobs
	go func() {
		defer close(jobChan)
		generateJobs(ctx, jobChan, &baseInput, startDate, n)
	}()

	// Wait for all workers to finish
	wg.Wait()
	close(resultChan)

	// Wait for result collection to finish
	resultWg.Wait()

	output(BatchOutput{
		Mode:        "batch",
		Results:     results,
		TotalDays:   n,
		ProcessTime: float64(time.Since(start).Nanoseconds()) / 1e6,
		Errors:      errorList,
	})
}

func calculateOptimalWorkers(requested, jobCount int) int {
	cpuCount := runtime.NumCPU()

	// Conservative worker calculation based on actual testing
	var optimal int

	if requested > 0 {
		// Respect user's preference but cap it reasonably
		optimal = min(requested, maxWorkers)
	} else {
		// Auto-calculate based on job count and CPU cores
		switch {
		case jobCount <= 10:
			optimal = min(2, cpuCount)
		case jobCount <= 50:
			optimal = min(4, cpuCount)
		case jobCount <= 200:
			optimal = min(cpuCount, 8)
		case jobCount <= 1000:
			optimal = min(cpuCount*2, 16)
		default:
			optimal = min(cpuCount*2, 32)
		}
	}

	// Ensure we have at least 1 worker
	return max(1, optimal)
}

func generateJobs(ctx context.Context, jobChan chan<- Job, baseInput *calc.Input, startDate time.Time, n int) {
	for i := 0; i < n; i++ {
		select {
		case <-ctx.Done():
			return
		default:
			dateObj := startDate.AddDate(0, 0, i)
			job := Job{
				Index:    i,
				Date:     dateObj.Format(dateLayout),
				DateObj:  dateObj,
				BaseData: baseInput,
			}

			select {
			case jobChan <- job:
				// Job sent successfully
			case <-ctx.Done():
				return
			}
		}
	}
}

func worker(ctx context.Context, workerID int, jobChan <-chan Job, resultChan chan<- Result, errorMutex *sync.Mutex, errorList *[]string) {
	for {
		select {
		case <-ctx.Done():
			return
		case job, ok := <-jobChan:
			if !ok {
				return // Channel closed
			}

			result := processJob(job, errorMutex, errorList)

			select {
			case resultChan <- result:
				// Result sent successfully
			case <-ctx.Done():
				return
			}
		}
	}
}

func processJob(job Job, errorMutex *sync.Mutex, errorList *[]string) Result {
	// Create a copy of the base input for this job
	input := *job.BaseData

	// Set the date for this specific job
	input.User.Datumcore = job.Date
	input.CurrentDate = job.Date

	// Process the calculation
	out, err := calc.ProcessCalculation(&input)

	dayResult := DayResult{
		Date:     job.Date,
		DayIndex: job.Index,
	}

	if err != nil {
		dayResult.Error = err.Error()

		// Thread-safe error collection
		errorMutex.Lock()
		if len(*errorList) < 100 { // Limit error list size
			*errorList = append(*errorList, err.Error())
		}
		errorMutex.Unlock()
	} else {
		dayResult.Output = *out
	}

	return Result{
		Index:  job.Index,
		Result: dayResult,
	}
}

func collectResults(resultChan <-chan Result, results []DayResult, expected int) {
	received := 0
	for result := range resultChan {
		if result.Index >= 0 && result.Index < len(results) {
			results[result.Index] = result.Result
			received++
		}

		if received >= expected {
			break
		}
	}

	// Fill in any missing results with timeout errors
	for i := 0; i < len(results); i++ {
		if results[i].Date == "" {
			results[i] = DayResult{
				Date:     "",
				DayIndex: i,
				Error:    "processing timeout or missing result",
			}
		}
	}
}

func output(out BatchOutput) {
	encoder := json.NewEncoder(os.Stdout)
	encoder.SetIndent("", "")
	if err := encoder.Encode(out); err != nil {
		outputError(fmt.Errorf("failed to encode output: %w", err))
	}
}

func outputError(err error) {
	fmt.Fprintf(os.Stdout, `{"error":"%s"}`+"\n", err.Error())
	os.Exit(1)
}

func conditionalErrors(s string) []string {
	if s == "" {
		return nil
	}
	return []string{s}
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}