package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math"
	"math/rand"
	"os"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

// Input structures
type Input struct {
	Mode string `json:"mode"`
	User User `json:"user"`
	Analyses []Analysis `json:"analyses"`
	BiorythSystem map[string]int `json:"bioryth_system"`
	ColorSettings ColorSettings `json:"color_settings"`
	RandomValues map[int]int `json:"random_values"`
	SavedCauses CausesData `json:"saved_causes"`
	RandomCauses CausesData `json:"random_causes"`
	PriceValues map[string]PriceValue `json:"price_values"`
	SubID *int `json:"subid"`
	ProID *int `json:"proid"`
	FilterType int `json:"filter_type"`
	Locale string `json:"locale"`
	CurrentDate string `json:"current_date"`
	UserString string `json:"user_string"`
	AnalysisString string `json:"analysis_string"`
	Bioryth int `json:"bioryth"`
}

type PriceValue struct {
	MinPrice *float64 `json:"min_price"`
	MaxPrice *float64 `json:"max_price"`
}

type User struct {
	ID int `json:"id"`
	FirstName string `json:"first_name"`
	LastName string `json:"last_name"`
	Gebdatum string `json:"gebdatum"`
	Gebort string `json:"gebort"`
	ThemaSpeichern string `json:"thema_speichern"`
	Datumcore string `json:"datumcore"`
	CalculationWith int `json:"calculation_with"`
	Biorythm int `json:"biorythm"`
	Bioryths int `json:"bioryths"`
	RanAna int `json:"ran_ana"`
	PatternSwitch int `json:"pattern_switch"`
}

type Analysis struct {
	ID int `json:"id"`
	Name string `json:"name"`
	DisplayName string `json:"display_name"`
	Bioryth int `json:"bioryth"`
	BodyDesc string `json:"body_desc"`
	MentalDesc string `json:"mental_desc"`
	Description string `json:"description"`
	DescImage string `json:"desc_image"`
	URLName string `json:"url_name"`
	URLLink string `json:"url_link"`
	PoolID int `json:"pool_id"`
}

type ColorSettings struct {
	Type string `json:"type"`
	RedMin int `json:"red_min"`
	RedMax int `json:"red_max"`
	OrangeMin int `json:"orange_min"`
	OrangeMax int `json:"orange_max"`
	GreenMin int `json:"green_min"`
	GreenMax int `json:"green_max"`
	CustomMin *int `json:"custom_min"`
	CustomMax *int `json:"custom_max"`
	CustomColor *string `json:"custom_color"`
}

type CausesData struct {
	Causes map[string]int `json:"causes"`
	Medium map[string]int `json:"medium"`
	Tipp map[string]int `json:"tipp"`
}

// Output structures
type AnalysisResult struct {
	AnaVal int `json:"anaVal"`
	AnaID int `json:"anaid"`
	AnaName string `json:"anaName"`
	URLName *string `json:"url_name"`
	URLLink *string `json:"url_link"`
	Desc *string `json:"desc"`
	DescImg *string `json:"desc_img"`
	BodyDesc *string `json:"bodyDesc"`
	MentalDesc *string `json:"mentalDesc"`
	Bioyrth int `json:"bioyrth"`
	AnaColor string `json:"anaColor"`
	MaleVal int `json:"maleVal"`
	HeartVal int `json:"heartVal"`
	MaleColor string `json:"maleColor"`
	HeartColor string `json:"heartColor"`
	Beergod int `json:"beergod"`
	Causes int `json:"causes"`
	Medium int `json:"medium"`
	Tipp int `json:"tipp"`
	PoolID int `json:"poolid"`
	RandPrice int `json:"randPrice"`
	RanValStatus int `json:"ranValStatus"`
}

type Output struct {
	Analyses []AnalysisResult `json:"analyses"`
	RedCount int `json:"red_count"`
	OrangeCount int `json:"orange_count"`
	GreenCount int `json:"green_count"`
	Value float64 `json:"value,omitempty"`
	Frequency int `json:"frequency,omitempty"`
}

// Matrix for character replacement
var charMatrix = map[rune]string{
	'a': "1", 'b': "2", 'c': "3", 'd': "4", 'e': "5", 'f': "6", 'g': "7", 'h': "8", 'i': "9",
	'j': "1", 'k': "2", 'l': "3", 'm': "4", 'n': "5", 'o': "6", 'p': "7", 'q': "8", 'r': "9",
	's': "1", 't': "2", 'u': "3", 'v': "4", 'w': "5", 'x': "6", 'y': "7", 'z': "8",
	'A': "1", 'B': "2", 'C': "3", 'D': "4", 'E': "5", 'F': "6", 'G': "7", 'H': "8", 'I': "9",
	'J': "1", 'K': "2", 'L': "3", 'M': "4", 'N': "5", 'O': "6", 'P': "7", 'Q': "8", 'R': "9",
	'S': "1", 'T': "2", 'U': "3", 'V': "4", 'W': "5", 'X': "6", 'Y': "7", 'Z': "8",
	'ß': "11", 'ä': "15", 'ü': "35", 'ö': "65", 'Ä': "15", 'Ü': "35", 'Ö': "65",
}

// Global variable for debugging
var debugFile *os.File
var debugMu sync.Mutex

// Worker for parallel processing
type CalcWorker struct {
	wg *sync.WaitGroup
	input *Input
	userString string
	tagUser int
	results chan<- AnalysisResult
	redCount *int32
	orangeCount *int32
	greenCount *int32
	mu *sync.Mutex
}

func main() {
	// Seed random number generator
	rand.Seed(time.Now().UnixNano())

	// Open debug file
	var err error
	debugFile, err = os.Create("debug.txt")
	if err != nil {
		outputError(err)
		return
	}
	defer debugFile.Close()

	debugLog("--- Starting Go Calculation Debug ---\n")

	// Read input from stdin
	inputData, err := ioutil.ReadAll(os.Stdin)
	if err != nil {
		outputError(err)
		return
	}

	var input Input
	if err := json.Unmarshal(inputData, &input); err != nil {
		outputError(err)
		return
	}

	debugLog(fmt.Sprintf("Input: %+v\n", input))

	// Handle different modes
	switch input.Mode {
	case "single":
		handleSingleCalculation(&input)
	case "frequency":
		handleFrequencyCalculation(&input)
	default:
		handleBatchCalculation(&input)
	}
}

func handleBatchCalculation(input *Input) {
	debugLog("--- Starting Batch Calculation ---\n")
	userString := getUserString(&input.User)
	tagUser := calculateTagUser(&input.User)

	debugLog(fmt.Sprintf("User String (strM): %s\n", userString))
	debugLog(fmt.Sprintf("Tag User: %d\n", tagUser))

	// Initialize counters
	var redCount, orangeCount, greenCount int32
	var mu sync.Mutex

	// Create result channel with buffer
	results := make(chan AnalysisResult, len(input.Analyses))

	// Create wait group for goroutines
	var wg sync.WaitGroup

	// Process analyses in parallel using goroutines
	for _, analysis := range input.Analyses {
		wg.Add(1)
		go func(ana Analysis) {
			defer wg.Done()

			worker := CalcWorker{
				wg:          &wg,
				input:       input,
				userString:  userString,
				tagUser:     tagUser,
				results:     results,
				redCount:    &redCount,
				orangeCount: &orangeCount,
				greenCount:  &greenCount,
				mu:          &mu,
			}

			worker.processAnalysis(ana)
		}(analysis)
	}

	// Wait for all goroutines to complete
	wg.Wait()
	close(results)

	// Collect results
	var analysisResults []AnalysisResult
	for result := range results {
		analysisResults = append(analysisResults, result)
	}

	// Apply sorting
	sortResults(analysisResults, input.FilterType, input.SubID)

	// Output results
	output := Output{
		Analyses:    analysisResults,
		RedCount:    int(redCount),
		OrangeCount: int(orangeCount),
		GreenCount:  int(greenCount),
	}

	outputJSON(output)
}

func (w *CalcWorker) processAnalysis(analysis Analysis) {
	debugLog(fmt.Sprintf("\n--- Processing Analysis: %s (ID: %d) ---\n", analysis.Name, analysis.ID))
	strP := analysis.Name
	bioryth := analysis.Bioryth
	if bioryth <= 0 {
		bioryth = 28
	}
	debugLog(fmt.Sprintf("Raw strP: %s\n", strP))

	// Core calculations
	beergS := w.calculationPS(w.userString, strP, bioryth)
	beergK := w.calculationPK(w.userString, strP, bioryth)
	beergod := w.calculationOneDay(w.userString, strP, bioryth)

	var beerg float64
	if w.input.User.RanAna == 1 {
		beerg = float64(rand.Intn(101))
	} else {
		beerg = w.calculation(w.userString, strP, bioryth)
	}

	debugLog(fmt.Sprintf("Calculated beergS: %f, beergK: %f, beerg: %f, beergod: %f\n", beergS, beergK, beerg, beergod))

	// Round values
	ma := int(math.Round(beerg))
	beergKRounded := int(math.Round(beergK))
	beergSRounded := int(math.Round(beergS))
	beergodRounded := int(math.Round(beergod))

	debugLog(fmt.Sprintf("Rounded ma: %d, beergK: %d, beergS: %d, beergod: %d\n", ma, beergKRounded, beergSRounded, beergodRounded))

	ranValStatus := 0
	if w.input.User.PatternSwitch == 1 {
		if val, exists := w.input.RandomValues[analysis.ID]; exists {
			ma = val
			ranValStatus = 1
			debugLog(fmt.Sprintf("Using random value from database: %d\n", ma))
		}
	}

	// Determine colors
	heartColor := "color:gray"
	if beergSRounded >= 50 {
		heartColor = "color:red"
	}

	maleColor := "color:gray"
	if beergKRounded <= 30 {
		maleColor = "color:red"
	}

	// Determine analysis color
	anaColor := w.determineColor(ma)
	debugLog(fmt.Sprintf("Ana Color: %s\n", anaColor))

	// Get causes
	causes := w.getCause(analysis.ID, "causes")
	medium := w.getCause(analysis.ID, "medium")
	tipp := w.getCause(analysis.ID, "tipp")

	// Calculate price
	randPrice := w.calculatePrice(analysis.ID)

	var urlName, urlLink, desc, descImg, bodyDesc, mentalDesc *string
	if analysis.URLName != "" {
		urlName = &analysis.URLName
	}
	if analysis.URLLink != "" {
		urlLink = &analysis.URLLink
	}
	if analysis.Description != "" {
		desc = &analysis.Description
	}
	if analysis.DescImage != "" {
		descImg = &analysis.DescImage
	}
	if analysis.BodyDesc != "" {
		bodyDesc = &analysis.BodyDesc
	}
	if analysis.MentalDesc != "" {
		mentalDesc = &analysis.MentalDesc
	}

	// Create result
	result := AnalysisResult{
		AnaVal: ma,
		AnaID: analysis.ID,
		AnaName: analysis.DisplayName,
		URLName: urlName,
		URLLink: urlLink,
		Desc: desc,
		DescImg: descImg,
		BodyDesc: bodyDesc,
		MentalDesc: mentalDesc,
		Bioyrth: bioryth,
		AnaColor: anaColor,
		MaleVal: beergKRounded,
		HeartVal: beergSRounded,
		MaleColor: maleColor,
		HeartColor: heartColor,
		Beergod: beergodRounded,
		Causes: causes,
		Medium: medium,
		Tipp: tipp,
		PoolID: analysis.PoolID,
		RandPrice: randPrice,
		RanValStatus: ranValStatus,
	}

	w.results <- result
}

// Function to replicate the PHP tag function
func (w *CalcWorker) tagReplicated(bioryth int) float64 {
	tagm := float64(w.tagUser)
	tagmbe := tagm / float64(bioryth)
	array := strings.Split(fmt.Sprintf("%.14f", tagmbe), ".")
	var intPart float64
	if len(array) > 0 {
		intPart, _ = strconv.ParseFloat(array[0], 64)
	} else {
		intPart = 0
	}

	result := (tagm - intPart) * float64(bioryth)
	debugLog(fmt.Sprintf("tag -> tagm: %f, bioryth: %d, tagmbe: %f, array[0]: %s, tag: %f\n", tagm, bioryth, tagmbe, array[0], result))
	return result
}

func (w *CalcWorker) calculation(strM, strP string, bioryth int) float64 {
	debugLog(fmt.Sprintf("--- calculation for: %s ---\n", strP))
	debugLog(fmt.Sprintf("calculation -> strM: %s, strP: %s, bioryth: %d\n", strM, strP, bioryth))

	strM = cleanString(strM)
	strP = cleanString(strP)

	strMReplace := sisReplace(strM)
	strPReplace := sisReplace(strP)
	debugLog(fmt.Sprintf("calculation -> cleaned strM: %s, cleaned strP: %s\n", strM, strP))
	debugLog(fmt.Sprintf("calculation -> strMreplace: %s, strPreplace: %s\n", strMReplace, strPReplace))

	var biorythm, bioryths int
	if w.input.User.CalculationWith == 1 {
		if w.input.User.Biorythm <= 0 {
			biorythm = w.input.BiorythSystem["m"]
		} else {
			biorythm = w.input.User.Biorythm
		}

		if w.input.User.Bioryths <= 0 {
			bioryths = w.input.BiorythSystem["p"]
		} else {
			bioryths = w.input.User.Bioryths
		}
	} else {
		biorythm = 365
		bioryths = 365
	}
	debugLog(fmt.Sprintf("calculation -> final biorythm: %d, bioryths: %d\n", biorythm, bioryths))

	tagmm := w.tagReplicated(biorythm)

	tag := float64(w.tagUser)
	tagjahr := tag / float64(bioryths)
	tagjahrarray := strings.Split(fmt.Sprintf("%.14f", tagjahr), ".")
	var intPart float64
	if len(tagjahrarray) > 0 {
		intPart, _ = strconv.ParseFloat(tagjahrarray[0], 64)
	} else {
		intPart = 0
	}
	tags := (tagjahr - intPart) * float64(bioryths)
	debugLog(fmt.Sprintf("calculation -> tagmm: %f, tag: %f, tagjahr: %f, tags: %f\n", tagmm, tag, tagjahr, tags))

	crossfootM := float64(crossfoot(strMReplace))
	crossfootP := float64(crossfoot(strPReplace))
	debugLog(fmt.Sprintf("calculation -> crossfootM: %f, crossfootP: %f\n", crossfootM, crossfootP))


	ym := 100 * math.Sin(crossfootM*(math.Pi+tag)+tagmm)
	yp := 100 * math.Sin(crossfootP*(math.Pi+tag)+tags)
	debugLog(fmt.Sprintf("calculation -> ym: %f, yp: %f\n", ym, yp))


	beob := math.Abs(math.Abs(ym)-math.Abs(yp)) * 100

	divisor := math.Abs(ym) + math.Abs(yp)
	if divisor == 0 {
		debugLog("calculation -> Division by zero, returning 0.\n")
		return 0
	}

	beerg := 100 - (beob / divisor)
	debugLog(fmt.Sprintf("calculation -> beob: %f, divisor: %f, beerg: %f\n", beob, divisor, beerg))
	return beerg
}

func (w *CalcWorker) calculationPS(strM, strP string, bioryth int) float64 {
	debugLog(fmt.Sprintf("--- calculationPS for: %s ---\n", strP))
	result := w.calculation(strM, strP+"Seele", bioryth)
	debugLog(fmt.Sprintf("--- Finished calculationPS. Result: %f ---\n", result))
	return result
}

func (w *CalcWorker) calculationPK(strM, strP string, bioryth int) float64 {
	debugLog(fmt.Sprintf("--- calculationPK for: %s ---\n", strP))
	result := w.calculation(strM, strP+"Körper", bioryth)
	debugLog(fmt.Sprintf("--- Finished calculationPK. Result: %f ---\n", result))
	return result
}

func (w *CalcWorker) calculationOneDay(strM, strP string, bioryth int) float64 {
	debugLog(fmt.Sprintf("--- calculationoneday for: %s ---\n", strP))
	strM = cleanString(strM)
	strP = cleanString(strP)

	strMReplace := sisReplace(strM)
	strPReplace := sisReplace(strP)

	tday := 2

	biorythm := 28
	if w.input.User.Biorythm > 0 {
		biorythm = w.input.User.Biorythm
	}

	bioryths := 28
	if w.input.User.Bioryths > 0 {
		bioryths = w.input.User.Bioryths
	}

	tagmm := w.tagReplicated(biorythm)

	tag := float64(w.tagUser)
	tagjahr := tag + float64(tday)/float64(bioryths)
	tagjahrarray := strings.Split(fmt.Sprintf("%.14f", tagjahr), ".")
	var intPart float64
	if len(tagjahrarray) > 0 {
		intPart, _ = strconv.ParseFloat(tagjahrarray[0], 64)
	} else {
		intPart = 0
	}
	tags := (tagjahr - intPart) * float64(bioryths)
	debugLog(fmt.Sprintf("calculationoneday -> tagmm: %f, tag: %f, tagjahr: %f, tags: %f\n", tagmm, tag, tagjahr, tags))

	crossfootM := float64(crossfoot(strMReplace))
	crossfootP := float64(crossfoot(strPReplace))

	ym := 100 * math.Sin(crossfootM*(math.Pi+tag)+tagmm)
	yp := 100 * math.Sin(crossfootP*(math.Pi+tag)+tags)

	beob := math.Abs(math.Abs(ym)-math.Abs(yp)) * 100

	divisor := math.Abs(ym) + math.Abs(yp)
	if divisor == 0 {
		debugLog("calculationoneday -> Division by zero, returning 0.\n")
		return 0
	}

	beergod := 100 - (beob / divisor)
	debugLog(fmt.Sprintf("calculationoneday -> ym: %f, yp: %f, beob: %f, divisor: %f, beergod: %f\n", ym, yp, beob, divisor, beergod))
	return beergod
}

func (w *CalcWorker) determineColor(ma int) string {
	cs := w.input.ColorSettings
	var color string

	isProductColorSet := cs.RedMin != 0 || cs.RedMax != 0 || cs.OrangeMin != 0 || cs.OrangeMax != 0 || cs.GreenMin != 0 || cs.GreenMax != 0 || cs.CustomMin != nil || cs.CustomMax != nil || cs.CustomColor != nil

	if isProductColorSet {
		if ma >= cs.RedMin && ma <= cs.RedMax {
			color = "#E84E1B"
		} else if ma >= cs.OrangeMin && ma <= cs.OrangeMax {
			color = "#F8B133"
		} else if ma >= cs.GreenMin && ma <= cs.GreenMax {
			color = "#2FAB66"
		} else if cs.CustomMin != nil && cs.CustomMax != nil && cs.CustomColor != nil && ma >= *cs.CustomMin && ma <= *cs.CustomMax {
			color = *cs.CustomColor
		}
	} else {
		if ma <= 33 {
			color = "#E84E1B"
		} else if ma <= 66 {
			color = "#F8B133"
		} else {
			color = "#2FAB66"
		}
	}

	if color == "" && cs.Type == "global" {
		if ma >= cs.RedMin && ma <= cs.RedMax {
			color = "#E84E1B"
		} else if ma >= cs.OrangeMin && ma <= cs.OrangeMax {
			color = "#F8B133"
		} else if ma >= cs.GreenMin && ma <= cs.GreenMax {
			color = "#2FAB66"
		}
	}

	if color == "" {
		if ma >= 0 && ma <= 33 {
			color = "#E84E1B"
		} else if ma >= 34 && ma <= 66 {
			color = "#F8B133"
		} else {
			color = "#2FAB66"
		}
	}
	debugLog(fmt.Sprintf("colorCalculationHelper -> ma: %d, color: %s\n", ma, color))

	if color == "#E84E1B" {
		atomic.AddInt32(w.redCount, 1)
	} else if color == "#F8B133" {
		atomic.AddInt32(w.orangeCount, 1)
	} else if color == "#2FAB66" {
		atomic.AddInt32(w.greenCount, 1)
	}

	return color
}

func (w *CalcWorker) getCause(analysisID int, causeType string) int {
	if w.input.SubID == nil {
		return 0
	}

	analysisIDStr := strconv.Itoa(analysisID)

	var savedMap map[string]int
	var randomMap map[string]int

	switch causeType {
	case "causes":
		savedMap = w.input.SavedCauses.Causes
		randomMap = w.input.RandomCauses.Causes
	case "medium":
		savedMap = w.input.SavedCauses.Medium
		randomMap = w.input.RandomCauses.Medium
	case "tipp":
		savedMap = w.input.SavedCauses.Tipp
		randomMap = w.input.RandomCauses.Tipp
	}

	if val, exists := savedMap[analysisIDStr]; exists && val != 0 {
		return val
	}

	if len(randomMap) > 0 {
		values := make([]int, 0, len(randomMap))
		for _, v := range randomMap {
			values = append(values, v)
		}
		if len(values) > 0 {
			return values[rand.Intn(len(values))]
		}
	}

	return 0
}

func (w *CalcWorker) calculatePrice(analysisID int) int {
	if w.input.ProID == nil || w.input.SubID == nil {
		return 0
	}

	analysisIDStr := strconv.Itoa(analysisID)
	priceValue, exists := w.input.PriceValues[analysisIDStr]
	if !exists {
		return 0
	}

	if priceValue.MinPrice == nil || priceValue.MaxPrice == nil {
		return 0
	}

	minPrice := int(*priceValue.MinPrice)
	maxPrice := int(*priceValue.MaxPrice)

	if minPrice > maxPrice {
		return minPrice
	}

	if minPrice == maxPrice {
		return minPrice
	}

	return minPrice + rand.Intn(maxPrice-minPrice+1)
}

func handleSingleCalculation(input *Input) {
	user := &input.User
	tagUser := calculateTagUser(user)

	worker := CalcWorker{
		input:   input,
		tagUser: tagUser,
	}

	value := worker.calculation(input.UserString, input.AnalysisString, input.Bioryth)

	output := Output{
		Value: value,
	}

	outputJSON(output)
}

func handleFrequencyCalculation(input *Input) {
	analysis := cleanString(input.AnalysisString)
	analysisMatrix := sisReplace(analysis)
	analysisCrossfoot := crossfoot(analysisMatrix)

	pi := math.Pi
	freq := float64(analysisCrossfoot*analysisCrossfoot) * pi
	freq = math.Round(freq*1000) / 1000

	if freq > 1000 {
		for freq > 1000 {
			freq /= 10
		}
	}

	output := Output{
		Frequency: int(math.Round(freq)),
	}

	outputJSON(output)
}

// Helper functions
func getUserString(user *User) string {
	return user.FirstName + user.LastName + user.Gebdatum + user.Gebort + user.ThemaSpeichern
}

func calculateTagUser(user *User) int {
    loc, err := time.LoadLocation("Europe/Vienna")
    if err != nil {
        loc = time.UTC
    }

    // Parse gebdatum - try multiple formats
    var gebdatum time.Time
    gebdatumFormats := []string{
        "2006-01-02", // hyphen format
        "2006/01/02", // slash format
    }

    parsed := false
    for _, format := range gebdatumFormats {
        gebdatum, err = time.ParseInLocation(format, user.Gebdatum, loc)
        if err == nil {
            parsed = true
            break
        }
    }

    if !parsed {
        return 0
    }

    // Handle datumcore - default to current date if empty, "0000-00-00", or invalid
    var userDate time.Time
    if user.Datumcore == "" || user.Datumcore == "0000-00-00" {
        userDate = time.Now().In(loc)
    } else {
        // Try multiple formats for datumcore as well
        parsed := false
        for _, format := range gebdatumFormats {
            userDate, err = time.ParseInLocation(format, user.Datumcore, loc)
            if err == nil {
                parsed = true
                break
            }
        }

        if !parsed {
            userDate = time.Now().In(loc)
        }
    }

    // Truncate both dates to start of day to match PHP's date_diff behavior
    gebdatum = time.Date(gebdatum.Year(), gebdatum.Month(), gebdatum.Day(), 0, 0, 0, 0, loc)
    userDate = time.Date(userDate.Year(), userDate.Month(), userDate.Day(), 0, 0, 0, 0, loc)

    // Calculate the difference in days
    duration := userDate.Sub(gebdatum)
    days := int(duration.Hours()/24 + 0.5) // Add 0.5 for rounding to match PHP behavior

    // PHP's %a format gives absolute value
    if days < 0 {
        days = -days
    }

    return days
}

func cleanString(s string) string {
	reg := regexp.MustCompile(`[^a-zA-Z0-9ßöäüÖÄÜ]`)
	return reg.ReplaceAllString(s, "")
}

func sisReplace(text string) string {
	var result strings.Builder
	for _, char := range text {
		if replacement, exists := charMatrix[char]; exists {
			result.WriteString(replacement)
		}
	}
	return result.String()
}

func crossfoot(digits string) int {
	sum := 0
	for _, char := range digits {
		if num, err := strconv.Atoi(string(char)); err == nil {
			sum += num
		}
	}
	return sum
}

func sortResults(results []AnalysisResult, filterType int, subID *int) {
	switch filterType {
	case 1:
		sort.Slice(results, func(i, j int) bool {
			return results[i].AnaName < results[j].AnaName
		})
	case 2:
		if subID != nil && *subID == 20 {
			sort.Slice(results, func(i, j int) bool {
				return results[i].AnaName < results[j].AnaName
			})
		} else {
			sort.Slice(results, func(i, j int) bool {
				return results[i].AnaName > results[j].AnaName
			})
		}
	case 3:
		if subID != nil && *subID != 20 {
			sort.Slice(results, func(i, j int) bool {
				return results[i].AnaVal < results[j].AnaVal
			})
		}
	case 4:
		if subID != nil && *subID != 20 {
			sort.Slice(results, func(i, j int) bool {
				return results[i].AnaVal > results[j].AnaVal
			})
		}
	default:
		sort.Slice(results, func(i, j int) bool {
			return results[i].AnaName < results[j].AnaName
		})
	}
}

func outputJSON(data interface{}) {
	output, _ := json.Marshal(data)
	fmt.Println(string(output))
}

func outputError(err error) {
	errorOutput := map[string]string{
		"error": err.Error(),
	}
	output, _ := json.Marshal(errorOutput)
	fmt.Println(string(output))
	os.Exit(1)
}

func debugLog(s string) {
	debugMu.Lock()
	defer debugMu.Unlock()
	if debugFile != nil {
		debugFile.WriteString(s)
	}
}