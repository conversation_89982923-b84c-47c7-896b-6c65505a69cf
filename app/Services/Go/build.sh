#!/bin/bash

# Build script for Go calculators
set -e

echo "Building Go calculators..."

# Set Go environment
export GO111MODULE=on
export CGO_ENABLED=0

# Build calc module
echo "Building calc binary..."
cd src/calc
go build -o ../../bin/calc calc.go
chmod +x ../../bin/calc

# Build batch_calc
echo "Building batch_calc binary..."
cd ../batch_calc
go build -o ../../bin/batch_calc batch_calc.go
chmod +x ../../bin/batch_calc

echo "Build complete!"
echo "Binaries created:"
echo "  - bin/calc"
echo "  - bin/batch_calc"