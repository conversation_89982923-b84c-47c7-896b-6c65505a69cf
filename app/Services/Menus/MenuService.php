<?php
namespace App\Services\Menus;

use App\Enums\MenuTypeEnum;
use App\Enums\UserRoll;
use App\Model\Menu;
use App\Model\Order;
use App\Model\Product;
use App\Model\ProductModule;
use App\Model\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class MenuService
{
    public function getUserMenuByUserId($id){
        return Cache::rememberForever('user_all_menus_'.$id, function () use($id){
            if($id === null) return collect();
            $user = User::join('user_options','users.id','user_options.user_id')->find($id,['users.id','users.user_type','user_options.shorting']);
            
            #remove user temp data like session,cache,cockies
            removeUserCacheKeys($id);

            if($user->user_type === UserRoll::Admin || $user->user_type === UserRoll::Therapist || $user->user_type === UserRoll::Demo){
                $content = json_decode($user->shorting, true);
                // Cache::forget('SYSTEM-MENU-'. $user->id);

                $get_system_menus = $this->__all_menus($id);
                // dd($get_system_menus,$id,maxAddUser(),$get_system_menus['maxAddUser']);
                /*Limit Add On session*/
                session()->put('maxAddUser'.$id, $get_system_menus['maxAddUser']);
                session()->put('total_order' . $id,  $get_system_menus['total_order']);
                
                $system_menus = $this->collection_value_sorting($get_system_menus['products'],$content,'sys');
                
                $own_menus = (checkOwnAccess($system_menus->pluck('id')->toArray())) ? $this->collection_value_sorting($this->__all_ownmenus($user, $system_menus),$content,'own') : collect();

                #Merge-System-And-Own-Menus
                if (is_object($system_menus)) {
                    $productLists = $system_menus->merge($own_menus);
                } else {
                    $productLists = $own_menus;
                }

                return $productLists ?? collect(); // Ensure we return a Collection

            }else if($user->user_type === UserRoll::Sub_User){
                Cache::forget('subuser_menus'. $user->id);
                
                return $this->getSubusersMenus($user);
            }
            else return collect(); // Changed from [] to collect()
        });
    }

    /**
     * Get All Menus from order
     * @param id
     * 
     * @return collection
    */
    private function __all_menus($id)
    {
        $langid = (app()->getLocale() == '') ? 'de' : app()->getLocale();
        $locale = app()->getLocale() ?? config('app.locale');
        
        return Cache::rememberForever('SYSTEM-MENU-'.$id, function () use($id, $langid){
            $__productCollection = collect();
            $_products = Order::with(['products'=> function ($queryProduct) use($langid){#get-Products-Under-SingleOrder
                #Select-ProductName-Based-On-Language
                if ($langid != 'de') {
                    $product_column_name = $langid . "_product_name";
                    $queryProduct->select('products.id', 'products.allow_user', 'products.limit_access', 'products.price', 'products.type', 'products.module', 'products.sp_status', 'products.products_view', $product_column_name . ' as product_name');
                }else $queryProduct->select('products.id', 'products.allow_user', 'products.limit_access', 'products.price', 'products.type', 'products.module', 'products.sp_status', 'products.products_view', 'products.product_name');
                $queryProduct->with(['productpackages:product_packages.id,product_packages.view_name','submenu','addon_products' => function($comboquery) use($langid){#join-Submenu-ProductPackage-AddonProducts
                    if ($langid != 'de') {#Select-ProductName-Based-On-Language
                        $product_column_name = $langid . "_product_name";
                        $comboquery->select('products.id', 'products.allow_user', 'products.limit_access', 'products.price', 'products.type', 'products.module', 'products.sp_status', 'products.products_view', $product_column_name . ' as product_name');
                    }else $comboquery->select('products.id', 'products.allow_user', 'products.limit_access', 'products.price', 'products.type', 'products.module', 'products.sp_status', 'products.products_view', 'products.product_name');
                    $comboquery->with('productpackages:product_packages.id,product_packages.view_name','submenu');#join-Submenu-ProductPackage-On-AddonProducts
                }]);
               
            }])->where('user_id', $id)->where('order_status',true)->get(['id','user_id','order_status','demo_status','demo_student'])->each(function($order) use($__productCollection, $id){
                if($order->products->count()){#check-Order-Products
                    $order->products->each(function ($product) use($__productCollection) {#Formet-The-Product-for-system
                        $product->submenus = ($product->submenu->count()) ? $product->submenu->sortBy('sortingNo') : null;
                        $product->sp = $product->sp_status;
                        $product->package_name = ($product->productpackages && $product->productpackages->count()) ? $product->productpackages[0]->view_name : null;
                        $product->menus = null;
                        $product->type  = (int)$product->type;
                        $product->status = ($product->type) ? MenuTypeEnum::Combo : MenuTypeEnum::System;
                        $product->isOn = false;
                        $product->description = $product->productSetting()->first()->description ?? '';
                        

                        if($product->addon_products->count()){#Formet-The-Addon-Product-for-system
                            $_comboMenu = collect();
                            $product->addon_products->each(function ($comPro) use ($_comboMenu, $__productCollection, $product) {
                                $comboProduct = clone $comPro;
                                if ($comboProduct) {
                                    $comboProduct->status = MenuTypeEnum::System;
                                    $comboProduct->submenus = ($comboProduct->submenu && $comboProduct->submenu->count()) ? $comboProduct->submenu->sortBy('sortingNo') : null;
                                    $comboProduct->sp = $comboProduct->sp_status ?? $comboProduct->sp;
                                    $comboProduct->package_name = ($comboProduct->productpackages && $comboProduct->productpackages->count()) ? $comboProduct->productpackages[0]->view_name : null;
                                    $comboProduct->menus = null;
                                    $comboProduct->type = (int) $comboProduct->type;
                                    $comboProduct->description = $comboProduct->productSetting()->first()->description ?? '';
                                    
                                    if ($product->module)
                                        $comboProduct->parent_module = $product->module;

                                    $comboProduct->isOn = false;
                                    
                                    unset($comboProduct->sp_status,$comboProduct->price, $comboProduct->pivot,$comboProduct->moduleName, $comboProduct->productpackages, $comboProduct->submenu, $comboProduct->addon_products);

                                    $_comboMenu->push($comboProduct);
                                    
                                    if (!$__productCollection->contains('id', $comboProduct->id))
                                        $__productCollection->push($comboProduct);
                                }
                            });
                            $product->menus = $_comboMenu;
                        }

                        unset($product->sp_status,$product->price, $product->pivot,$product->moduleName, $product->productpackages, $product->submenu, $product->addon_products);
                        if (!$__productCollection->contains('id', $product->id)) $__productCollection->push($product);
                    });
                }
                if($order->demo_status) {
                    session()->put('demo' . $id, true);
                    session()->put('orderLimit' . $id, ['pids'=> implode(',', $order->products->pluck('id')->toArray()),'limit' => $order->accesslimit, 'affid' => $order->affid, 'order_id' => $order->id,'student'=>$order->demo_student, 'created_at' => $order->created_at]);
                } 
                
            });
            
            $total_order = $_products->count();
            unset($_products);
            return [
                'products' => $__productCollection,
                'maxAddUser' => $__productCollection->max('allow_user'),
                'total_order' => $total_order
            ];
        });
        
    }

    /**
     * Get All Menus from user own
     * @param User, @param System_menus
     * 
     * @return collection
    */
    private function  __all_ownmenus($user, $system_menus){
        #Load-Menus-UserSubmenus-ComboMenus
        $user->load(['menus:id,user_id,name,menu_status','menus.usersubmenus:id,menu_id,name,sortingNo,status','menus.combo_menus']);

        $own_menus = collect();

        $user->menus->each(function($menu) use($own_menus){#Formet-For-Own-Menus
            $menu->product_name = $menu->name;
            $menu->status = ($menu->menu_status) ? MenuTypeEnum::OwnCombo : MenuTypeEnum::Own;
            $menu->menus = ($menu->menu_status) ? $menu->combo_menus : null;
            $menu->submenus = ($menu->usersubmenus->count()) ? $menu->usersubmenus->sortBy('sortingNo') : null;
            $menu->sp = false;
            $menu->package_name = null;
            $menu->module = null;
            
            $menu->isOn = false;
            unset($menu->menu_status, $menu->name, $menu->user_id, $menu->usersubmenus, $menu->combo_menus);
            $own_menus->push($menu);
        });
        unset($user->menus);
        
        $own_menus->where('status',MenuTypeEnum::OwnCombo)->each(function($combo) use($system_menus, $own_menus){
            $_menus = collect();
            $combo->menus->each(function($own_combos) use($system_menus, $own_menus, $_menus){#FormatA-Own-Combo-Menus
                $own_combo = clone $own_combos;
                if($own_combo->type == MenuTypeEnum::System) $menu = $system_menus->where('id',$own_combo->menu_id)->first();
                else $menu = $own_menus->where('id',$own_combo->menu_id)->first();
                
                if($menu) $_menus->push($menu);
            });

            $combo->menus = $_menus;
        });
        return $own_menus;
    }

    /**
     * Did sort all menus based on user sorting setting
     * @param all_menus, @param User_sort_array, @param Sort_for
     * 
     * @return collection
    */
    private function collection_value_sorting($collection, $userSortRecord, $sortFor = null)
    {
        $sysProducts = clone $collection;
        if($userSortRecord == null) return $sysProducts;
        
        if ($sysProducts->count()) {
            $userSortRecord = collect($userSortRecord);
            if ($sortFor !== null) {
                $userSortRecord = collect($userSortRecord[$sortFor] ?? []);
            }

            $filtered = $sysProducts->whereIn('id', $userSortRecord->pluck('proid')->toArray());
            $remaining = $sysProducts->whereNotIn('id', $userSortRecord->pluck('proid')->toArray())->sortBy('id');
            return $filtered->sortBy(function ($item) use ($userSortRecord) {
                if (in_array($item->id, $userSortRecord->pluck('proid')->toArray())) {
                    $userSortData = $userSortRecord->where('proid', $item->id)->first();
                    $item->submenus = $this->collection_submenu_sort($item->submenus, $userSortData['subids'] ?? []);
                }
                return array_search($item->id, $userSortRecord->pluck('proid')->toArray());
            })->values()->concat($remaining);
        } else {
            return collect();
        }
    }

    private function collection_submenu_sort($submenus,$sortdata = array()){
        
        if(!$sortdata || $submenus == null) return $submenus;
        return $submenus->sortBy(function ($item) use ($sortdata) {
            return array_search($item->id, $sortdata);
        })->values();
    }

    /**
     * Get All Subusers Assign menus
     * @param User
     * 
     * @return collection
    */
    private function getSubusersMenus($user){
        $id = $user->id;
        $locale = app()->getLocale() ?? config('app.locale');

        return Cache::rememberForever('subuser_menus'.$id, function () use($user){#User-Menus-Serve
            $submenus = $user->load(['subuserLimitation' => function($limit){
                $limit->whereDate('end_date', '>=', now());
            },'subuserLimitation.submenus','subuserLimitation.ownsubmenus']);

            $system_submenus = $submenus->subuserLimitation->submenus ?? collect();
            $own_submenus = $submenus->subuserLimitation->ownsubmenus ?? collect();

            $sys_proids = ($system_submenus->count()) ? $system_submenus->pluck('product_id')->unique() : collect();#Get-System-Product-Ids
            $own_proids = ($own_submenus->count()) ? $own_submenus->pluck('menu_id')->unique() : collect();#Get-System-Product-Ids

            $system_menu_query = Product::whereIn('id', $sys_proids)->with('productpackages:product_packages.id,product_packages.view_name');#Get-Product-With-Packages
            $langid = (app()->getLocale() == '') ? 'de' : app()->getLocale();
            if ($langid != 'de') {#Select-Product-Based-On-Language
                $product_column_name = $langid . "_product_name";
                    $system_menu_query->select('products.id', 'products.type', 'products.module', 'products.sp_status', 'products.products_view', $product_column_name . ' as product_name');
            }else $system_menu_query->select('products.id', 'products.type', 'products.module', 'products.sp_status', 'products.products_view', 'products.product_name');
            $system_menu = $system_menu_query->get()->map(function($sys_menu) use($system_submenus){#Prepare-The-System-Product
                $sys_menu->sp = $sys_menu->sp_status;
                $sys_menu->package_name = ($sys_menu->productpackages && $sys_menu->productpackages->count()) ? $sys_menu->productpackages[0]->view_name : null;
                $sys_menu->type = (int)$sys_menu->type;
                $sys_menu->status = ($sys_menu->type) ? MenuTypeEnum::Combo : MenuTypeEnum::System;
                $sys_menu->submenus = $system_submenus->where('product_id',$sys_menu->id)->sortBy('sortingNo');
                return $sys_menu;
            });
            $own_menu = Menu::whereIn('id', $own_proids)->get(['id','name','menu_status'])->map(function($own_query) use($own_submenus){#Prepare-The-Own-Product
                $own_query->product_name = $own_query->name;
                $own_query->status = ($own_query->menu_status) ? MenuTypeEnum::OwnCombo : MenuTypeEnum::Own;
                $own_query->submenus = $own_submenus->where('menu_id',$own_query->id)->sortBY('sortingNo');
                $own_query->sp = false;
                $own_query->package_name = null;
                $own_query->module = null;
                return $own_query;
            });        
            
            return $system_menu->merge($own_menu);
        });
    }



    public function getMenuCollectionDesign($id){
        // if(Cache::has('user_all_menus_'. $id)) Cache::forget('user_all_menus_'. $id);

        $getAllMenus = $this->getUserMenuByUserId($id);

        $all_menus = empty($getAllMenus) ? collect() : clone $getAllMenus;

        $sorting = DB::table('user_options')->where('user_id',getAuthID())->first('shorting')->shorting;
        $sorting = $sorting ? json_decode($sorting, true) : null;

        $system = collect();

        if(!$all_menus->count()) return $system;

        $_returnSystemMenus = collect();

        $this->collection_value_sorting($all_menus->where('status', 1),$sorting,'sys')->each(function($sys_menus) use($_returnSystemMenus){
            $sys = clone $sys_menus;
            $sys->link = $this->getProductUrl($sys);
            if($sys->status === MenuTypeEnum::Combo && $sys->menus) $sys->menus->each(function($scmenu) use($sys){
                $scmenu->link = $this->getProductUrl($scmenu,$sys->id);
                if(session()->get('_menu_active' . getAuthID()) && !isset(request()->menu_head) && isset(request()->proid) && isset(request()->comid) && request()->proid == $scmenu->id){
                    $scmenu->isOn = true;
                    if(isset(request()->subid) && $scmenu->submenus) $scmenu->submenus->each(function($_submenu){
                        if($_submenu->id == request()->subid) $_submenu->isOn = true;
                        return $_submenu;
                    });
                }
                return $scmenu;
            });
            if(session()->get('_menu_active' . getAuthID()) && !isset(request()->comid) && request()->proid == $sys->id && (!isset(request()->menu_head) || request()->menu_head == '' || request()->menu_head == 0)){
                $sys->isOn =  true;
                if(isset(request()->subid) && $sys->submenus) $sys->submenus->map(function($_submenu){
                    if($_submenu->id == request()->subid) $_submenu->isOn = true;
                    return $_submenu;
                });
            }else $sys->isOn = false;
            $_returnSystemMenus->push($sys);
        });

        $system->put(trans('action.systemMenu')."__sys", $_returnSystemMenus);
        $this->getModules()->map(function($module) use($all_menus, $system, $sorting){
            $moduleMenus = collect();
            $system_mennus = collect();
            $system_mennus = $all_menus->whereNotNull('module')->where('module', $module->get('id'));
            $parent_menu = $all_menus->where('parent_module', $module->get('id'));
            $comboChild = $all_menus->whereNotNull('module')->where('module', $module->get('id'))->where('status',3)->first();
                        
            if ($comboChild) $parent_menu = $comboChild->menus;
            $system_mennus->merge($parent_menu)->where('status',MenuTypeEnum::System)->each(function($mquery) use($module,$moduleMenus){
                                
                $q = clone $mquery;
                $combo_menus_lists = collect();
                if($q->status === MenuTypeEnum::Combo && $q->menus) $q->menus->each(function($mmenu) use($q,$combo_menus_lists){
                    if($mmenu){
                        $menu = clone $mmenu;
                        if($menu->link != 'javascript:void(0)') $menu->link = $this->getProductUrl($menu,$q->id)."?menu_head=".$menu->parent_module;
                        if(session()->get('_menu_active' . getAuthID()) && $menu->parent_module == request()->menu_head && request()->comid == $q->id && request()->proid == $menu->id){
                            $menu->isOn = true;
                            if(isset(request()->subid) && $q->submenus) $q->submenus->map(function($_submenu){
                                if($_submenu->id == request()->subid) $_submenu->isOn = true;
                                return $_submenu;
                            });
                        }else $menu->isOn = false;
                        $combo_menus_lists->push($menu);
                    }
                });
                if($combo_menus_lists->count()) $q->menus = $combo_menus_lists;
                if($q->link != 'javascript:void(0)') $q->link = $this->getProductUrl($q)."?menu_head=".$module->get('id');
                
                if(session()->get('_menu_active' . getAuthID()) && $module->get('id') == request()->menu_head && !isset(request()->comid) && request()->proid == $q->id){
                    $q->isOn = true;
                    if(isset(request()->subid) && $q->submenus) $q->submenus->map(function($sub) use($module){
                        if($sub->id == request()->subid && $module->get('id') == request()->menu_head) $sub->isOn = true;
                        return $sub;
                    });
                }else $q->isOn = false;
                if(!$moduleMenus->containsStrict('id',$q->id)) $moduleMenus->push($q);
            });
            // if($system_mennus->count()) $system->put($module->get('module_name')."__".$module->get('id') , $moduleMenus);
            if($moduleMenus->count()) 
                $system->put($module->get('module_name')."__".$module->get('id')."__".$module->get('type') ,  $this->collection_value_sorting($moduleMenus,$sorting,$module->get('id')));
        });
        
        $_returnOwnMenus = collect();
        $this->collection_value_sorting($all_menus->where('status', 2),$sorting,'own')->each(function($own_menus) use($_returnOwnMenus){
            $own = clone $own_menus;
            if($own->status === MenuTypeEnum::OwnCombo && $own->menus) $own->menus->map(function($menu) use($own){
                
                if(session()->get('_menu_active' . getAuthID()) && !isset(request()->menu_head) && isset(request()->ownid) && isset(request()->comid)){
                    if(request()->ownid == $menu->id) {
                        if(isset(request()->ownsubid)) $menu->submenus->map(function($_ownsubmenu){
                            if($_ownsubmenu->id == request()->ownsubid) $_ownsubmenu->isOn = true;
                            return $_ownsubmenu;
                        });
                    }
                }

                $menu->link = $this->getProductUrl($menu,$own->id);
                return $menu;
            });
            if(!isset(request()->comid) && session()->get('_menu_active' . getAuthID()) && !isset(request()->menu_head) && request()->ownid == $own->id){
                $own->isOn = true;
                if(isset(request()->ownsubid)) $own->submenus->map(function($_ownsubmenu){
                    if($_ownsubmenu->id == request()->ownsubid) $_ownsubmenu->isOn = true;
                    return $_ownsubmenu;
                });
            }
            
            $own->link = $this->getProductUrl($own);

            $_returnOwnMenus->push($own);
        });
        if($_returnOwnMenus->count())$system->put(trans('action.ownMenu')."__own" ,$_returnOwnMenus);
        // dd($system,Cache::has('subuser_menus'. Auth::id()));
        $this->collection_value_sorting($all_menus->where('status', MenuTypeEnum::OwnCombo), $sorting, 'ownCombo')->each(function ($ownCombo_menus) use ($system) {
            $ownCombo = clone $ownCombo_menus;
            if ($ownCombo->status === MenuTypeEnum::OwnCombo && $ownCombo->menus) $ownCombo->menus->map(function ($menu) use ($ownCombo) {
                //For selected active menu head
                if (session()->get('_menu_active' . getAuthID()) && isset(request()->menu_head) && isset(request()->ownC)) {
                    $menu->isOn = request()->proid ? request()->proid == $menu->id : (request()->ownid ? request()->ownid == $menu->id : false);
                }
                if($menu->submenus){
                    //For selected active sub menu
                    $menu->submenus->map(function ($submenu) {
                        if ($submenu->id == request()->subid ||
                            $submenu->id == request()->ownsubid
                        ) {
                            $submenu->isOn = true;
                        }
                        // $submenu->link = $this->getProductUrl($submenu) . "?menu_head=" . $submenu->id . "&ownC=0";
                        return $submenu;
                    });
                }
                if($menu->submenus)  $menu->sMenuHead = "?menu_head=" . $ownCombo->id . "&ownC=0";
                $menu->link =  $this->getProductUrl($menu) . "?menu_head=" . $ownCombo->id . "&ownC=0";
                //for submenu menu head  link
                $menu->module = $ownCombo->id . "&ownC=0";
                return $menu;
            });
            $system->put($ownCombo->product_name . "__" . $ownCombo->id, collect($ownCombo->menus));
        });
        return $system;
    }

    private function getModules(){
        return Cache::rememberForever('SYSTEM-MODULE', function () {
            return ProductModule::orderBy('sortingNo')->get();
        })->map(function($module){
            $module_name = $module->{(app()->getLocale() != '' && app()->getLocale() != 'de') ? app()->getLocale() . '_module_name' : 'module_name'};
            if($module_name === null) $module_name = $module->module_name;

            return collect([
                'id' => $module->id,
                'module_name' => $module_name,
                'type'=>'module_head'
            ]);
        });
    }

    private function getSystemMenus($menus){
        $return_menus = collect();
        $menus->map(function($sys_menus){
            $sys = clone $sys_menus;
            $sys->link = $this->getProductUrl($sys);
            if($sys->status === MenuTypeEnum::Combo) $sys->menus->map(function($scmenu) use($sys){
                $scmenu->link = $this->getProductUrl($scmenu,$sys->id);
                if(session()->get('_menu_active' . getAuthID()) && !isset(request()->menu_head) && isset(request()->proid) && isset(request()->comid)){
                    $scmenu->isOn = true;
                    if(isset(request()->subid) && $scmenu->submenus) $scmenu->submenus->map(function($sub){
                        if($sub->id == request()->subid) $sub->isOn = true;
                        return $sub;
                    });
                }
                return $scmenu;
            });
            if(session()->get('_menu_active' . getAuthID()) && !isset(request()->menu_head) && !isset(request()->comid) && request()->proid == $sys->id){
                $sys->isOn = true;
                if(isset(request()->subid) && $sys->submenus) $sys->submenus->map(function($sub){
                    if($sub->id == request()->subid) $sub->isOn = true;
                    return $sub;
                });
            }else $sys->isOn = false;
            return $sys;
        });
        return $return_menus;
    }


    private function getProductUrl($product, $com = false){
        if($product->status == MenuTypeEnum::System || $product->status == MenuTypeEnum::Combo){
            if(!$com){
                if($product->package_name == 'Premium') return route('dashboard.preproduct', [$product->id]);
                elseif($product->package_name == 'Frabklang') return route('frabklang.frabklang', [$product->id]);
                elseif($product->sp || $product->status === MenuTypeEnum::Combo) return "javascript:void(0)";
                else return route('dashboard.products', [$product->id]);
            }else{
                if($product->package_name == 'Premium') return route('dashboard.combo.preproduct', [$product->id,$com]);
                elseif($product->package_name == 'Frabklang') return route('frabklang.combo', [$product->id,$com]);
                elseif($product->sp || $product->status === MenuTypeEnum::Combo) return "javascript:void(0)";
                else return route('dashboard.combo.products', [$product->id,$com]);            
            }
        }else{
            if($product->status == MenuTypeEnum::OwnCombo){
                return "javascript:void(0)";
            }else return $link = ($com) ? route('dashboard.combo.ownProducts', [$product->id, $com]) : route('dashboard.ownProducts', [$product->id]);
        }
    }
    
 


    
}