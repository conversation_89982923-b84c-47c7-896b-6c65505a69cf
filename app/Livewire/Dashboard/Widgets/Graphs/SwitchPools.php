<?php

namespace App\Livewire\Dashboard\Widgets\Graphs;

use App\Model\Pool;
use App\Model\Dashboards\Dashboard as DashboardModel;
use Livewire\Component;

class SwitchPools extends Component
{
    public $widgetId;
    public $poolType;
    public $poolIds = [];
    public $pools = [];
    public $poolsLoaded = false;
    public $isExpanded = false;

    public function mount($widgetId, $poolType, $poolIds)
    {
        $this->widgetId = $widgetId;
        $this->poolType = $poolType;
        $this->poolIds = $poolIds;
    }

    public function loadPools()
    {
        if (!$this->poolsLoaded && empty($this->pools)) {
            $this->pools = Pool::whereIn('id', $this->poolIds)
                ->orderBy('pool_name', 'ASC')
                ->get()
                ->map(fn ($pool) => [
                    'id' => $pool->id,
                    'name' => $pool->pool_name,
                    'is_selected' => $pool->id == $this->poolType,
                ])
                ->toArray();
            $this->poolsLoaded = true;
        }
        
        // Toggle the expanded state
        $this->isExpanded = !$this->isExpanded;
    }

    public function switchPool($poolId, $targetWidgetId)
    {
        $this->poolType = $poolId; // update current state
        
        // Change this line to use dynamic event name
        $this->dispatch("updateWidgetPoolType.{$targetWidgetId}", poolId: $poolId);
    }

    public function render()
    {
        return view('livewire.dashboard.widgets.graphs.switch-pools');
    }
}
