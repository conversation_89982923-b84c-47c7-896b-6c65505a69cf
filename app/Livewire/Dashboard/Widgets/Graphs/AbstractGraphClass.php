<?php

namespace App\Livewire\Dashboard\Widgets\Graphs;

use App\Enums\DiagramType;
use App\Services\Dashboard\WidgetViewService;
use App\Traits\LivewireGeneralFunctions;
use App\Traits\PoolAnalysesGeneralFunctions;
use Livewire\Attributes\On;
use Livewire\Component;

abstract class AbstractGraphClass extends Component
{
    use LivewireGeneralFunctions, PoolAnalysesGeneralFunctions;

    public $uniqueId;
    public $widgetId;
    public $poolId;
    public $settings;
    public $results;
    public $labels;
    public $fullResults;
    public $filter;
    public $selectedLongDay;
    protected static DiagramType $diagramType;

    public function mount($widgetId, $poolId, $settings = [])
    {
        $this->uniqueId = $this->poolId . '_' . rand();
        $this->selectedLongDay = session("user_" . getUserId() . "_" . 'longday');
        $this->widgetId = $widgetId;
        $this->poolId = (int)$poolId;
        $this->settings = $settings;
        $this->fetchResults();
    }

    #[On('daySelected')]
    public function handleDaySelected($day)
    {
        $this->selectedLongDay = $day;
        $this->fetchResults();
    }

    #[On('dateUpdated')]
    #[On('filterChanged')]
    #[On('OwnTopicUpdated')]
    public function handleFilterUpdated()
    {
        $this->fetchResults();
    }
    
    #[On('updateWidgetPoolType.{widgetId}')]
    public function handleSwitchPoolType($poolId)
    {
        $this->poolId = (int)$poolId;
        $this->fetchResults();
    }

    protected function fetchResults()
    {
        $widgetService = app(WidgetViewService::class);
        $sorting = $this->settings['sorting'] ?? [];

        $results = $widgetService->processWidgetView(
            $this->poolId,
            static::$diagramType->value,
            ['longDay' => $this->selectedLongDay],
            $sorting
        ) ?? [];

        $this->fullResults = $results['full_results'] ?? [];
        $this->results = $results['results'] ?? [];
        $this->labels = $results['labels'] ?? [];
        $this->filter = $results['filter'] ?? false;
        
        // Dispatch with both uniqueId and widgetId for more specific targeting
        $this->dispatch('graphResultUpdated', 
            uniqueId: $this->uniqueId,
            widgetId: $this->widgetId,
            data: [
                'results' => $this->results,
                'labels' => $this->labels,
                'fullResults' => $this->fullResults,
                'filter' => $this->filter,
            ]
        );
    }
}