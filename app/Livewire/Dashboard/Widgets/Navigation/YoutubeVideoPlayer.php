<?php

namespace App\Livewire\Dashboard\Widgets\Navigation;

use App\Services\Dashboard\WidgetViewService;
use Livewire\Component;

class YoutubeVideoPlayer extends Component
{
    public $widget;
    public $videoId;

    public function mount($widget)
    {
        $this->widget = $widget;
        $this->extractVideoId();
    }

    private function extractVideoId()
    {
        $url = $this->widget['settings']['youtube_link'] ?? '';
        
        // Pattern to match various YouTube URL formats
        $patterns = [
            '/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube-nocookie\.com\/embed\/)([a-zA-Z0-9_-]+)/',
            '/^([a-zA-Z0-9_-]+)$/' // Direct video ID
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url, $matches)) {
                $this->videoId = $matches[1];
                return;
            }
        }
        
        $this->videoId = null;
    }

    public function render()
    {
        return view('livewire.dashboard.widgets.navigation.youtube-video-player', [
            'title' => $this->widget['title'] ?? '',
            'videoId' => $this->videoId
        ]);
    }
}