<?php

namespace App\Livewire\Dashboard\Widgets\Navigation;

use Livewire\Component;
use App\Traits\LivewireGeneralFunctions;
use App\Services\Calculation\FrequencyService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Contracts\View\View;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Request;
/**
 * FrequencyGenerator Livewire Component
 *
 * Handles frequency generation based on topic text input,
 * harmonic calculations, and cart integration.
 */
class FrequencyGenerator extends Component
{
    use LivewireGeneralFunctions;

    /**
     * @var int Pool identifier
     */
    public int $poolId;

    /**
     * @var array Widget configuration
     */
    public array $widget = [];

    /**
     * @var object|null User details from backend
     */
    public ?object $userDetails = null;

    /**
     * @var object|null Biorhythm configuration details
     */
    public ?object $biorythDetails = null;

    /**
     * @var int Random time value within configured bounds
     */
    public int $randomTime = 30;

    /**
     * @var int Calculated frequency based on topic
     */
    public int $calculatedFrequency = 0;

    /**
     * @var string Topic text input
     */
    public string $topicText = '';

    /**
     * @var string Frequency in Hz
     */
    public string $frequencyHz = '';

    /**
     * @var string Time in seconds
     */
    public string $frequencyTime = '';

    /**
     * @var bool Flag to track if frequency was manually entered
     */
    public bool $isManualFrequency = false;

    /**
     * @var array Validation rules
     */
    protected $rules = [
        'topicText' => 'nullable|string|max:500',
        'frequencyHz' => 'required|numeric|min:0',
        'frequencyTime' => 'nullable|numeric|min:5|max:3600'  // Changed to nullable
    ];

    /**
     * Get FrequencyService from container
     *
     * @return FrequencyService
     */
    protected function getFrequencyService(): FrequencyService
    {
        return app(FrequencyService::class);
    }

    /**
     * Component mount lifecycle
     *
     * @param int $poolId Pool identifier
     * @param array $widget Widget configuration
     * @return void
     */
    public function mount(int $poolId, array $widget = []): void
    {
        $this->poolId = $poolId;
        $this->widget = $widget;
        try {
            $this->initializeComponentData();
        } catch (\Exception $e) {
            $this->setDefaultValues();
        }
    }

    /**
     * Initialize component data from backend services
     *
     * @return void
     * @throws \Exception
     */
    protected function initializeComponentData(): void
    {
        // Cache user details for performance
        $cacheKey = 'user_details_' . getUserId();
        $this->userDetails = Cache::remember($cacheKey, 300, function () {
            return getUserDetails();
        });

        if (!$this->userDetails) {
            throw new \Exception('User details not found');
        }

        // Get biorhythm details
        $this->biorythDetails = biorythVisibleDetails();

        if (!$this->biorythDetails) {
            throw new \Exception('Biorhythm details not found');
        }

        // Generate random time within bounds but don't set it yet
        $this->randomTime = $this->getFrequencyService()->generateRandomTime($this->biorythDetails);

        // Get topic and calculate frequency
        $topicText = $this->getFrequencyService()->sanitizeInput($this->userDetails->thema_speichern ?? '');

        if (!empty($topicText)) {
            $this->calculatedFrequency = $this->getFrequencyService()->calculateFrequencySafely($topicText);
        }

        // Set initial values - leave frequencyTime empty
        $this->topicText = $topicText;
        $this->frequencyHz = $this->calculatedFrequency > 0 ? (string)$this->calculatedFrequency : '';
        $this->frequencyTime = ''; // Keep empty on initialization
    }

    /**
     * Set default values when initialization fails
     *
     * @return void
     */
    protected function setDefaultValues(): void
    {
        $this->userDetails = null;
        $this->biorythDetails = null;
        $this->randomTime = 30;
        $this->calculatedFrequency = 0;
        $this->topicText = '';
        $this->frequencyHz = '';
        $this->frequencyTime = ''; // Keep empty on initialization
    }

    /**
     * Handle property updates with rate limiting
     *
     * @param string $propertyName
     * @return void
     */
    public function updated(string $propertyName): void
    {
        // Clear validation errors
        $this->resetErrorBag($propertyName);
        // Handle topic text updates
        if ($propertyName === 'topicText') {
            // Only auto-calculate if frequency wasn't manually entered
            if (!empty(trim($this->topicText)) && !$this->isManualFrequency) {
                $this->updateFrequencyFromTopic();
            }
        }
        
        // Handle frequency manual input
        if ($propertyName === 'frequencyHz') {
            // Mark as manual input if user types something
            if (!empty(trim($this->frequencyHz))) {
                $this->isManualFrequency = true;
            } else {
                // Reset flag if frequency is cleared
                $this->isManualFrequency = false;
            }
        }
    }

    /**
     * Update frequency based on topic text with rate limiting
     *
     * @return void
     */
    protected function updateFrequencyFromTopic(): void
    {
        $userId = getUserId();

        $this->getFrequencyService()->updateFrequencyFromTopic(
            $this->topicText,
            $userId,
            function($frequency) {
                $this->frequencyHz = (string)$frequency;
                // Also generate new random time when frequency is updated
                $this->randomTime = $this->getFrequencyService()->generateRandomTime($this->biorythDetails);
                $this->frequencyTime = (string)$this->randomTime;
            },
            function($result) {
                if ($result['rate_limited']) {
                    $this->showToastr('warning', trans('action.frequency_generator'), $result['message']);
                }
            }
        );
    }
    /**
     * Add frequency to cart with transaction handling
     *
     * @return void
     */
    public function addToCart(): void
    {
        try {
            // Use local variable for time - don't update the component property
            $timeForSubmission = !empty(trim($this->frequencyTime)) ? $this->frequencyTime : '10';

            // Validate inputs (but temporarily set frequencyTime for validation if empty)
            $originalFrequencyTime = $this->frequencyTime;
            if (empty(trim($this->frequencyTime))) {
                $this->frequencyTime = '10'; // Temporarily set for validation
            }
           
            $this->validate();
            // RESTORE ORIGINAL VALUE IMMEDIATELY AFTER VALIDATION
            $this->frequencyTime = $originalFrequencyTime;

            // Additional business logic validation - pass the computed time
            $this->validateBusinessRules($timeForSubmission);

            // Rate limit cart additions (5 per minute)
            $key = 'cart_add_' . getUserId();
            if (RateLimiter::tooManyAttempts($key, 5)) {
                $seconds = RateLimiter::availableIn($key);
                throw new \Exception(trans('action.too_many_cart_additions', ['seconds' => $seconds]));
            }

            RateLimiter::hit($key, 60);

            $response = $this->getFrequencyService()->submitToSajaxController($this->prepareCartData($timeForSubmission));
            $this->handleCartResponse($response, $this->frequencyHz);

        } catch (ValidationException $e) {
            // ALSO RESTORE ON VALIDATION ERROR
            $this->frequencyTime = $originalFrequencyTime ?? '';
            return;
        } catch (\Exception $e) {
            $this->showToastr('error', trans('action.frequency_generator'),
            $e->getMessage() ?: trans('action.error_occurred'));
        }
    }

    /**
     * Validate business rules beyond basic validation
     *
     * @param string|null $timeValue The computed time value to validate
     * @throws \Exception
     */
    protected function validateBusinessRules(?string $timeValue = null): void
    {
        $time = (int)($timeValue ?? $this->frequencyTime ?: '10');
        
        // Only validate topic-based rules if topic is provided
        if (!empty(trim($this->topicText))) {
            $validationResult = $this->getFrequencyService()->validateBusinessRules(
                $this->topicText, 
                $time, 
                $this->biorythDetails
            );

            if (!$validationResult['valid']) {
                throw new \Exception(implode(', ', $validationResult['errors']));
            }
        }
    }

    /**
     * Prepare cart data for submission
     *
     * @param string|null $timeForSubmission The time value to use for submission
     * @return Request
     */
    protected function prepareCartData(?string $timeForSubmission = null): Request
    {
        $timeValue = $timeForSubmission ?? $this->frequencyTime;
        
        // Use "Frequenz" as default name if no topic is provided
        $topicName = trim($this->topicText) ?: 'Frequenz';

        return new Request([
            'ana_id'      => 1,
            'name'        => $this->getFrequencyService()->sanitizeInput($topicName),
            'submenu_id'  => '',
            'proID'       => '',
            'calculation' => '',
            'male'        => '',
            'heart'       => '',
            'price'       => (int)$timeValue,
            'causes_id'   => '',
            'medium_id'   => '',
            'tipp_id'     => '',
            'color'       => '',
            'type'        => 'Topic',
            'frequency'   => (float)$this->frequencyHz,
            'time'        => (int)$timeValue,
            '_token' => csrf_token()
        ]);
    }

    /**
     * Handle cart API response
     *
     * @param \Illuminate\Http\Client\Response $response
     * @throws \Exception
     */
    protected function handleCartResponse($response, $frequencyHz): void
    {
        $data = $response->getData(true);

        if ($data['success'] ?? false) {
            // Clear form and reset with new values
            $this->resetFormWithNewValues();
            if($frequencyHz >= 250 && $frequencyHz <= 10000){
                $this->showToastr('success', trans('action.frequency_generator'),trans('action.topic_cart_save'));
            }else{
                $this->showToastr('success', trans('action.frequency_generator'),trans('action.hearable_area'));
            }
            // Update cart UI
            $this->dispatch('cartUpdated');
        } else {
            $message = $data['message'] ?? trans('action.error_occurred');
            // Check for specific error types
            if (isset($data['error_code'])) {
                switch ($data['error_code']) {
                    case 'cart_full':
                        $message = trans('action.cart_max_allow_alert');
                        break;
                }
            }
            throw new \Exception($message);
        }
    }

    /**
     * Reset form with new random values
     *
     * @return void
     */
    protected function resetFormWithNewValues(): void
    {
        $this->reset(['topicText', 'frequencyHz','frequencyTime']); 
        // Reset manual frequency flag
        $this->isManualFrequency = false;
    }

    /**
     * Get custom validation messages
     *
     * @return array
     */
    protected function messages(): array
    {
        return [
            'topicText.max' => trans('action.topic_too_long'),
            'frequencyHz.required' => trans('action.frequency_time_both_required'),
            'frequencyHz.numeric' => trans('action.frequency_must_be_number'),
            'frequencyTime.numeric' => trans('action.time_must_be_number'),
            'frequencyTime.min' => trans('action.invalid_time'),
            'frequencyTime.max' => trans('action.invalid_time'),
        ];
    }

    /**
     * Render the component
     *
     * @return View
     */
    public function render(): View
    {
        return view('livewire.dashboard.widgets.navigation.frequency-generator');
    }
}