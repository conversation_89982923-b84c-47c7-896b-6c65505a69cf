<?php

namespace App\Livewire\Dashboard\Widgets\Navigation;

use Carbon\Carbon;
use App\Model\User;
use Livewire\Component;
use App\Enums\FilterType;
use Livewire\Attributes\Computed;
use Illuminate\Support\Facades\DB;
use App\Services\AnalysisCacheService;
use App\Traits\LivewireGeneralFunctions;

class Filters extends Component
{
    use LivewireGeneralFunctions;

    public ?string $selectedLongDay = null;
    public bool $showDateModal = false;
    public ?string $dateInput = null;
    public string $originalDate;
    public bool $filterMonth = false;
    public bool $filterYear = false;
    public ?int $userFilterType = null;

    private User $user;
    private AnalysisCacheService $cacheService;

    public function boot(): void
    {
        $this->user = User::find(getUserId());
        $this->cacheService = new AnalysisCacheService(getUserId());
    }

    public function mount(): void
    {
        $this->selectedLongDay = session($this->sessionKey('longday'));
        $this->userFilterType = $this->selectedLongDay 
            ? FilterType::NUMERICAL_DESC->value
            : $this->user->userfilter->filter_type ?? FilterType::NUMERICAL_DESC->value;

        $this->storeOriginalFilterType();
        $this->setCalculationFilters();
        $this->setDateFromUser();
    }

    public function updateLongDay($value): void
    {
        $this->selectedLongDay = $value;
        session([$this->sessionKey('longday') => $value]);

        if ($value) {
            $this->storeOriginalFilterType();
            $this->updateUserFilterType(FilterType::NUMERICAL_DESC->value);
        } else {
            $originalType = session($this->sessionKey('filter_type'), FilterType::NUMERICAL_DESC->value);
            $this->updateUserFilterType($originalType);
            session()->forget($this->sessionKey('filter_type'));
        }

        $this->dispatchUpdates('daySelected', ['day' => $value]);
    }

    public function updateFilter($filterId): void
    {
        if ($this->selectedLongDay) return;

        $this->updateUserFilterType(FilterType::from($filterId)->value);
        $this->dispatchUpdates('filterChanged');
    }

    public function toggleFilter(string $type): void
    {
        $isMonth = $type === 'month';
        $shouldToggle = $isMonth ? !$this->user->calculation_with : $this->user->calculation_with;
        
        if (!$shouldToggle) return;

        $this->filterMonth = $isMonth;
        $this->filterYear = !$isMonth;
        $this->user->update(['calculation_with' => $isMonth]);
        
        $this->dispatchUpdates('filterChanged', ['type' => $type, 'value' => $isMonth]);
        $this->showToastr('success', trans('action.setting_saved_succfully'), trans('action.setting_saved_succfully'));
    }

    public function openDateModal(): void
    {
        $this->setDateFromUser();
        $this->showDateModal = true;
    }

    public function closeDateModal(): void
    {
        $this->showDateModal = false;
    }

    public function saveDate(): void
    {
        $this->handleDateUpdate($this->dateInput);
    }

    public function resetDate(): void
    {
        $this->handleDateUpdate(Carbon::now()->format('Y-m-d'));
    }

    #[Computed]
    public function ltaDays()
    {
        return DB::table('lta_days')
            ->orderBy('days')
            ->get(['id', 'days']);
    }

    // Private helper methods
    private function sessionKey(string $key): string 
    {
        return "user_" . getUserId() . "_" . $key;
    }

    private function storeOriginalFilterType(): void
    {
        $sessionKey = $this->sessionKey('filter_type');
        
        if ($this->selectedLongDay && !session()->has($sessionKey)) {
            session([$sessionKey => $this->user->userfilter->filter_type ?? FilterType::NUMERICAL_DESC->value]);
        }
    }

    private function setCalculationFilters(): void
    {
        $this->filterMonth = $this->user->calculation_with === true;
        $this->filterYear = $this->user->calculation_with === false;
    }

    private function setDateFromUser(): void
    {
        $userDate = $this->user->datumcore ?? date('Y-m-d');
        $this->originalDate = $this->dateInput = $userDate;
    }

    private function updateUserFilterType(int $filterType): void
    {
        $this->userFilterType = $filterType;
        $this->user->userfilter->update(['filter_type' => $filterType]);
    }

    private function handleDateUpdate(string $newDate): void
    {
        $this->dateInput = $this->originalDate = $newDate;
        
        $this->user->update([
            'datumcore' => $newDate === date('Y-m-d') ? Carbon::now()->format('Y-m-d') : getEfitDateFormat($newDate)
        ]);

        $this->dispatchUpdates('dateUpdated', [$this->originalDate]);
        $this->showDateModal = false;
        $this->showToastr('success', trans('action.setting_saved_succfully'), trans('action.setting_saved_succfully'));
    }

    private function dispatchUpdates(string $event, array $params = []): void
    {
        $this->cacheService->clearAll();
        $this->dispatch($event, ...$params);
    }

    public function render()
    {
        return view('livewire.dashboard.widgets.navigation.filters');
    }
}