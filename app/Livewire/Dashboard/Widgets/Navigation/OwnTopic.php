<?php

namespace App\Livewire\Dashboard\Widgets\Navigation;

use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Traits\LivewireGeneralFunctions;
use App\Services\Calculation\FrequencyService;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Http\Request;
use Livewire\Attributes\On;

class OwnTopic extends Component
{
   use LivewireGeneralFunctions;

    public $poolId;
    public $widget = [];
    public $userDetails;
    public $topicText = '';
    public $farbklang = false;
    public $showFrequencyGenerator = false;
    public $showYoutubePlayer = false;
    public $currentTitle = '';

    protected $rules = [
        'topicText' => 'nullable|string|max:1000',
    ];

    /**
     * Get FrequencyService from container
     *
     * @return FrequencyService
     */
    protected function getFrequencyService(): FrequencyService
    {
        return app(FrequencyService::class);
    }

    public function mount()
    {
        $this->userDetails = getUserDetails();
        $this->topicText = $this->userDetails->thema_speichern ?? '';
        // Initialize the current title
        $this->currentTitle = $this->widget['title'] ?? trans('action.own_topic');
    }

    protected function isTopicTextEmpty(): bool
    {
        return empty(trim($this->topicText));
    }

    protected function rateLimitKey(): string
    {
        return 'cart_add_' . getUserId(); // assumes getUserId() is globally available
    }

    protected function hasExceededRateLimit(): bool
    {
        return RateLimiter::tooManyAttempts($this->rateLimitKey(), 10);
    }

    protected function prepareCartData(): Request
    {
        return new Request([
            'ana_id'      => 1,
            'name'        => trim($this->topicText),
            'submenu_id'  => '',
            'proID'       => '',
            'calculation' => '',
            'male'        => '',
            'heart'       => '',
            'causes_id'   => '',
            'medium_id'   => '',
            'tipp_id'     => '',
            'color'       => '',
            'type'        => 'Topic'
        ]);
    }

    protected function warnUser(string $messageKey): void
    {
        $this->showToastr('warning', trans('action.warning'), trans("action.$messageKey"));
    }

    protected function successUser(string $messageKey): void
    {
        $this->showToastr('success', trans('action.success'), trans("action.$messageKey"));
    }

    protected function errorUser(string $messageKey): void
    {
        $this->showToastr('error', trans('action.error'), trans("action.$messageKey"));
    }

    protected function logCartError(\Throwable $e): void
    {
        Log::error('Cart addition failed', [
            'error' => $e->getMessage(),
            'topic' => substr($this->topicText, 0, 50) . '...',
            'trace' => $e->getTraceAsString(),
        ]);
    }

    public function addToCart(): void
    {
        if ($this->isTopicTextEmpty()) {
            $this->warnUser('no_product_title');
            return;
        }

        if ($this->hasExceededRateLimit()) {
            $this->warnUser('cart_max_allow_alert');
            return;
        }

        RateLimiter::hit($this->rateLimitKey(), 60);

        try {
            $response = $this->getFrequencyService()->submitToSajaxController($this->prepareCartData());

            $data = $response->getData(true);

            if ($data['success'] ?? false) {
                $this->successUser('added_successfully');
                $this->dispatch('cartUpdated');
            } else {
                $this->errorUser($data['message'] ?? 'cart_add_failed');
            }
        } catch (\Throwable $e) {
            $this->logCartError($e);
            $this->errorUser('cart_add_failed');
        }
    }
 

    public function saveTopic()
    {
        try {
            $updated = DB::table('users')
                ->where('id', getUserId())
                ->update(['thema_speichern' => $this->topicText]);

            $this->clearAnalysisCache();
            if ($updated) {
                $this->showToastr('success', trans('action.success'), trans('action.topic_saved_successfully'));
                // Update userDetails to reflect the change
                $this->userDetails = getUserDetails();
                $this->dispatch('OwnTopicUpdated');
            } else {
                $this->showToastr('error', trans('action.error'), trans('action.topic_save_failed'));
            }
        } catch (\Exception $e) {
            Log::error('Topic save failed', [
                'error' => $e->getMessage(),
                'user_id' => getUserId()
            ]);
            $this->showToastr('error', trans('action.error'), trans('action.topic_save_failed'));
        }
    }

    public function deleteTopic()
    {
        try {
            $this->topicText = '';
            $updated = DB::table('users')
                ->where('id', getUserId())
                ->update(['thema_speichern' => '']);
            $this->clearAnalysisCache();
            if ($updated) {
                $this->showToastr('success', trans('action.success'), trans('action.topic_deleted_successfully'));
                // Update userDetails to reflect the change
                $this->userDetails = getUserDetails();
                $this->dispatch('OwnTopicUpdated');
            } else {
                $this->showToastr('error', trans('action.error'), trans('action.topic_delete_failed'));
            }
        } catch (\Exception $e) {
            Log::error('Topic delete failed', [
                'error' => $e->getMessage(),
                'user_id' => getUserId()
            ]);
            $this->showToastr('error', trans('action.error'), trans('action.topic_delete_failed'));
        }
    }

    #[On('toggleFrequencyGenerator')]
    public function toggleFrequencyGenerator()
    {
        // Reset all views first
        $this->showYoutubePlayer = false;
        $this->showFrequencyGenerator = !$this->showFrequencyGenerator;

        // Update the title based on current view
        if ($this->showFrequencyGenerator) {
            $this->currentTitle = __('action.frequency_generator');
        } else {
            $this->currentTitle = $this->widget['title'] ?? trans('action.own_topic');
        }

        $this->js("
            window.dispatchEvent(new CustomEvent('ownTopicToggled', {
                detail: {
                    widgetId: '{$this->widget['id']}',
                    showFrequencyGenerator: " . ($this->showFrequencyGenerator ? 'true' : 'false') . ",
                    currentTitle: '" . addslashes($this->currentTitle) . "'
                }
            }));
        ");
    }

    #[On('toggleYoutubePlayer')]
    public function toggleYoutubePlayer()
    {
        // Reset all views first
        $this->showFrequencyGenerator = false;
        $this->showYoutubePlayer = !$this->showYoutubePlayer;

        // Update the title based on current view
        if ($this->showYoutubePlayer) {
            $this->currentTitle = __('action.video_player');
        } else {
            $this->currentTitle = $this->widget['title'] ?? trans('action.own_topic');
        }

        $this->js("
            window.dispatchEvent(new CustomEvent('youtubePlayerToggled', {
                detail: {
                    widgetId: '{$this->widget['id']}',
                    showYoutubePlayer: " . ($this->showYoutubePlayer ? 'true' : 'false') . ",
                    currentTitle: '" . addslashes($this->currentTitle) . "'
                }
            }));
        ");
    }

    private function clearAnalysisCache(){
        $cacheService = new \App\Services\AnalysisCacheService(getUserId());
        $cacheService->clearAll();
    }

    public function render()
    {
        return view('livewire.dashboard.widgets.navigation.own-topic');
    }
}
