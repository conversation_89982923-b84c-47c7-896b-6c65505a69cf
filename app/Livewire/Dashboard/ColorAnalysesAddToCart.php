<?php

namespace App\Livewire\Dashboard;


use Livewire\Component;
use Livewire\Attributes\On;
use App\Services\Users\UserService;
use App\Traits\LivewireGeneralFunctions;
use App\Services\Dashboard\ColorAnalysesAddToCartService;


class ColorAnalysesAddToCart extends Component
{
    use LivewireGeneralFunctions;

    public $widgetId;
    public $poolId;
    public $title;
    public $widget;

    private $colorAnalysesAddToCartService;

    public function __construct()
    {
        $this->colorAnalysesAddToCartService = new ColorAnalysesAddToCartService();
    }

    public function mount($poolId, $title = '', $widget = [])
    {
        $this->poolId = $poolId;
        $this->title = $title;
        $this->widget = $widget;
        $this->widgetId = $widget['id']; // Set widgetId from widget array or generate unique ID
    }

    #[On('updateWidgetPoolType.{widgetId}')]
    public function handleSwitchPoolType($poolId)
    {
        $this->poolId = (int)$poolId;
    }

    public function addToCart($color)
    {
        if (!UserService::checkUserAccess()) {
            return $this->showToastr(
                'warning',
                trans('action.warning'),
                trans('action.payment_due_message', ['siteName' => env('APP_NAME')])
            );
        }

        $response = $this->colorAnalysesAddToCartService->addToCart($this->poolId, 0, $color);
        $analysisIds = collect($response)->pluck('analysis_id')->toArray();
        $this->showToastrMessage($response);

        foreach ($analysisIds as $analysisId) {
            $this->dispatch("singleCartItemAdded.{$analysisId}")
                ->to(SingleAnalysesAddToCart::class);
        }

        $this->dispatch('cartUpdated')->to(CartModal::class);
    }

    private function showToastrMessage($response)
    {
        $this->showToastr(
            $response ? 'success' : 'error',
            $response ? 'Success' : 'Error',
            $response ?  trans('action.cart_save') : trans('action.cart_not_save')
        );
        $this->showCart();
        $this->dispatch('cartUpdated')->to(CartModal::class);
    }

    public function render()
    {
        return view('livewire.dashboard.color-analyses-add-to-cart');
    }
}
