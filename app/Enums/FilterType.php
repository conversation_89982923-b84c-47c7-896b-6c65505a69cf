<?php

namespace App\Enums;

enum FilterType: int
{
    case ALPHABETICAL_ASC = 1;
    case ALPHABETICAL_DESC = 2;
    case NUMERICAL_ASC = 3;
    case NUMERICAL_DESC = 4;

    public function label(): string
    {
        return match($this) {
            self::ALPHABETICAL_ASC => 'A-Z',
            self::ALPHABETICAL_DESC => 'Z-A',
            self::NUMERICAL_ASC => '1-100',
            self::NUMERICAL_DESC => '100-1'
        };
    }
}