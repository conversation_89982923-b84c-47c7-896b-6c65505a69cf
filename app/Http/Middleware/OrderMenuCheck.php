<?php

namespace App\Http\Middleware;

use App\Enums\MenuTypeEnum;
use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redirect;

class OrderMenuCheck
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        session()->put('_menu_active' . getAuthID(), true);

        $menuData = __getMenus();

        // Ensure we always work with a Collection, regardless of cache state
        $getMenus = is_array($menuData) ? collect($menuData) : $menuData;

        // Additional safety check to ensure it's a Collection
        if (!($getMenus instanceof \Illuminate\Support\Collection)) {
            $getMenus = collect($getMenus ?? []);
        }

        if(isset($request->proid)){
            $menu = $getMenus->whereIn('status',[1,3])->filter(function ($item) use($request){
                if($item->id == $request->proid && isset($request->subid)){
                    $submenu = $item->submenus->filter(function($sub) use($request){
                        return ($request->subid == $sub->id) ? true:false;
                    })->values()->first();
                    if(!$submenu) {
                        return Redirect::to('/dashboard')->with([
                            'alert-type' =>  "error", 
                            'message' => "You dont have access on this submenu =>" . $request->subid
                        ]);
                    }
                    
                }
                return ($item->id == $request->proid) ? true : false;
            })->values()->first();
            if(!$menu) return Redirect::to('/dashboard')->with([
                        'alert-type' =>  "error", 
                        'message' => "You dont have access on this menu =>" . $request->proid
                    ]);

        }elseif(isset($request->ownid)){
            $menu = $getMenus->whereIn('status',[2,4])->filter(function ($item) use($request){
                if($item->id == $request->ownid && isset($request->ownsubid)){
                    $submenu = $item->submenus->filter(function($sub) use($request){
                        return ($request->ownsubid == $sub->id) ? true:false;
                    })->values()->first();
                    if(!$submenu) return Redirect::to('/dashboard')->with(['alert-type' =>  "error", 'message' => "You dont have access on this submenu =>" . $request->ownsubid]);
                }
                return ($item->id == $request->ownid) ? true : false;
            })->values()->first();
            if(!$menu) return Redirect::to('/dashboard')->with(['alert-type' =>  "error", 'message' => "You dont have access on this menu =>" . $request->ownid]);
        }
        if($request->has('menu_head')) $request->query->set('menu_head', $request->get('menu_head'));
        return $next($request);
    }
}
