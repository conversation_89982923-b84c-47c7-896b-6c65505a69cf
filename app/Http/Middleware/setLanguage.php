<?php

namespace App\Http\Middleware;

use App\Services\GoCalcService;
use Cart;
use Closure;
use App\Model\Language;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;

class setLanguage
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if(!Auth::check()){
            $userLangs = substr($request->server('HTTP_ACCEPT_LANGUAGE') ?? '', 0, 2);
            if(isset($request->lang)) $userLangs = $request->lang;
            if (Session::has('locale')) {
                setServerLocal(Session::get('locale'));
            } else {
                setServerLocal($userLangs);
                Session::put('locale',$userLangs);
            }
            return $next($request);
        }else{
            session()->put('_menu_active' . getAuthID(), false);
            #get login user data
            if (Session::has('lang_id'.Auth::id())) $lang = Session::get('lang_id'.Auth::id());
            else $lang = Session::put('lang_id'.Auth::id(), Language::find(Auth::user()->language_id)->short_code);

            if(Cache::has('emergency_update')){
                $this->checkUserRemoveCacheSession(Cache::get('emergency_update'));
            }
            #cart data
            // Cart::instance('wishlist')->restore(Session::get('id'));

            #language set
            $lang = $lang??session()->get('lang_id'.Auth::id());
            setServerLocal($lang);
            GoCalcService::getInstance(getAuthID(), $lang);
        }

        return $next($request);
    }

    private function checkUserRemoveCacheSession($emergencyIds){
        if(in_array(Auth::id(), $emergencyIds)){
            #remove all temp data like sessions,caches
            removeUserCacheKeys(Auth::id());
            #unset after user temp data remove
            unset($emergencyIds[array_search(Auth::id(), $emergencyIds)]);
            // Update the data without changing the expiration time
            Cache::put('emergency_update', $emergencyIds, null);
        }
    }
}
