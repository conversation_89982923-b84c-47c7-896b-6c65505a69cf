<?php

namespace App\Http\Controllers\Ajax;

use Exception;
use App\Model\Pdf;
use App\Model\Pool;
use App\Model\Cause;
use App\Model\DcPdf;
use App\Model\Group;
use App\Model\Audios;
use App\Model\Causes;
use App\Model\Analyse;
use App\Model\EnCause;
use App\Model\Submenu;
use App\Model\CronSetup;
use App\Model\EnAnalyses;
use App\Model\UserOption;
use App\Model\UserSubMenu;
use App\Model\EinflueImage;
use App\Model\GlobalSetting;
use Illuminate\Http\Request;
use App\Model\ProductSetting;
use App\Model\SaveCalculation;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use App\Http\Traits\Base64EncodingTrait;

class AajaxController extends Controller
{
    use Base64EncodingTrait;

    public function GetFocusImage()
    {
        $imgName = EinflueImage::find(request()->imgID);
        return response()->json(['success' => true, 'imagename' => $imgName]);
    }

    public function GetGroupCauses()
    {
        if (request()->groupid == 0) {

            $einid = Submenu::with('group_einflus', 'group_focus')->find(request()->subid);
            $pools = DB::table('focus_pools')->where('submenu_id', request()->subid)->pluck('pool_id')->toArray();

            $group = array();
            if (!empty($einid->group_einflus)){
                foreach ($einid->group_einflus as $val)
                    $group[] = "g_{$val->id}";
            }
            if (!empty($einid->group_focus)){
                foreach ($einid->group_focus as $val)
                    $group[] = "g_{$val->id}";
            }
            if (!empty($pools)){
                foreach ($pools as $val)
                    $group[] = "p_{$val}";
            }

            shuffle($group);
            $groupid = $group[rand(0, count($group) - 1)];
            $poolOrGroup = explode('_', $groupid);
            $groupid = $poolOrGroup[1];
        } else {
            $tempgroups = request()->groupid;
            $groupid = $tempgroups[rand(0, count($tempgroups) - 1)];
            $poolOrGroup = explode('_', $groupid);
            $groupid = $poolOrGroup[1];
        }

        try{
            $status = true;
            if($poolOrGroup[0] == 'g'){
                $group_column_name = (Lang::locale() == '' || Lang::locale() == 'de') ? 'group_name' : Lang::locale() . "_group_name as group_name";
                $groupsids = Group::with("causes")->select('groups.id', 'groups.group_type_id', 'groups.' . $group_column_name)->where('id', $groupid)->first();

                $groupname = $groupsids->group_name;
                $groups = $groupsids->causes->random(9);
                $group_type = $groupsids->grouptype->type_name;
            }elseif($poolOrGroup[0] == 'p'){
                $pool_column_name = (Lang::locale() == '' || Lang::locale() == 'de') ? 'pool_name' : Lang::locale() . "_pool_name as pool_name";
                $groupsids = Pool::with("analyses")->where('id', $groupid)->first(['id', $pool_column_name]);
                $groupname = $groupsids->pool_name;
                $groups = $groupsids->analyses->random(9);
                $group_type = "Analyse";
            }

        }catch(Exception $e){
            $status = false;
        }

        for ($i = 0; $i < 9; $i++) {
            $groupColor = array("#FF0000", "#F8B133", "#2FAB66");
            $randomColor = $groupColor[rand(0, count($groupColor) - 1)];
            $color[] = $randomColor;
        }

        return response()->json(['success' => $status, 'selectedGroup' => $groupid, 'groups' => $groups, 'colors' => $color, 'groupname' => $groupname, 'group_type' => $group_type]);
    }

    public function GetTopGroupCauses()
    {
        $poolIds  = array();
        $groupIds = array();

        if (request()->groupid == 0) {

            $einid = Submenu::with(['group_einflus', 'group_focus'])->find(request()->subid);
            $poolIds = DB::table('focus_pools')->where('submenu_id', request()->subid)->pluck('pool_id')->toArray();
            $groupid = array();
            $groupIds = ($einid != null) ? array_merge($einid->group_einflus->pluck('id')->toArray(), $einid->group_focus->pluck('id')->toArray()) : [];

        } else {
            $groupid = request()->groupid;
            foreach($groupid as $grp){
                $poolOrGroup = explode('_', $grp);
                if($poolOrGroup[0] == 'p') $poolIds[] = $poolOrGroup[1];
                else $groupIds[] = $poolOrGroup[1];
            }
        }

        $groupCauses = array();
        $causesids = array();

        if(!empty($poolIds)){
            $poolsId = Pool::with("analyses")->whereIn('id', $poolIds)->get(['id']);
            foreach ($poolsId as $pols) {
                foreach ($pols->analyses->pluck('id')->toArray() as $ana){
                    $groupCauses[] = "p_{$ana}";
                }
            }
        }
        if(!empty($groupIds)){
            $group_column_name = (Lang::locale() == '' || Lang::locale() == 'de') ? 'group_name': Lang::locale() . "_group_name as group_name";
            $groupsids = Group::with("causes")->select('groups.id', 'groups.group_type_id', 'groups.' . $group_column_name)->whereIn('id', $groupIds)->get(['id']);

            foreach ($groupsids as $causes){
                foreach ($causes->causes->pluck('id')->toArray() as $cau){
                    $groupCauses[] = "g_{$cau}";
                }
            }
        }

        shuffle($groupCauses);

        try {

            for ($i = 1; $i <= 9; $i++) {
                $index = rand(0, count($groupCauses) - 1);
                $causesids[] = $groupCauses[$index];
                unset($groupCauses[$index]);
                $groupCauses = array_values($groupCauses);
            }

            $anaIds = array();
            $cauIds = array();

            foreach($causesids as $cid){
                $poolOrGroup = explode('_', $cid);
                if($poolOrGroup[0] == 'p') $anaIds[] = $poolOrGroup[1];
                else $cauIds[] = $poolOrGroup[1];
            }

            $status = true;

            if(!empty($anaIds)){
                $analyse = Analyse::with(['pools' => function($pool) {
                    if (Lang::locale() != 'de') {
                        $pool->select(['pools.id', 'pools.'.Lang::locale() . '_pool_name as pool_name']);
                    }else $pool->select(['pools.id', 'pools.pool_name']);
                }])->whereIn('id', $anaIds)->get();
  
                foreach($analyse as $ana){
                    $groups[] = $ana;
                }
            }

            if(!empty($cauIds)){
                $groupData = Causes::with('group')->whereIn('id', $cauIds)->get();

                foreach($groupData as $grp){
                    $groups[] = $grp;
                }
            }

            shuffle($groups); 

            foreach($groups as $grp){
                if($grp->name){
                    $group_type[] = "Analyse";
                }else{
                    $group_type[] = DB::table('group_types')->where('id', $grp->group->group_type_id)->first();
                }
            }
        }catch(Exception $e){
            $status = false;
        }

        for ($i = 0; $i < 9; $i++) {
            $groupColor = array("#FF0000", "#F8B133", "#2FAB66");
            $randomColor = $groupColor[rand(0, count($groupColor) - 1)];
            $color[] = $randomColor;
        }

        return response()->json(['success' => $status, 'selectedGroup' => $groupid, 'groups' => $groups, 'colors' => $color, 'group_type' => $group_type]);
    }

    public function saveCalculation()
    {
        $userid    = getUserId();
        $isChecked = request()->is_ck;
        $anaid = request()->anaid;
        $newSaveCal = new SaveCalculation;

        if($isChecked == "true"){
            $saveRanValue = randomValue(getUserId());
            $this->cacheRemoveIfChecked();
        }else{
            $ranValueId = SaveCalculation::whereHas('analyses', function ($query) use ($anaid) {
                $query->where('analyse_id', $anaid);
            })->where('user_id', getUserId())->first();
            if($ranValueId){
                $delRanValue = SaveCalculation::where('id', $ranValueId->id)->first();
                if($delRanValue->analyses()->get()) $delRanValue->analyses()->detach();
                // $delRanValue->pools()->detach();
                $delRanValue->submenus()->detach();
                $delRanValue->products()->detach();
                $delRanValue->delete();
            }
            $saveRanValue = array();
            $this->cacheRemoveIfChecked();
        }


        if(!array_key_exists($anaid, $saveRanValue) or $delRanValue)
        {

            if ($isChecked == 'true') {

                $ranVal = rand(1, 100);

                $newSaveCal->user_id   = $userid;
                $newSaveCal->ran_value = $ranVal;
                $newSaveCal->type      = "other";

                $newSaveCal->save();
                $newSaveCal->analyses()->attach(request()->anaid);
                // $newSaveCal->pools()->attach(request()->poolid);
                $newSaveCal->submenus()->attach(request()->subid);
                $newSaveCal->products()->attach(request()->proid);

            }
                $userid   = getUserId();

                if(request()->type == 'sub')
                    $productColors = productColors(request()->proid);
                else
                    $colorValue = GlobalSetting::where('id', 1)->first();

                $biorythDetail = biorythVisibleDetails();
                $biorythsystem = array('p' => $biorythDetail->gs_bioryth_p, 'm' => $biorythDetail->gs_bioryth_m);

                $user = getUserDetails();
                $strM = $user->first_name.$user->last_name.$user->gebdatum.$user->gebort.$user->thema_speichern;

                if(request()->type == 'sub')
                    $analyses = analysesByid(request()->poolid, request()->anaid);
                else
                {
                    $ownana = UserSubMenu::with('analyses')->where('id', request()->subid)->first();
                    $analyses = $ownana->analyses()->find(request()->anaid);
                }

                $anaid  = $analyses->id;
                $strP   = $analyses->name;
                $bioryth= $analyses->bioryth;

                if ($analyses->bioryth == 0)  $bioryth = 28;
                elseif ($analyses->bioryth < 0)  $bioryth = 28;
                else  $bioryth = $analyses->bioryth;

                #Analysis/Male/Heart/Oneday Calculation
                $beergS  = calculationPS($strM, $strP, $user, $bioryth,$biorythsystem);
                $beergK  = calculationPK($strM, $strP, $user, $bioryth,$biorythsystem);
                $beerg   = calculation($strM, $strP, $user, $bioryth, $biorythsystem);
                $beergod = calculationoneday($strM, $strP, $user, $bioryth);

                $ma      = round($beerg, 0);
                $beergK  = round($beergK, 0);
                $beergS  = round($beergS, 0);
                $beergod = round($beergod, 00);

                if ($isChecked == "true")
                    $ma = $ranVal;

                #Analysis Value Color
                if(request()->type == 'sub')
                {
                    if ($ma >= $productColors->red_min && $ma <= $productColors->red_max) $class_skill ="#E84E1B";
                    elseif ($ma >= $productColors->orange_min && $ma <= $productColors->orange_max) $class_skill ="#F8B133";
                    elseif($ma >= $productColors->green_min && $ma <= $productColors->green_max) $class_skill ="#2FAB66";
                    elseif($ma >= $productColors->custom_min && $ma <= $productColors->custom_max) $class_skill =$productColors->custom_color;
                }else{

                    if ($ma >= $colorValue->gs_red_min && $ma <= $colorValue->gs_red_max) $class_skill ="#E84E1B";
                    elseif ($ma >= $colorValue->gs_orange_min && $ma <= $colorValue->gs_orange_max) $class_skill ="#F8B133";
                    elseif($ma >= $colorValue->gs_green_min && $ma <= $colorValue->gs_green_max) $class_skill ="#2FAB66";
                }

                $record['ma'] = $ma;
                $record['color'] = $class_skill;

                return response()->json(['success' => true, 'record' => $record]);

        }else{

            if(request()->type == 'sub')
                $productColors = productColors(request()->proid);
            else
                $colorValue = GlobalSetting::where('id', 1)->first();


            if(!empty($saveRanValue))
                $ma = $saveRanValue[$anaid];
            else
                $ma = rand(1, 100);

            #Analysis Value Color
            if(request()->type == 'sub')
            {
                if ($ma >= $productColors->red_min && $ma <= $productColors->red_max) $class_skill ="#E84E1B";
                elseif ($ma >= $productColors->orange_min && $ma <= $productColors->orange_max) $class_skill ="#F8B133";
                elseif($ma >= $productColors->green_min && $ma <= $productColors->green_max) $class_skill ="#2FAB66";
                elseif($ma >= $productColors->custom_min && $ma <= $productColors->custom_max) $class_skill =$productColors->custom_color;
            }else{

                if ($ma >= $colorValue->gs_red_min && $ma <= $colorValue->gs_red_max) $class_skill ="#E84E1B";
                elseif ($ma >= $colorValue->gs_orange_min && $ma <= $colorValue->gs_orange_max) $class_skill ="#F8B133";
                elseif($ma >= $colorValue->gs_green_min && $ma <= $colorValue->gs_green_max) $class_skill ="#2FAB66";
            }

            $record['ma'] = $ma;
            $record['color'] = $class_skill;

            return response()->json(['success' => true, 'record' => $record]);
        }
    }


    public function audioEncoding($request) {
        $subidEx = explode('-', $request->subid);
        $subtype = ($subidEx[0] == "own") ? false : true;

        #check Analysis have Audio
        if (!empty($request->analysis_id)) {
            $audio = DB::table('analyses_audios')->where('analyse_id', $request->analysis_id)->first();
            if ($audio->audio != ''){
                if($subtype) 
                    // return asset('storage/analyse/aduio/' . $audio->audio);
                    return storage_path() . '/app/public/analyse/aduio/' . $audio->audio;
                else 
                    return storage_path() . '/app/public/analyse/aduio/' . $audio->audio;
            }
        }
        #check Submenu have Audio
        if($request->subid != '' && !empty($request->subid) && $subtype){
            $subID = $subidEx[1] ?? $request->subid;
            $submenu = Submenu::find($subID);
            if($submenu->randomAudio){
                $audio_file = Audios::find($submenu->randomAudio)->audio_name;
                if(!empty($audio_file))
                    // return asset('storage/analyse/aduio/' . $audio_file);
                    return storage_path() . '/app/public/analyse/aduio/' . $audio_file;
            }
        }
        #check Product have Audio
        if ($request->id != '' && !empty($request->id)) {
            $product_setting = ProductSetting::where('product_id', $request->id)->first();
            if (!empty($product_setting)) {
                $audio = $product_setting->productaudioes()->inRandomOrder()->first();
                if ($audio != null && $audio->audio_id != 0) {
                    $audio_file = Audios::find($audio->audio_id)->first()->audio_name;
                    // return 'product'.asset('storage/analyse/aduio/' . $audio_file);
                    return 'product'.storage_path() . '/app/storage/analyse/aduio/' . $audio_file;
                }
            }
        }
        #Random From Audio Lists
        $audio_file  = Audios::inRandomOrder()->first()->audio_name;
        // return asset('storage/analyse/aduio/' . $audio_file);
        return storage_path() . '/app/public/analyse/aduio/' . $audio_file;
    }

    public function getAudio(Request $request)
    {
        $subidEx = explode('-', $request->subid);
        $subtype = ($subidEx[0] == "own") ? false : true;
        #check Analysis have Audio

        if (!empty($request->analysis_id)) {
            $audio = DB::table('analyses_audios')->where('analyse_id', $request->analysis_id)->first(['audio']);
            if ($audio !== null && $audio->audio != '') {
                // if($subtype) return asset('storage/analyse/aduio/' . $audio->audio);
                // else return asset('storage/analyse/audio/' . $audio->audio);
                if($subtype) return $audioUrl = url(Storage::disk('audio')->url($audio->audio));
                else return $audioUrl = url(Storage::disk('audio')->url($audio->audio));
            }
        }
        #check Submenu have Audio
        if($request->subid != '' && !empty($request->subid) && $subtype){
            $subID = $subidEx[1] ?? $request->subid;
            $submenu = Submenu::find($subID);
            if($submenu->randomAudio){
                $audio_file = Audios::find($submenu->randomAudio,['audio_name'])->audio_name;
                if(!empty($audio_file))
                    // return asset('storage/analyse/aduio/' . $audio_file);
                    return $audioUrl = url(Storage::disk('audio')->url($audio_file));
            }
        }
        #check Product have Audio
        if ($request->id != '' && !empty($request->id)) {
            $product_setting = ProductSetting::where('product_id', $request->id)->first();
            if (!empty($product_setting)) {
                $audio = $product_setting->productaudioes()->inRandomOrder()->first(['audio_id']);
                if ($audio != null && $audio->audio_id != 0) {
                    $audio_file = Audios::find($audio->audio_id)->first(['audio_name'])->audio_name;
                    // return Storage::disk('public')->url('analyse/aduio/' . $audio_file);
                    return $audioUrl = url(Storage::disk('audio')->url($audio_file));
                }
            }
        }
        #Random From Audio Lists
        $audio_file  = Audios::inRandomOrder()->first(['audio_name'])->audio_name;
        // return asset('storage/analyse/aduio/' . $audio_file);
        return $audioUrl = url(Storage::disk('audio')->url($audio_file));

        // $file = $this->audioEncoding($request);

        // $file = storage_path() . '/app/storage/analyse/aduio/' . 'memu-aagamu.mp3';

        // Calling trait for encoding the audio file
        // $result = $this->audioBase64Encoding($file);

        // return $result;
        
    }

    


    public function deletePDF(Request $request)
    {
        $id = $request->id;
        $pdfid = $request->pdfid;
        $dpdf = Pdf::findOrFail($id); #AL
        try{
            $dpdf->delete(); #AL
            // activityLogTrack(3, $dpdf->type.' type pdf named '.$dpdf->name, $dpdf->type.' Typ pdf benannt '.$dpdf->name, 'cart_pdf');#AL
            if ($dpdf) {
                if (\File::exists(public_path('gpdf/' . $pdfid))) {
                    \File::delete(public_path('gpdf/' . $pdfid));
                }
            }

            return response()->json(['success' => true, 'msg' => __('action.pdf_deleted')]);
        } catch (\Throwable $th) {
        }
        return response()->json(['success' => false, 'msg' => $th]);
    }

    public function deleteDCPDF($id)
    {
        $dcpdf = DcPdf::findOrFail($id);
        try{
            $dcpdf->delete();
            if ($dcpdf) {
                if (\File::exists(public_path('focus/sessionspdf/' . $dcpdf->pdfunique))) {
                    \File::delete(public_path('focus/sessionspdf/' . $dcpdf->pdfunique));
                }
            }
            return response()->json(['success' => true, 'msg' => __('action.pdf_deleted')]);
        } catch (\Throwable $th) {}
        return response()->json(['success' => false, 'msg' => $th]);
    }

    public function resetMenu(Request $request)
    {
        $userid = $request->userid;
        $userop = UserOption::where('user_id', $userid)->first();
        //Remove cache for show actual data
        removeUserCacheKeys($userid);
        $userop->update([
            'shorting' => ""
        ]);
        // activityLogTrack(4, 'menu sorted by '.getAuthUserName(), 'Menü Sortierung für '.getAuthUserName(), 'menu_sorting');#AL
        return response()->json(['success' => true,'message'=>  __('action.reset_menu_success')]);
    }

    public function autoGenerate()
    {
        $product = __getMenus();
        $subid = collect();
        $product->whereIn('status',[1,2])->each(function($prod,$key) use($subid){
            if($prod->submenus) $prod->submenus->map(function($sub) use($subid){
                $subid->push($sub->id);
            });
        });
        
        // dd($subid);

        return response()->json(['success' => true, 'subid' => $subid]);
    }

    public function fetchdata(Request $request)
    {
        $user = DB::table('users')->where('id', $request->id)->first();
        $mail =  $user->cron_email ?? $user->email;

        return response()->json(['success' => true, 'email' => $mail]);
    }

    public function updateDirection(Request $request) {
        $status = $request->status;
        DB::table("user_options")->where("user_id", getAuthID())->update(['dc_direction_view' => $status]);

        return response()->json(['status' => true]);
    }


    public function checkCron(Request $request)
    {
        if($request->single == null || $request->duepow == null || $request->time2 == null || $request->time3 == null){
            return response()->json(['status' => false, 'message' => __('action.starttime_required')]);
        }

        $single = date("Y-m-d H:i:s", strtotime($request->single));
        $date   = date("Y-m-d", strtotime($request->single));
        $duepow = date("Y-m-d H:i:s", strtotime($request->duepow));
        $time2  = date("H:i:s", strtotime($request->time2));
        $time3  = date("H:i:s", strtotime($request->time3));
        $message = "";
        $status = false;
        $checkDuePowerStatus = $request->cdp;

        if($single != "1970-01-01 01:00:00" && $checkDuePowerStatus == "false"){
            $checkSetupTime = CronSetup::whereHas('cronsetuptimes', function ($query) use ($single) {
                $query->where('start_time', '=', $single)->where('status', 0);
            })->where('user_id', getAuthID())->where('selected_user', $request->userid)->where('is_deleted', 0)->first();

            if ($checkSetupTime){
                $message = __('action.first_starttime');
                $status = true;
                return response()->json(['status' => $status, 'message' => $message]);
            }
        }if($duepow != "1970-01-01 01:00:00" && $checkDuePowerStatus == "true"){
            $checkSetupTime = CronSetup::whereHas('cronsetuptimes', function ($query) use ($duepow) {
                $query->where('start_time', '=', $duepow)->where('status', 0);
            })->where('user_id', getAuthID())->where('selected_user', $request->userid)->where('is_deleted', 0)->first();

            if ($checkSetupTime){
                $message =  __('action.duepow_starttime');
                $status = true;
                return response()->json(['status' => $status, 'message' => $message]);
            }
        }

        if($time2 != "01:00:00" && $checkDuePowerStatus == "false"){

            $tmptime2 = "{$date} {$time2}";

            $checkSetupTime = CronSetup::whereHas('cronsetuptimes', function ($query) use ($tmptime2) {
                $query->where('start_time', '=', $tmptime2)->where('status', 0);
            })->where('user_id', getAuthID())->where('selected_user', $request->userid)->where('is_deleted', 0)->first();

            if ($single == $tmptime2){
                $message =  __('action.second_starttime');
                $status = true;
                return response()->json(['status' => $status, 'message' => $message]);
            }elseif($checkSetupTime){
                $message =  __('action.second_starttime');
                $status = true;
                return response()->json(['status' => $status, 'message' => $message]);
            }
        }

        if($time3 != "01:00:00" && $checkDuePowerStatus == "false"){

            $tmptime3 = "{$date} {$time3}";

            $checkSetupTime = CronSetup::whereHas('cronsetuptimes', function ($query) use ($tmptime3) {
                $query->where('start_time', '=', $tmptime3)->where('status', 0);
            })->where('user_id', getAuthID())->where('selected_user', $request->userid)->where('is_deleted', 0)->first();



            if ($single == $tmptime3 ){
                $message =  __('action.third_starttime');
                $status = true;
                return response()->json(['status' => $status, 'message' => $message]);
            }elseif ($tmptime2 == $tmptime3 ){
                $message =  __('action.third_starttime');
                $status = true;
                return response()->json(['status' => $status, 'message' => $message]);
            }elseif($checkSetupTime){
                $message =  __('action.third_starttime');
                $status = true;
                return response()->json(['status' => $status, 'message' => $message]);
            }
        }
        return response()->json(['status' => false, 'message' => $message]);
    }

    public function menuAutoHide(Request $request)
    {
        UserOption::where('user_id', $request->user_id)->update(['auto_menu_hide' => $request->checkbox_val]);
        Cache::forget('user_auto_menu_hide_' . getUserId());
        return response()->json(['success' => true,'message'=>  __('action.auto_menu_hide')]);
    }

    private function cacheRemoveIfChecked()
    {
        try {
            $user_id   = session()->get('id');
            $userDetails  = userFilterid($user_id);
            $calculation_with = ($userDetails->calculation_with) ?? '';
            $datumcore = $userDetails->datumcore ?? '';
            $thema_speichern = str_replace(' ', '', $userDetails->thema_speichern ?? '');
            $dynamic_cache_key = 'analysis_' . $user_id . '_' . request()->proid . '_' . request()->subid . '_' . $calculation_with . '_' . $datumcore . '_' . $thema_speichern;
            if (Cache::has($dynamic_cache_key)) Cache::forget($dynamic_cache_key);
        } catch (\Exception $e) {
            // Handle the error here
            info('An error occurred in cacheRemoveIfChecked(): ' . $e->getMessage());
        }
    }
}
