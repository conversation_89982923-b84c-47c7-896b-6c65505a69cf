# Laravel
/public/hot
/public/storage
/public/storage/*
public/.DS_Store

# STORAGE
storage/*
!storage/app
storage/logs/*
!storage/logs
storage/*.key

/vendor
.env
/nbproject
Homestead.json
Homestead.yaml
.phpunit.result.cache
.env.backup
# Vagrant
/.vagrant

# Dependencies
/node_modules

# IDEs and editors
.idea
.vscode
.vs
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.suo
.ntvs
*.njsproj
*.sln
error_log

# Logs
!logs
*.log
*.tmp
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Misc
.npm
.eslintcache
.yarn-integrity

# System Files
.DS_Store
.AppleDouble
.LSOverride

# Thumbnails
._*
Thumbs.db
ehthumbs.db
ehthumbs_vista.db

# Dump file
*.stackdump

# Folder config file
[Dd]esktop.ini

# Windows shortcuts
*.lnk
/.history
*.zip


#other folders
/public/analysis_audio
/public/profile
/public/focus
/public/cronpdf
/public/analyse
/public/analysis_import
/public/gpdf
/public/bgimages
/public/pdfimages
/public/temp_cronpdf
/public/pdf
/public/users
/public/slides
/public/submenu_icon
/public/demofile
/public/cronmail/*.txt
/bootstrap/cache/*
/app/Services/Go/bin/*
