name: Import Latest Database Backup

on:
  workflow_dispatch:
    inputs:
      database_name:
        description: 'Target database name'
        required: true
        type: string
        default: 'test3'
      backup_date:
        description: 'Specific backup date (YYYY-MM-DD) - leave empty for latest'
        required: false
        type: string
        default: ''
      debug:
        description: 'Enable debug mode'
        required: false
        type: boolean
        default: false

jobs:
  import-database:
    runs-on: ubuntu-latest
    steps:
      - name: Install required packages
        run: apt-get update && apt-get install -y openssh-client awscli

      - name: Fix DNS resolution
        run: |
          # Add Google DNS servers to resolv.conf
          echo "nameserver *******" >> /etc/resolv.conf
          echo "nameserver *******" >> /etc/resolv.conf

      - name: Set up SSH
        run: |
          # Set up SSH
          mkdir -p ~/.ssh
          echo "${{ secrets.TEST_TARGET_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          
          # Configure SSH for direct connection
          cat > ~/.ssh/config << EOF
          Host target
            HostName ${{ secrets.TEST_TARGET_SERVER_ADDRESS }}
            User ${{ secrets.TEST_TARGET_SERVER_USER }}
            IdentityFile ~/.ssh/id_rsa
            StrictHostKeyChecking no
            ConnectTimeout 30
          EOF
          chmod 600 ~/.ssh/config
          
          # Test connection
          ssh ${{ inputs.debug && '-vvv' || '-q' }} target "echo 'Connection successful'" || echo "SSH connection test failed but continuing"

      - name: Configure AWS CLI and find latest backup
        run: |
          # Configure AWS CLI for Hetzner S3
          aws configure set aws_access_key_id ${{ secrets.S3_ACCESS_KEY }}
          aws configure set aws_secret_access_key ${{ secrets.S3_SECRET_KEY }}
          aws configure set default.region eu-central-1
          aws configure set default.output json
          
          # Set S3 endpoint
          S3_ENDPOINT="https://nbg1.your-objectstorage.com"
          
          # Determine backup date/folder
          if [ -n "${{ inputs.backup_date }}" ]; then
            BACKUP_DATE="${{ inputs.backup_date }}"
            echo "Using specified backup date: $BACKUP_DATE"
          else
            echo "Finding latest backup..."
            # List all backup folders and get the latest one
            BACKUP_DATE=$(aws s3 ls s3://enfit-coolify/laravel11-db-backups/ --endpoint-url="$S3_ENDPOINT" | \
              grep "PRE 20" | \
              awk '{print $2}' | \
              sed 's/\///g' | \
              sort -r | \
              head -1)
            echo "Latest backup date found: $BACKUP_DATE"
          fi
          
          if [ -z "$BACKUP_DATE" ]; then
            echo "Error: No backup date found"
            exit 1
          fi
          
          # Find the SQL file in that date folder
          echo "Looking for SQL files in backup folder: $BACKUP_DATE"
          SQL_FILE=$(aws s3 ls s3://enfit-coolify/laravel11-db-backups/$BACKUP_DATE/ --endpoint-url="$S3_ENDPOINT" | \
            grep "\.sql\.gz$" | \
            awk '{print $4}' | \
            head -1)
          
          if [ -z "$SQL_FILE" ]; then
            echo "Error: No SQL file found in backup folder $BACKUP_DATE"
            exit 1
          fi
          
          echo "Found SQL file: $SQL_FILE"
          
          # Save variables for next step
          echo "BACKUP_DATE=$BACKUP_DATE" >> $GITHUB_ENV
          echo "SQL_FILE=$SQL_FILE" >> $GITHUB_ENV
          echo "S3_ENDPOINT=$S3_ENDPOINT" >> $GITHUB_ENV
          echo "S3_PATH=s3://enfit-coolify/laravel11-db-backups/$BACKUP_DATE/$SQL_FILE" >> $GITHUB_ENV

      - name: Download and import database
        run: |
          # Create download command
          DOWNLOAD_CMD="cd /tmp && "
          
          # Configure AWS CLI on target server
          DOWNLOAD_CMD="${DOWNLOAD_CMD}aws configure set aws_access_key_id '${{ secrets.S3_ACCESS_KEY }}' && "
          DOWNLOAD_CMD="${DOWNLOAD_CMD}aws configure set aws_secret_access_key '${{ secrets.S3_SECRET_KEY }}' && "
          DOWNLOAD_CMD="${DOWNLOAD_CMD}aws configure set default.region eu-central-1 && "
          
          # Download the backup file
          DOWNLOAD_CMD="${DOWNLOAD_CMD}echo 'Downloading backup file...' && "
          DOWNLOAD_CMD="${DOWNLOAD_CMD}aws s3 cp '${{ env.S3_PATH }}' dump.sql.gz --endpoint-url='${{ env.S3_ENDPOINT }}' && "
          
          # Verify download
          DOWNLOAD_CMD="${DOWNLOAD_CMD}echo 'Backup file downloaded successfully:' && "
          DOWNLOAD_CMD="${DOWNLOAD_CMD}ls -lh dump.sql.gz && "
          
          # Import the database
          DOWNLOAD_CMD="${DOWNLOAD_CMD}echo 'Starting database import...' && "
          DOWNLOAD_CMD="${DOWNLOAD_CMD}clpctl db:import --databaseName='${{ inputs.database_name }}' --file=dump.sql.gz && "
          
          # Cleanup
          DOWNLOAD_CMD="${DOWNLOAD_CMD}echo 'Cleaning up...' && "
          DOWNLOAD_CMD="${DOWNLOAD_CMD}rm -f dump.sql.gz && "
          DOWNLOAD_CMD="${DOWNLOAD_CMD}echo 'Database import completed successfully!'"
          
          echo "Running database import on target server..."
          echo "Backup: ${{ env.BACKUP_DATE }}/${{ env.SQL_FILE }}"
          echo "Target DB: ${{ inputs.database_name }}"
          
          # Execute the command on target server
          ssh ${{ inputs.debug && '-vvv' || '-q' }} target "$DOWNLOAD_CMD"

      - name: Cleanup
        if: always()
        run: |
          # Remove sensitive files
          rm -f ~/.ssh/id_rsa

  notify:
    needs: [ import-database ]
    if: always()
    uses: ./.gitea/workflows/notify-chat.yml
    with:
      status: ${{ needs.import-database.result }}
      branch: 'database-import'
      server: ${{ secrets.TEST_TARGET_SERVER_USER }}@${{ secrets.TEST_TARGET_SERVER_ADDRESS }}
      environment: 'test-database'
      repo_url: 'https://gitea.energetisch.dev/enfit/start'
      additional_info: 'Database: ${{ inputs.database_name }} | Backup imported to test server'
    secrets:
      ROCKETCHAT_AUTH_TOKEN: ${{ secrets.ROCKETCHAT_AUTH_TOKEN }}
      ROCKETCHAT_USER_ID: ${{ secrets.ROCKETCHAT_USER_ID }}
      ROCKETCHAT_SERVER_URL: ${{ secrets.ROCKETCHAT_SERVER_URL }}
      ROCKETCHAT_ROOM_ID: ${{ secrets.ROCKETCHAT_ROOM_ID }}