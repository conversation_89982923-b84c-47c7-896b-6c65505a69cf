<div>
    <canvas wire:loading.class="loading-animation" wire:ignore id="{{$uniqueId}}_widget_chart"></canvas>
</div>

@assets
<script src="{{ asset('/js/chart/chart.min.js') }}"></script>
<script src="{{ asset('/js/chart/chartjs-plugin-annotation.min.js') }}"></script>
@endassets

@script
<script>
    const id = '{{$uniqueId}}';
    const filter = @js($filter);
    const results = @json($results);
    const originalLabels = @json($labels);
    const MAX_LABEL_LENGTH = 10;
    // Truncate labels for display
    function truncateLabel(label, maxLength = MAX_LABEL_LENGTH) {
        return label.length > maxLength ? label.substring(0, maxLength - 3) + '...' : label;
    }

    function createPolarAreaChart(id, originalLabels, datasets, filters) {
        const labels = originalLabels.map(label => truncateLabel(label));
        const options = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        title: () => '', // Hide default title line
                        label: function(context) {
                            const index = context.dataIndex;
                            const fullLabel = originalLabels[index]; // Show full label in tooltip
                            const value = context.dataset.data[index];
                            return filters ? `${fullLabel}: ${value}` : `${fullLabel}`;
                        }
                    }
                },
                zoom: {
                    pan: { enabled: true, mode: 'xy' },
                    zoom: { enabled: true, mode: 'xy' },
                },
            },
            scales: {
                r: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 10,
                    },
                    pointLabels: {
                        display: true,
                        centerPointLabels: true,
                        font: { size: 16 },
                        callback: function(value) {
                            return truncateLabel(value);
                        }
                    }
                }
            }
        };

        const config = {
            type: 'polarArea',
            data: {
                labels: labels,
                datasets: getFormattedDatasets(datasets, filters)
            },
            options: options
        };

        const canvasId = `${id}_widget_chart`;
        const ctx = document.getElementById(canvasId).getContext('2d');

        if (window.chartInstances && window.chartInstances[canvasId]) {
            window.chartInstances[canvasId].destroy();
        }
        window.chartInstances = { ...(window.chartInstances || {}), [canvasId]: new Chart(ctx, config) };
    }

    function getFormattedDatasets(datasets, filters) {
        if (!filters) {
            const chartColors = getChartColors(datasets);
            return [{
                data: datasets,
                backgroundColor: chartColors.backgroundColor,
                borderColor: 'white',
                borderWidth: 2
            }];
        }

        return datasets.map((data, index) => ({
            data: data,
            backgroundColor: index === 0 ? 'rgba(255, 99, 132, 0.2)' : 'rgba(75, 192, 192, 0.2)',
            borderColor: index === 0 ? 'rgba(255, 99, 132, 1)' : 'rgba(47, 171, 102, 1)',
            borderWidth: 1
        }));
    }

    function getChartColors(dataSet) {
        const getColor = value => {
            if (value <= 10) return ['rgba(232, 78, 27, 1)', 'rgb(232, 78, 27)'];
            if (value <= 69) return ['rgba(248, 177, 51, 1)', 'rgb(248, 177, 51)'];
            if (value <= 100) return ['rgba(47, 171, 102, 1)', 'rgb(47, 171, 102)'];
            return ['rgba(0, 0, 0, 0.2)', 'rgb(0, 0, 0)'];
        };

        return dataSet.reduce((colors, value) => {
            const [bgColor, borderColor] = getColor(value);
            colors.backgroundColor.push(bgColor);
            colors.borderColor.push(borderColor);
            return colors;
        }, { backgroundColor: [], borderColor: [] });
    }

    createPolarAreaChart(id, originalLabels, results, filter);

    // Handle window resize to maintain chart responsiveness
    const optimizedResize = debounce(() => {
        createPolarAreaChart(id, originalLabels, results, filter);
    }, 200);

    window.addEventListener('resize', optimizedResize);

    // add event listener to 'resultUpdated-'$uniqueId
    Livewire.on('graphResultUpdated', (event) => {
        // Only update if this event is for this specific widget
        if (event.uniqueId === '{{$uniqueId}}' || event.widgetId === '{{$widgetId}}') {
            createPolarAreaChart('{{$uniqueId}}', event.data.labels, event.data.results, event.data.filter);
        }
    });

    function debounce(func, wait) {
        let timeout;
        return function (...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }
</script>
@endscript