<div class="switch-pools" wire:key="switch-pools-{{ $widgetId }}">
    <div class="custom-select-wrapper {{ $isExpanded ? 'expanded' : '' }}">
        <button type="button" wire:click="loadPools()" class="select-icon" id="select-icon-{{ $widgetId }}">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="6,9 12,15 18,9"></polyline>
            </svg>
        </button>
        <select class="form-select" wire:model.live="poolType" wire:change="switchPool($event.target.value,'{{ $widgetId }}')" id="select-{{ $widgetId }}"
            @if($poolsLoaded)
                @foreach($pools as $pool)
                    <option value="{{ $pool['id'] }}" @if($pool['id'] == $poolType) selected @endif>
                        {{ $pool['name'] }}
                    </option>
                @endforeach
            @else
                <option value="">Click to load pools</option>
            @endif
        </select>
    </div>
    <style>
        .switch-pools {
            display: inline-block;
            position: relative;
            line-height: 1;
        }

        .custom-select-wrapper {
            position: relative;
            display: inline-block;
            width: 24px;
            overflow: hidden;
            transition: width 0.3s ease;
        }

        .custom-select-wrapper.expanded {
            width: 150px;
        }

        .select-icon {
            position: absolute;
            right: 0;
            top: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border: 1px solid #e2e8f0;
            border-radius: 0.2rem;
            background-color: #fff;
            cursor: pointer;
        }

        .select-icon svg {
            color: #6b7280;
            width: 12px;
            height: 12px;
        }

        .form-select {
            width: 150px;
            height: 24px;
            padding: 0.15rem 1.5rem 0.15rem 0.5rem;
            font-size: 0.813rem;
            border-radius: 0.25rem;
            border: 1px solid #e2e8f0;
            background-color: #fff;
            cursor: pointer;
            transition: border-color 0.15s ease-in-out;
            appearance: none;
            -webkit-appearance: none;
        }

        .form-select:hover,
        .form-select:focus {
            border-color: #02a065;
            outline: none;
        }

        @media (max-width: 768px) {
            .custom-select-wrapper.expanded {
                width: 120px;
            }

            .form-select {
                width: 120px;
                padding: 0.15rem 1.5rem 0.15rem 0.5rem;
            }

            select.form-select {
                direction: rtl;
                padding-right: 25px;
            }

            select.form-select option {
                direction: ltr;
            }
        }
    </style>
</div>



