<div>
    <div class="d-flex align-items-center gap-2" style="gap: 5px;">
        <div class="d-flex align-items-center">
            <select class="custom-select border-none mt-2 mt-sm-0" 
                    id="filter-db-v3" 
                    wire:model="userFilterType"
                    wire:change="updateFilter($event.target.value)" 
                    style="width:85px;" 
                    @if($selectedLongDay) disabled @endif>
                <option @selected($userFilterType == App\Enums\FilterType::ALPHABETICAL_ASC->value) value="1">A-Z</option>
                <option @selected($userFilterType == App\Enums\FilterType::ALPHABETICAL_DESC->value) value="2">Z-A</option>
                <option @selected($userFilterType == App\Enums\FilterType::NUMERICAL_ASC->value) value="3">1-100</option>
                <option @selected($userFilterType == App\Enums\FilterType::NUMERICAL_DESC->value) value="4">100-1</option>
            </select>
        </div>
        <!-- Long Day Dropdown -->
        <select class="custom-select border-none mt-2 mt-sm-0 tabs-itemBox-Style" 
                id="longday-livewire"
                wire:model="selectedLongDay"
                wire:change="updateLongDay($event.target.value)"
                style="max-width: 160px;">
            <option value="">{{ __('action.lta_choose_days') }}</option>
            @foreach($this->ltaDays as $days)
            <option value="{{ $days->days }}" @selected($selectedLongDay == $days->days)>
                {{ $days->days }} {{ __('action.lta_days') }}
            </option>
            @endforeach
        </select>
        <!-- Month / Year Toggle -->
        <div class="filter-month-year ml-1">
            <span class="filter-btn {{ $filterMonth ? 'active' : '' }}" wire:click="toggleFilter('month')"
                title="{{ __('action.month') }}" data-toggle="tooltip" data-placement="top">
                {{ __('action.lta_mm') }}
            </span>
            <span class="filter-btn {{ $filterYear ? 'active' : '' }}" wire:click="toggleFilter('year')"
                title="{{ __('action.year') }}" data-toggle="tooltip" data-placement="top">
                {{ __('action.lta_yy') }}
            </span>
        </div>
        <!-- Date Picker Icon -->
        <i class="fas fa-calendar-alt f-22 @if($originalDate != date('Y-m-d')) text-success @endif" id="open-changeDate" data-toggle="tooltip" data-placement="top"
            title="{{ \Carbon\Carbon::parse($originalDate)->format('d.m.Y') }}" wire:click="openDateModal" style="cursor:pointer;">
        </i>
        <small class="ml-1">{{ \Carbon\Carbon::parse($originalDate)->format('d.m.Y') }}</small>
    </div>
    <!-- Date Modal -->
    @if($showDateModal)
    <div class="modal fade show d-block" tabindex="-1" role="dialog" style="background:rgba(0,0,0,0.5)">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('action.date_system_dashboard') }}</h5>
                    <button type="button" class="close" wire:click="closeDateModal">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <input type="date" wire:model="dateInput" class="form-control">
                </div>
                <div class="modal-footer modalFooter_padding">
                    <button type="button" class="btn btn-warning" wire:click="resetDate">{{ __('action.reset')
                        }}</button>
                    <button type="button" class="btn btn-secondary" wire:click="closeDateModal">{{ __('action.close')
                        }}</button>
                    <button type="button" class="btn btn-success" wire:click="saveDate">{{ __('action.save') }}</button>
                </div>
            </div>
        </div>
    </div>
    @endif

    @once
    <style>
        @keyframes pulseOpacity {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.4;
            }
        }

        .opacity-pulse {
            animation: pulseOpacity 1.5s ease-in-out infinite;
            pointer-events: none;
            cursor: not-allowed;
        }

        /* Updated Select Styling */
        .custom-select {
            border: 1px solid #02a065 !important;
            color: #02a065 !important;
            height: 24px;
            line-height: 16px;
            padding: 0 12px;
            border-radius: 4px;
            background: white;
            font-size: 14px;
            outline: none;
            cursor: pointer;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2302a065' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px 12px;
            padding-right: 35px;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
        }

        .custom-select:focus {
            border-color: #02a065;
            box-shadow: 0 0 0 2px rgba(2, 160, 101, 0.2);
        }

        .custom-select option {
            color: #333;
        }

        /* Radio Button Style M/Y Toggle Buttons with Gaps */
        .filter-month-year {
            display: flex;
            gap: 3px;
            align-items: center;
        }

        .filter-btn {
            width: 20px;
            height: 20px;
            border-radius: 50% !important;
            background: #333 !important;
            color: white !important;
            border: 2px solid #333 !important;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            box-sizing: border-box;
        }

        .filter-btn.active {
            background: #02a065 !important;
            border-color: #02a065 !important;
            color: white !important;
        }

        .filter-btn:hover {
            opacity: 0.8;
        }

        /* Calendar Icon Styling */
        .f-22 {
            font-size: 18px;
        }

        #open-changeDate {
            color: #6c757d;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        #open-changeDate:hover {
            color: #02a065;
            background: rgba(2, 160, 101, 0.1);
        }

        #open-changeDate.text-success {
            color: #02a065 !important;
        }

        /* Modal Enhancements */
        .modal-content {
            border-radius: 8px;
            border: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .modal-title {
            font-weight: 600;
            color: #333;
        }

        .close {
            color: #6c757d;
            opacity: 1;
        }

        .close:hover {
            color: #dc3545;
        }

        .btn-success {
            background-color: #02a065;
            border-color: #02a065;
        }

        .btn-success:hover {
            background-color: #028a56;
            border-color: #028a56;
        }
    </style>

    <script>
        document.addEventListener('livewire:load', () => {
            if (window.$ && $.fn.tooltip) $('[data-toggle="tooltip"]').tooltip();
        });
        document.addEventListener('livewire:update', () => {
            if (window.$ && $.fn.tooltip) $('[data-toggle="tooltip"]').tooltip();
        });
    </script>
    @endonce
</div>