{{-- resources/views/livewire/dashboard/widgets/navigation/frequency-generator.blade.php --}}
<div>
    <style>
        @keyframes glow {
            0% {
                box-shadow: 0 0 5px rgba(2, 160, 101, 0.5), 0 0 10px rgba(2, 160, 101, 0.3);
            }

            50% {
                box-shadow: 0 0 20px rgba(2, 160, 101, 0.8), 0 0 30px rgba(2, 160, 101, 0.5);
            }

            100% {
                box-shadow: 0 0 5px rgba(2, 160, 101, 0.5), 0 0 10px rgba(2, 160, 101, 0.3);
            }
        }

        @keyframes glowText {
            0% {
                text-shadow: 0 0 5px rgba(2, 160, 101, 0.5);
                opacity: 0.7;
            }

            50% {
                text-shadow: 0 0 20px rgba(2, 160, 101, 0.8);
                opacity: 1;
            }

            100% {
                text-shadow: 0 0 5px rgba(2, 160, 101, 0.5);
                opacity: 0.7;
            }
        }

        @keyframes glowButton {
            0% {
                box-shadow: 0 0 5px rgba(255, 193, 7, 0.5), inset 0 0 5px rgba(255, 193, 7, 0.2);
            }

            50% {
                box-shadow: 0 0 20px rgba(255, 193, 7, 0.8), inset 0 0 10px rgba(255, 193, 7, 0.3);
            }

            100% {
                box-shadow: 0 0 5px rgba(255, 193, 7, 0.5), inset 0 0 5px rgba(255, 193, 7, 0.2);
            }
        }

        @keyframes glowPrimary {
            0% {
                box-shadow: 0 0 5px rgba(13, 110, 253, 0.5), inset 0 0 5px rgba(13, 110, 253, 0.2);
            }

            50% {
                box-shadow: 0 0 20px rgba(13, 110, 253, 0.8), inset 0 0 10px rgba(13, 110, 253, 0.3);
            }

            100% {
                box-shadow: 0 0 5px rgba(13, 110, 253, 0.5), inset 0 0 5px rgba(13, 110, 253, 0.2);
            }
        }

        .glowing-input {
            animation: glow 1.5s ease-in-out infinite;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .glowing-text {
            animation: glowText 1.5s ease-in-out infinite;
        }

        .glowing-button {
            animation: glowButton 1.5s ease-in-out infinite;
        }

        .btn-primary.glowing-button {
            animation: glowPrimary 1.5s ease-in-out infinite;
        }

        .position-relative {
            position: relative;
        }

        .position-absolute {
            z-index: 10;
        }

        .right-inputs {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            height: 100%;
        }

        .right-inputs input {
            flex: 1;
        }

        .main-class {
            display: flex;
            gap: 8px !important
        }

        .topicText-input {
            height: 100% !important;
        }

        .parent-group {
            height: 100%;
        }

        .position-group {
            height: 100%;
        }

        .frequency-cart-btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s;
            align-items: center;
            gap: 0.5rem;
            height: 35px;
            width: 50px;
        }

        .frequency-cart-btn:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 1024px) and (min-width: 992px) {
            .main-class {
                flex-wrap: wrap;
                /* allow wrapping if needed */
            }

            .col-6,
            .col-5 {
                flex: 0 0 48%;
                /* shrink columns slightly */
                max-width: 48%;
            }

            .frequency-btns-addToCart {
                margin-top: 8px;
            }
        }
    </style>
    <link href="{{ asset('css/dashboard/right-panel.css') }}" rel="stylesheet">
    <div class="tab-navigation font-awesome mb-4">
        <div class="frequencyGeneratorTab fade show"
            id="nav-widget-frequencyGeneratorTab-{{ $widget['id'] ?? 'default' }}"
            data-livewire-component="{{ $this->getId() }}">
            <form wire:submit.prevent="addToCart">
                <div class="row g-2 align-items-stretch main-class justify-content-center">
                    <!-- Left Column -->
                    <div class="col-6 p-0 m-0">
                        <div class="form-group topic parent-group">
                            <div class="position-relative position-group">
                                <textarea rows="2" class="form-control topicText-input"
                                    wire:model.live.debounce.500ms="topicText" wire:loading.class="glowing-input"
                                    wire:target="topicText"
                                    placeholder="{{ trans('action.topic_name_placeholder') }}"></textarea>
                                <div wire:loading wire:target="topicText" class="position-absolute top-0 end-0 p-2">
                                    <small class="text-muted glowing-text">
                                        <i class="fas fa-calculator"></i>
                                    </small>
                                </div>
                            </div>
                            @error('topicText')
                            <small class="text-danger">{{ $message }}</small>
                            @enderror
                        </div>
                    </div>
                    <!-- Right Column -->
                    <div class="col-5 p-0 m-0">
                        <div class="right-inputs">
                            <div class="frequency-input-group">
                                <div class="input-group">
                                    <input type="number" class="form-control" style="border: 0 !important;" step="1"
                                        min="0" placeholder="{{ trans('action.frequency_placeholder') }}"
                                        wire:model.live="frequencyHz" wire:loading.class="glowing-input"
                                        wire:target="topicText" wire:loading.attr="readonly">
                                    <span class="input-group-text" wire:loading.class="glowing-text"
                                        wire:target="topicText">
                                        {{ trans('action.unit_hertz') }}
                                    </span>
                                </div>
                                @error('frequencyHz')
                                <small class="form-text text-danger">{{ $message }}</small>
                                @enderror
                            </div>
                            <div class="time-input-group">
                                <div class="input-group">
                                    <input type="number" class="form-control" style="border: 0 !important;" min="5"
                                        max="3600" step="1" placeholder="{{ trans('action.seconds_placeholder') }}"
                                        wire:model.defer="frequencyTime" wire:loading.class="glowing-input"
                                        wire:target="topicText" wire:loading.attr="readonly" wire:target="topicText">
                                    <span class="input-group-text" wire:loading.class="glowing-text"
                                        wire:target="topicText">{{
                                        trans('action.unit_seconds') }}</span>
                                </div>
                                @error('frequencyTime')
                                <small class="form-text text-danger">{{ $message }}</small>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-center frequency-btns-addToCart mt-1">
                    <button class="frequency-cart-btn btn-primary" wire:click="addToCart" wire:loading.attr="disabled"
                        wire:loading.class="glowing-button" wire:target="addToCart"
                        aria-label="{{ trans('action.cart') }}" title="{{ trans('action.cart') }}" data-toggle="tooltip"
                        data-placement="top">
                        <i class="ion ion-md-cart topic-icon"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>