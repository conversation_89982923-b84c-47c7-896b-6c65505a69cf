<div>
    <style>
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .grid-stack-item-content,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .widget-body,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .custom-widget-container,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .custom-widget-body {
            overflow: visible !important;
        }
        .topic-button-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }
        .own-topic-textarea textarea {
         resize: none;
        }
        .topic-btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }       

        .topic-btn:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .topic-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn-outline {
            background-color: transparent;
            border: 1px solid;
        }

        .btn-outline.btn-top {
            border-color: #bfdbfe;
            color: #26B4FF;
        }

        .btn-outline.btn-top:hover {
            background-color: #eff6ff;
        }

        .btn-outline.btn-emerald {
            border-color: #a7f3d0;
            color: #059669;
        }

        .btn-outline.btn-emerald:hover {
            background-color: #ecfdf5;
        }

        .btn-outline.btn-red {
            border-color: #fecaca;
            color: #dc2626;
        }

        .btn-outline.btn-red:hover {
            background-color: #fef2f2;
        }

        .topic-icon {
            width: 1rem;
            height: 1rem;
            stroke: currentColor;
            fill: none;
            stroke-width: 2;
        }

        .icon-sm {
            width: 0.75rem;
            height: 0.75rem;
        }

        .icon-fill {
            fill: currentColor;
            stroke: none;
        }

        .topic-btns {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        /* Default: show text, hide icon */
        .topic-btns .btn-icon {
            display: none;
        }

        .topic-btns .btn-text {
            display: inline-block;
        }

        .youtube-player-container {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
            background: #000;
        }
        .youtube-player-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
        }
        .btn-youtube {
            border-color: #ff0000;
            color: #ff0000;
        }

        /* Step 1: smaller screens — show icon, hide text */
        @media (max-width: 1625px) {
            .topic-btns .btn-icon {
                display: inline-block;
                margin-right: 0;
            }
            .topic-btns .btn-text {
                display: none;
            }
        }

        /* Step 2: very small — compact buttons */
        @media (max-width: 1320px) {
            .topic-btns {
                gap: 4px;
            }
            .topic-btns .topic-btn {
                padding: 6px 8px;
            }
        }
        @media (max-width: 992px) and (min-width: 575.92px) {
            .own-topic-textarea textarea {
                height: 75px;
            }
        }
    </style>
    <link href="{{ asset('css/dashboard/right-panel.css') }}" rel="stylesheet">
    {{-- Own Topic View --}}
    <div class="topicTab fade show own-topic-text-div"
         id="own-topic-widget-tab-{{ $widget['id'] }}"
         style="background: #fff;background-clip: padding-box; {{ $showFrequencyGenerator || $showYoutubePlayer ? 'display: none;' : '' }}">
        <div class="arrow"></div>
        <form wire:submit.prevent="">
            <div class="form-group own-topic-textarea">
                <textarea wire:model.blur="topicText"
                    rows="2"
                    class="form-control"
                    placeholder="{{trans('action.topic_name_placeholder')}}">
                </textarea>
            </div>
            <div class="topic-button-container topic-btns">
                @if(!$farbklang)
                <button class="topic-btn btn-primary"
                   wire:click="addToCart" @if(empty($topicText)) disabled @endif
                   aria-label="{{ trans('action.cart') }}"
                   title="{{ trans('action.cart') }}"
                   data-toggle="tooltip"
                   data-placement="top"
                >
                   <i class="ion ion-md-cart topic-icon"></i>
                </button>
                @endif
                <button 
                    class="topic-btn btn-outline btn-emerald"
                    wire:click="saveTopic"
                    aria-label="{{ trans('action.save') }}"
                    title="{{ trans('action.save') }}"
                    data-toggle="tooltip"
                    data-placement="top"
                >
                  
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
                        <polyline points="17,21 17,13 7,13 7,21"/>
                        <polyline points="7,3 7,8 15,8"/>
                    </svg>
                </button>
            
                <button class="topic-btn btn-outline btn-red"
                  wire:click="deleteTopic"
                  aria-label="{{ trans('action.delete') }}"
                  title="{{ trans('action.delete') }}"
                  data-toggle="tooltip"
                  data-placement="top"
                >
                    <svg class="topic-icon" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                </button>
            </div>
        </form>
    </div>

    {{-- Frequency Generator View --}}
    <div class="frequency-generator-container"
         id="nav-widget-frequencyGeneratorTab-{{ $widget['id'] }}"
         style="{{ $showFrequencyGenerator ? '' : 'display: none;' }}">
        <livewire:dashboard.widgets.navigation.frequency-generator
            :poolId="$poolId"
            :widget="$widget"
            :wire:key="'embedded-frequency-generator-' . $widget['id'] . '-pool-' . $poolId . '-' . rand()"
        />
    </div>

    {{-- YouTube Player View --}}
    <div class="youtube-video-player-container"
         id="nav-widget-youtubePlayerTab-{{ $widget['id'] }}"
         style="{{ $showYoutubePlayer ? '' : 'display: none;' }}">
        <livewire:dashboard.widgets.navigation.youtube-video-player
            :poolId="$poolId"
            :widget="$widget"
            :wire:key="'embedded-youtube-player-' . $widget['id'] . '-pool-' . $poolId . '-' . rand()"
        />
    </div>
</div>
