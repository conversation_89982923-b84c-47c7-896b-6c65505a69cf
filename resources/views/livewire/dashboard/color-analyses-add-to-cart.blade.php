<div wire:loading.class="opacity-50">
    <div class="pool-content__title">
        <h5 class="pool-content__title-text">{{ $title ?? ''}}</h5>
        <ul class="pool-content__icon-list">
            @if(isset($widget['settings']['youtube_link']) && !empty($widget['settings']['youtube_link']))
                <li class="pool-content__icon-item">
                    <i class="pool-content__icon pool-content__icon--youtube fas fa-play-circle f-18"
                       id="youtube-toggle-{{ $widget['id'] }}"
                       data-toggle="tooltip"
                       data-placement="bottom"
                       data-widget-id="{{ $widget['id'] }}"
                       data-youtube-link="{{ $widget['settings']['youtube_link'] }}"
                       onclick="toggleYouTubeVideo(this)"
                       data-original-title="{{__('action.video_player')}}"></i>
                </li>
            @endif
            <li class="pool-content__icon-item">
                <i class="pool-content__icon pool-content__icon--danger fas fa-plus-circle f-18"
                   data-toggle="tooltip"
                   data-placement="bottom"
                   data-state="danger"
                   wire:click="addToCart('RED')"
                   data-original-title="{{__('action.add_all_red_values_to_cart')}}"></i>
            </li>
            <li class="pool-content__icon-item">
                <i class="pool-content__icon pool-content__icon--warning fas fa-plus-circle f-18"
                   data-toggle="tooltip"
                   data-placement="bottom"
                   data-state="warning"
                   wire:click="addToCart('ORANGE')"
                   data-original-title="{{__('action.add_all_orange_values_to_cart')}}"></i>
            </li>
            <li class="pool-content__icon-item">
                <i class="pool-content__icon pool-content__icon--success fas fa-plus-circle f-18"
                   data-toggle="tooltip"
                   data-placement="bottom"
                   data-state="success"
                   wire:click="addToCart('GREEN')"
                   data-original-title="{{__('action.add_all_green_values_to_cart')}}"></i>
            </li>
        </ul>
    </div>
    @if(isset($widget['settings']['youtube_link']) && !empty($widget['settings']['youtube_link']))
        <!-- Video overlay container - initially hidden -->
        <div id="video-overlay-{{ $widget['id'] }}" class="youtube-video-overlay" style="display: none;">
            <div class="youtube-video-container">
                <iframe id="youtube-iframe-{{ $widget['id'] }}"
                        allowfullscreen
                        allow="autoplay; encrypted-media">
                </iframe>
            </div>
        </div>
    @endif
    <style>
        .pool-content__title {
            display: flex;
            justify-content: space-between;
            padding: 10px 20px;
            border-bottom: solid 1px #cdcdcd;
        }

        .pool-content__title-text {
            margin: 0;
            display: flex;
            align-items: center;
        }

        .pool-content__icon-list {
            list-style-type: none;
            padding: 0;
            display: flex;
            gap: 10px;
            margin-bottom: 0 !important;
        }

        .pool-content__icon-item {
            display: inline-block;
        }

        .pool-content__icon {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pool-content__icon--danger {
            color: red;
        }

        .pool-content__icon--warning {
            color: orange;
        }

        .pool-content__icon--success {
            color: green;
        }

        .pool-content__icon--youtube {
            color: #FF0000;
        }

        .pool-content__icon--youtube.active {
            color: #cc0000;
            text-shadow: 0 0 8px rgba(255, 0, 0, 0.5);
            filter: brightness(0.8);
        }

        .youtube-video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.95);
            z-index: 1000;
            padding: 20px;
            box-sizing: border-box;
            margin-top: 60px;
        }

        .youtube-video-container {
            position: relative;
            width: 100%;
            height: 100%;
            min-height: 200px;
            max-height: 400px;
            background: #000;
            border-radius: 4px;
            overflow: hidden;
        }

        .youtube-video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
        }

        /* Ensure the parent widget container has relative positioning */
        [id^="widget-content-"] {
            position: relative !important;
        }

        /* Hide original content when video is playing */
        .content-hidden {
            visibility: hidden;
        }
    </style>
</div>

@once
    <script>
        function toggleYouTubeVideo(button) {
            const widgetId = button.getAttribute('data-widget-id');
            const youtubeLink = button.getAttribute('data-youtube-link');
            const targetElement = document.getElementById('widget-content-' + widgetId);
            const videoOverlay = document.getElementById('video-overlay-' + widgetId);
            const iframe = document.getElementById('youtube-iframe-' + widgetId);
            const isActive = button.classList.contains('active');

            if (!targetElement || !videoOverlay || !iframe) {
                console.error('Required elements not found for widget:', widgetId);
                return;
            }

            if (isActive) {
                // Hide video and show original content
                button.classList.remove('active');
                button.classList.remove('fa-stop-circle');
                button.classList.add('fa-play-circle');

                // Hide video overlay
                videoOverlay.style.display = 'none';

                // Show original content
                const originalContent = targetElement.querySelector('[id^="widget-content-"]:not(#video-overlay-' + widgetId + ')');
                if (originalContent) {
                    originalContent.classList.remove('content-hidden');
                }

                // Stop video by removing src
                iframe.src = '';
            } else {
                // Show video and hide original content
                button.classList.add('active');
                button.classList.remove('fa-play-circle');
                button.classList.add('fa-stop-circle');

                // Hide original content
                const originalContent = targetElement.querySelector('[id^="widget-content-"]:not(#video-overlay-' + widgetId + ')');
                if (originalContent) {
                    originalContent.classList.add('content-hidden');
                }

                // Extract video ID and set iframe src
                const videoId = extractYouTubeVideoId(youtubeLink);
                if (videoId) {
                    iframe.src = `https://www.youtube.com/embed/${videoId}?autoplay=1`;
                    videoOverlay.style.display = 'block';
                } else {
                    console.error('Invalid YouTube URL:', youtubeLink);
                    // Revert button state if URL is invalid
                    button.classList.remove('active');
                    button.classList.remove('fa-stop-circle');
                    button.classList.add('fa-play-circle');
                }
            }
        }

        function extractYouTubeVideoId(url) {
            // Handle various YouTube URL formats
            const patterns = [
                /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
                /youtube\.com\/v\/([^&\n?#]+)/
            ];

            for (let pattern of patterns) {
                const match = url.match(pattern);
                if (match && match[1]) {
                    return match[1];
                }
            }
            return null;
        }
    </script>
@endonce