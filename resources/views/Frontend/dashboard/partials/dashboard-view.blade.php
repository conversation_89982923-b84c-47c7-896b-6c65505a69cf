{{-- Gridstack CSS and Required Fonts --}}
<link href="https://cdn.jsdelivr.net/npm/gridstack@12.1.2/dist/gridstack.min.css" rel="stylesheet">
<link rel="preconnect" href="https://rsms.me/">
<link rel="stylesheet" href="https://rsms.me/inter/inter.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

<style type="text/css">
    :root {
        --main-bg-color: #ffffff;
        --main-bg-color-light-gray: #f5f5f5;
        --widget-bg-color: #ffffff;
        --text-color: #34495e;
        --text-muted-color: #7f8c8d;
        --border-color: #ecf0f1;
    }

    /* Loader Styles */
    .loader-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: var(--main-bg-color-light-gray);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease-in-out;
    }

    .loader {
        display: none;
    }

    .skeleton-container {
        width: 100%;
        max-width: 800px; /* Adjust as needed */
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
        box-sizing: border-box;
    }

    .skeleton-item {
        background: linear-gradient(-90deg, #f0f0f0 0%, #e0e0e0 50%, #f0f0f0 100%);
        background-size: 400% 400%;
        animation: skeleton-shimmer 1.2s ease-in-out infinite;
        border-radius: 8px;
        margin-bottom: 15px;
    }

    @keyframes skeleton-shimmer {
        0% {
            background-position: 100% 50%;
        }
        100% {
            background-position: 0% 50%;
        }
    }

    .dashboard-view-container {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        background-color: var(--main-bg-color-light-gray);
        color: var(--text-color);
        line-height: 1.6;
        padding: 20px;
    }

    /* Mobile padding adjustment */
    @media (max-width: 768px) {
        .dashboard-view-container {
            padding: 10px;
        }
    }

    .grid-stack {
        margin: -5px;
    }

    /* Mobile grid adjustments */
    @media (max-width: 768px) {
        .grid-stack {
            margin: 0;
            gap: 0;
        }

        /* Force single column layout on mobile */
        .grid-stack > .grid-stack-item {
            width: 100% !important;
            left: 0 !important;
            position: relative !important;
            margin-bottom: 0 !important;
            padding: 0 !important;
            top: auto !important;
            margin-top: 0 !important;
        }

        .grid-stack > .grid-stack-item[gs-w="12"] {
            width: 100% !important;
        }

        /* Remove GridStack's internal spacing */
        .grid-stack-item .grid-stack-item-content {
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            padding: 0 !important;
        }
    }

    .grid-stack-item {
        transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .grid-stack-item-content {
        background: transparent;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .custom-widget-container {
        width: 100%;
        height: 100%;
        background-color: var(--widget-bg-color);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 15px;
        font-size: 0.85rem;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        min-height: 200px; /* Ensure minimum height on mobile */
    }

    /* Mobile widget container adjustments */
    @media (max-width: 768px) {
        .custom-widget-container {
            padding: 12px;
            font-size: 0.9rem;
            border-radius: 8px;
            margin-bottom: 10px; /* Add gap between widgets */
        }

        /* Remove margin from last widget */
        .grid-stack-item:last-child .custom-widget-container {
            margin-bottom: 0;
        }
    }

    @media (max-width: 576px) {
        .custom-widget-container {
            padding: 10px;
            font-size: 0.85rem;
        }
    }
   .custom-widget-body {
        height: 100%;
        display: flex;
        overflow: hidden;
    }
    .custom-widget-body > * {
        flex: 1; /* Ensures child fills available width */
        min-width: 0; /* Prevents overflow issues */
    }
    .custom-widget-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        margin-bottom: 7px;
        /* padding-bottom: 10px; */
        min-height: 44px; /* Increased from 30px to match button height */
        border-bottom: 1px solid var(--border-color);
        flex-shrink: 0;
        flex-wrap: wrap;
        gap: 8px;
    }

    .custom-widget-header .title {
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        min-width: 0;
        margin: 0; /* Remove any default margin */
        line-height: 1.1; /* Use normal line-height instead of matching min-height */
    }

    /* Mobile header adjustments */
    @media (max-width: 576px) {
        .custom-widget-header .title {
            font-size: 1rem;
        }
    }

   .custom-widget-header .controls {
        margin-left: 10px;
        flex-shrink: 0;
        min-height: 44px; /* Ensure controls area has consistent height */
        display: flex;
        align-items: center;
    }

    /* Special containers for Biorhythm and Menu */
    .biorhythm-container,
    .menu-container {
        flex: 1;
        min-height: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    /* Mobile adjustments for special containers */
    @media (max-width: 768px) {
        .biorhythm-container,
        .menu-container {
            min-height: 250px;
        }
    }

    /* Force full height for Biorhythm and Menu contents */
    .biorhythm-container > div,
    .menu-container > div {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .pool-content__title {
        border-bottom: none !important;
    }
    /* Improved responsive row layout */
    .widget-content-row {
        display: flex;
        height: 100%;
        /* min-height: 0; */
        gap: 8px;
        align-items: stretch;
    }

    .widget-content-row.reverse {
        flex-direction: row-reverse;
    }

    .widget-graph-column,
    .widget-image-column {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
    }
    .widget-image-column {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .widget-image-container {
        width: 100%;
        height: 100%;
        min-height: 150px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .widget-image-container img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        border-radius: 8px;
    }
    /* Make sure the chart container inside graph column uses full height */
    .widget-graph-column > * {
        flex: 1; /* Add this */
        min-height: 0; /* Add this */
        height: 100%; /* Add this */
    }
    /* Special handling for progress bars and charts without images */
    .widget-chart-placeholder:not(.widget-content-row) > * {
        flex: 1;
        height: 100%;
        min-height: 150px; /* Minimum height for standalone charts */
    }
    .controls{
        gap: 5px;
    }

    /* Enhanced mobile responsiveness */
    @media (max-width: 992px) {
        .widget-content-row,
        .widget-content-row.reverse {
            flex-direction: column;
            gap: 12px;
        }

        .widget-graph-column {
            min-height: 180px;
        }

        .widget-image-container {
            min-height: 180px;
            max-height: 250px;
        }
    }

    @media (max-width: 768px) {
        .widget-content-row,
        .widget-content-row.reverse {
            gap: 10px;
        }

        .widget-image-container {
            min-height: 150px;
            max-height: 200px;
            padding: 8px;
        }

        .widget-graph-column {
            min-height: 150px;
        }
    }

    /* Small screen adjustments */
    @media (max-width: 576px) {
        .widget-image-container {
            min-height: 120px;
            max-height: 150px;
            padding: 5px;
        }

        .widget-graph-column {
            min-height: 120px;
        }
    }

    .no-widgets-message {
        text-align: center;
        padding: 40px;
        color: var(--text-muted-color);
    }

    @media (max-width: 768px) {
        .no-widgets-message {
            padding: 20px;
        }

        .no-widgets-message h3 {
            font-size: 1.2rem;
        }
    }

    /* Ensure touch-friendly controls on mobile */
    @media (max-width: 768px) {
        .custom-widget-header .controls button,
        .custom-widget-header .controls a {
            min-width: 32px;
            min-height: 32px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
    }

    /* Prevent horizontal scroll on mobile */
    @media (max-width: 768px) {
        body {
            overflow-x: hidden;
        }

        .dashboard-view-container {
            overflow-x: hidden;
        }
    }
    /* Mobile adjustments */
    @media (max-width: 768px) {
        .custom-widget-header .controls button,
        .custom-widget-header .controls a {
            min-width: 30px;
            min-height: 30px;
        }
    }

    @media (max-width: 1024px) {
        .custom-widget-container{
            min-height: none !important;
            height: auto !important;
        }
        .widget-graph-column, .widget-image-column{
            flex: 100% !important;
            max-width: 100% !important;
            display: block !important;
            width: 100% !important;
        }

        .dashboard-progress-container {
           gap: 0px !important;
        }
        .widget-graph-column {
                        height: 300px !important;
                        overflow: auto !important;
        }
        .widget-content-row{
            display: block !important;
        }

        .widget-image-container img {
          height: 150px !important;
        }
    }
</style>

{{-- Loader Container with Skeleton --}}
<div class="loader-container">
    <div class="skeleton-container">
        <div class="skeleton-item" style="width: 100%; height: 30px;"></div>
        <div class="skeleton-item" style="width: 80%; height: 20px; margin-top: 10px;"></div>
        <div class="skeleton-item" style="width: 90%; height: 200px; margin-top: 20px;"></div>
        <div class="skeleton-item" style="width: 75%; height: 150px; margin-top: 20px;"></div>
    </div>
</div>

<div class="dashboard-view-container dashboard-designer-mode">
    @php
        $auto_menu_hide = cache()->get("user_auto_menu_hide_".getUserId()) ?? true;
    @endphp
    @if (!empty($dashboardData))
        <div class="grid-stack" id="dashboard-design" style="display: none">
            @foreach ($dashboardData as $widget)
                <div class="grid-stack-item" gs-x="{{ $widget['x'] ?? 0 }}" gs-y="{{ $widget['y'] ?? 0 }}"
                     gs-w="{{ $widget['width'] ?? 4 }}" gs-h="{{ $widget['height'] ?? 2 }}" gs-id="{{ $widget['id'] }}">

                    <div class="grid-stack-item-content">
                        @php
                            $diagramType = strtolower($widget['diagram_type'] ?? '');
                        @endphp
                        @if ($diagramType === 'biorythm')
                            <div class="custom-widget-container">
                                <div class="custom-widget-body" id="widget-content-<?= $widget['id'] ?>">
                                    <div class="biorhythm-container">
                                        @include('Frontend.partials.includes.layout-biorhythmus-v3', ['widget' => $widget])
                                    </div>
                                </div>
                            </div>
                        @elseif (in_array($diagramType, ['menu','own_topic','frequency_generator']))
                            <div class="custom-widget-container">
                                <div class="custom-widget-header">
                                    @if (!empty($widget['settings']['menu_link']))
                                        <h5 class="title">
                                            <a id="widget-title-{{ $widget['id'] }}" href="{{ $widget['settings']['menu_link'] }}" style="text-decoration: none; color: inherit; display: flex; align-items: center; gap: 8px;">
                                                {{ $widget['title'] }}
                                                <i class="fas fa-external-link-alt" style="font-size: 0.8em; opacity: 0.7;"></i>
                                            </a>
                                        </h5>
                                    @else
                                        <h5 id="widget-title-{{ $widget['id'] }}" class="title">{{ $widget['title'] }}</h5>
                                    @endif
                                    <div class="controls">
                                        @if($diagramType === 'own_topic')
                                            {{-- Switch button for Own Topic to toggle Frequency Generator --}}
                                            <button class="topic-btn btn-outline btn-top"
                                              id="toggle-btn-{{$widget['id']}}"
                                              onclick="toggleOwnTopicFrequencyGenerator('{{ $widget['id'] }}')"
                                              title="{{ trans('action.frequency_generator') }}"
                                              data-toggle="tooltip"
                                            >
                                                <svg class="topic-icon" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                                </svg>
                                            </button>
                                            @if(!empty($widget['settings']['youtube_link']))
                                            <button class="topic-btn btn-outline btn-youtube"
                                                    onclick="toggleYoutubePlayer('{{ $widget['id'] }}')"
                                                    aria-label="{{ trans('action.video_player') }}"
                                                    title="{{ trans('action.video_player') }}"
                                                    data-toggle="tooltip"
                                                    data-placement="top"
                                            >
                                                <svg class="topic-icon" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" 
                                                        d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"/>
                                                    <path stroke-linecap="round" stroke-linejoin="round" 
                                                        d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                            </button>
                                            @endif
                                        @endif
                                    </div>
                                </div>
                                <div class="custom-widget-body" id="widget-content-<?= $widget['id'] ?>">
                                    @includeIf('Frontend.dashboard.partials._navigation', ['widget' => $widget, 'diagramType' => $diagramType])
                                </div>
                            </div>

                        @elseif (in_array($diagramType, ['bar', 'line', 'radar', 'polararea', 'progress','progressbarsmall']))
                            <div class="custom-widget-container">
                                <div class="custom-widget-header">
                                    <h5 class="title" style="display: flex; gap: 8px; ">
                                        @if (!empty($widget['settings']['menu_link']))
                                                <a href="{{ $widget['settings']['menu_link'] }}" style="text-decoration: none; color: inherit; display: flex; align-items: center; gap: 8px;">
                                                    {{ $widget['title'] }}
                                                    <i class="fas fa-external-link-alt" style="font-size: 0.8em; opacity: 0.7;"></i>
                                                </a>
                                        @else
                                        {{ $widget['title'] }}
                                        @endif

                                        @if(!empty($widget['pool_ids']) && isset($widget['pool_ids'][1]))
                                        <livewire:dashboard.widgets.graphs.switch-pools
                                            :widget-id="$widget['id']"
                                            :pool-type="$widget['pool_type']"
                                            :pool-ids="$widget['pool_ids'] ?? []"
                                            :wire:key="'switch-pools-' . $widget['id'] . '-' . uniqid()"
                                        />
                                        @endif
                                    </h5>
                                    <div class="controls">
                                        <livewire:dashboard.color-analyses-add-to-cart
                                                :widget="$widget"
                                                :poolId="$widget['pool_type']"
                                                :wire:key="'color-analyses-add-to-cart-' . $widget['id']"
                                        />
                                    </div>
                                </div>
                                @if (!empty($widget['settings']['image']))
                                    @php
                                        $imageMaxWidth = $widget['settings']['image_max_width'] ?? 50;
                                        $graphWidth = 100 - $imageMaxWidth;
                                    @endphp
                                    <div class="custom-widget-body" id="widget-content-<?= $widget['id'] ?>">
                                        <div class="widget-content-row {{ $widget['settings']['image_position'] === 'right' ? '' : 'reverse' }}">
                                            <div class="widget-graph-column" style="flex: 0 0 {{ $graphWidth }}%; max-width: {{ $graphWidth }}%;">
                                                @includeIf('Frontend.dashboard.partials._graph', ['widget' => $widget, 'diagramType' =>
                                                $diagramType])
                                            </div>
                                            <div class="widget-image-column" style="flex: 0 0 {{ $imageMaxWidth }}%; max-width: {{ $imageMaxWidth }}%;">
                                                <div class="widget-image-container">
                                                    <img src="{{ asset('storage/images/' . $widget['settings']['image']) }}"
                                                         alt="{{ $widget['title'] ?? 'Widget image' }}" loading="lazy">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @else
                                    <div class="custom-widget-body" id="widget-content-<?= $widget['id'] ?>">
                                        @includeIf('Frontend.dashboard.partials._graph', ['widget' => $widget, 'diagramType' =>
                                        $diagramType])
                                    </div>
                                @endif
                            </div>
                        @elseif ($diagramType === 'richtext')
                            <div class="custom-widget-container">
                                <div class="custom-widget-header">
                                    @if (!empty($widget['settings']['menu_link']))
                                        <h5 class="title">
                                            <a href="{{ $widget['settings']['menu_link'] }}" style="text-decoration: none; color: inherit; display: flex; align-items: center; gap: 8px;">
                                                {{ $widget['title'] }}
                                                <i class="fas fa-external-link-alt" style="font-size: 0.8em; opacity: 0.7;"></i>
                                            </a>
                                        </h5>
                                    @else
                                        <h5 class="title">{{ $widget['title'] }}</h5>
                                    @endif
                                    <div class="controls">
                                        {{-- This empty div ensures consistent spacing and height --}}
                                    </div>
                                </div>
                                <div class="custom-widget-body" id="widget-content-<?= $widget['id'] ?>">
                                    @includeIf('Frontend.dashboard.partials._graph', ['widget' => $widget, 'diagramType' =>
                                    $diagramType])
                                </div>
                            </div>
                        @else
                            <div class="custom-widget-container">
                                <div class="custom-widget-header">
                                    <h5 class="title">Unsupported Widget</h5>
                                </div>
                                <div class="custom-widget-body" id="widget-content-<?= $widget['id'] ?>">
                                    Widget type ({{ $widget['diagram_type'] }}) not supported.
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <div class="no-widgets-message">
            <h3>Bu Dashboard'da Gösterilecek Widget Bulunmuyor.</h3>
        </div>
    @endif
</div>

<script src="https://cdn.jsdelivr.net/npm/gridstack@12.1.2/dist/gridstack-all.min.js"></script>
<script>
    ['DOMContentLoaded', 'inlinePageTrigger'].forEach(event =>
        document.addEventListener(event, function () {
        //calculate verticalMargin based on the screensize
        var verticalMargin = () => {
            switch (true) {
                case window.innerHeight < 500:
                    return 0;
                case window.innerHeight < 768:
                    return 5;
                case window.innerHeight < 1024:
                    return 10;
                default:
                    return 15;
            }
        };

        const gridOptions = {
            staticGrid: true,
            animate: false,
            margin: 5,
            float: 1,
            cellHeight: 75,
            disableOneColumnMode: false, // Enable one column mode for mobile
            disableDrag: true,
            disableResize: true, // Disable resize on all devices
            column: 12,
            verticalMargin: verticalMargin()
        };

        const grid = GridStack.init(gridOptions);
            $('#dashboard-design').fadeIn();

            // Fade out the loader once the dashboard is visible
            $('.loader-container').fadeOut(300, function() {
                $(this).remove(); // Remove the loader from the DOM for a cleaner state
            });

            window.addEventListener('resize', function() {
                grid.verticalMargin = verticalMargin();
            });
        })
    );

    function toggleOwnTopicFrequencyGenerator(widgetId) {
        Livewire.dispatch('toggleFrequencyGenerator', { widgetId: widgetId });
    }
    function toggleYoutubePlayer(widgetId) {
        Livewire.dispatch('toggleYoutubePlayer', { widgetId: widgetId });
    }

    ['livewire:init', 'inlinePageTrigger'].forEach(event =>
        document.addEventListener(event, function () {
            // Listen for pool switched events
            Livewire.on('poolSwitched', ({ widgetId, poolType }) => {
                // Update the widget's content
                Livewire.dispatch('updateWidget', { widgetId, poolType });
            });
            
            window.addEventListener('ownTopicToggled', function (e) {
                const data = e.detail;
                const titleEl = document.querySelector(`#widget-title-${data.widgetId}`);
                if (titleEl) {
                    // For anchor tags, update first text node or create one
                    if (titleEl.tagName === 'A') {
                        const firstText = titleEl.firstChild?.nodeType === Node.TEXT_NODE ? 
                            titleEl.firstChild : 
                            titleEl.insertBefore(document.createTextNode(''), titleEl.firstChild);
                        firstText.textContent = data.currentTitle;
                    } else {
                        titleEl.textContent = data.currentTitle;
                    }
                }
                const icon = document.querySelector(`#toggle-btn-${data.widgetId} i`);
                if (icon) {
                    icon.className = data.showFrequencyGenerator ? 'fas fa-wave-square' : 'fas fa-edit';
                }
            });

            window.addEventListener('youtubePlayerToggled', function (e) {
                const data = e.detail;
                const titleEl = document.querySelector(`#widget-title-${data.widgetId}`);
                if (titleEl) {
                    // For anchor tags, update first text node or create one
                    if (titleEl.tagName === 'A') {
                        const firstText = titleEl.firstChild?.nodeType === Node.TEXT_NODE ? 
                            titleEl.firstChild : 
                            titleEl.insertBefore(document.createTextNode(''), titleEl.firstChild);
                        firstText.textContent = data.currentTitle;
                    } else {
                        titleEl.textContent = data.currentTitle;
                    }
                }
                const icon = document.querySelector(`#toggle-btn-${data.widgetId} i`);
                if (icon) {
                    icon.className = data.showYoutubePlayer ? 'fas fa-wave-square' : 'fas fa-edit';
                }
            });

        })
    );
    // menu auto hide start
    ('{{ $auto_menu_hide }}' == 1) ? $('html').addClass('layout-collapsed') : $('html').removeClass('layout-collapsed');
    // menu auto hide end
</script>