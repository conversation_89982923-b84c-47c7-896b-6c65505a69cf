@switch($diagramType)
    @case('bar')
        <livewire:dashboard.widgets.graphs.bar 
            :widgetId="$widget['id']"
            :poolId="$widget['pool_type']"
            :settings="$widget['settings'] ?? []" 
            :wire:key="'widget-bar-' . $widget['id'] . '-pool-' . $widget['pool_type'] . '-' . rand()" />
        @break
    @case('line')
        <livewire:dashboard.widgets.graphs.line 
            :widgetId="$widget['id']"
            :poolId="$widget['pool_type']"
            :settings="$widget['settings'] ?? []"
            :wire:key="'widget-line-' . $widget['id'] . '-pool-' . $widget['pool_type'] . '-' . rand()" />
        @break
    @case('radar')
        <livewire:dashboard.widgets.graphs.radar 
            :widgetId="$widget['id']"
            :poolId="$widget['pool_type']"
            :settings="$widget['settings'] ?? []" 
            :wire:key="'widget-radar-' . $widget['id'] . '-pool-' . $widget['pool_type'] . '-' . rand()" />
        @break
    @case('polararea')
        <livewire:dashboard.widgets.graphs.polar-area 
            :widgetId="$widget['id']"
            :poolId="$widget['pool_type']"
            :settings="$widget['settings'] ?? []" 
            :wire:key="'widget-polar-' . $widget['id'] . '-pool-' . $widget['pool_type'] . '-' . rand()" />
        @break
    @case('progress')
        <livewire:dashboard.widgets.graphs.progressbar 
            :widgetId="$widget['id']"
            :poolId="$widget['pool_type']"
            :settings="$widget['settings'] ?? []" 
            :wire:key="'widget-progress-' . $widget['id'] . '-pool-' . $widget['pool_type'] . '-' . rand()" />
        @break
    @case('progressbarsmall')
        <livewire:dashboard.widgets.graphs.progress-bar-small 
            :widgetId="$widget['id']"
            :poolId="$widget['pool_type']"
            :settings="$widget['settings'] ?? []"
            :wire:key="'widget-progress-bar-small-' . $widget['id'] . '-pool-' . $widget['pool_type'] . '-' . rand()" />
        @break
    @case('richtext')
        {{-- Assuming rich text is a different widget type --}}
        <livewire:dashboard.widgets.graphs.rich-text
            :widgetId="$widget['id']"
            :poolId="$widget['pool_type']"
            :settings="$widget['settings'] ?? []"
            :wire:key="'widget-richtext-' . $widget['id'] . '-pool-' . $widget['pool_type'] . '-' . rand()" />
        @break
@endswitch
