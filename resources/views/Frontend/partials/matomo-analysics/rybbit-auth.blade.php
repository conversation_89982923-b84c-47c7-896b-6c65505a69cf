@auth
    @if(auth()->user()->email)
        <script>
            function initRybbit() {
                if (typeof rybbit !== "undefined" && rybbit.identify) {
                    try {
                        rybbit.identify(@json(auth()->user()->email));
                    } catch (error) {
                        console.error('Rybbit identify error:', error);
                    }
                } else {
                    setTimeout(initRybbit, 100);
                }
            }

            document.addEventListener("DOMContentLoaded", initRybbit);
        </script>
    @endif
@endauth