<!DOCTYPE html>

<html lang="{{ app()->getLocale() }}" class="default-style layout-offcanvas layout-collapsed">
<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="IE=edge,chrome=1">
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
    {{-- <link rel="icon" type="image/png" href="{{ asset('/favicon.ico') }}"> --}}
    <link rel="icon" type="image/png" href="{!! asset(brandFavicon()) !!}">
    <title>{{ brandName() }}</title>


    <!-- Icons. Uncomment required icon fonts -->

    <link rel="stylesheet" href="{{ asset('/vendor/fonts/fontawesome.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/fonts/ionicons.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/fonts/linearicons.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/fonts/open-iconic.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/fonts/pe-icon-7-stroke.css') }}">

    <!-- Core stylesheets -->
    <link rel="stylesheet" href="{{ asset('/vendor/css/bootstrap.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/css/appwork.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/css/theme-corporate.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/css/colors.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/css/uikit.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.css') }}">

    <link rel="stylesheet" href="{{ asset('/vendor/libs/flatpickr/flatpickr.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/libs/sweetalert2/sweetalert2.css') }}">
    <!-- <link rel="stylesheet" href="{{ asset('/vendor/libs/bootstrap-material-datetimepicker/bootstrap-material-datetimepicker.css') }}"> -->
    <link rel="stylesheet" href="{{ asset('/vendor/libs/timepicker/timepicker.css') }}">
    <!-- <link rel="stylesheet" href="{{ asset('/vendor/crudbooster/assets/lightbox/dist/css/lightbox.min.css') }}"> -->
    <link rel="stylesheet" href="{{ asset('/magnifig-popup/magnific-popup.css') }}">

    <!-- <link rel="stylesheet" href="{{ asset('/vendor/libs/minicolors/minicolors.css') }}"> -->

    <!-- Load polyfills -->
    <script src="{{ asset('/vendor/js/polyfills.js') }}"></script>
    <script>document['documentMode']===10&&document.write('<script src="https://polyfill.io/v3/polyfill.min.js?features=Intl.~locale.en"><\/script>')</script>

    <!-- Layout helpers -->
    <script src="{{ asset('/vendor/js/layout-helpers.js') }}"></script>

    <!-- Libs -->
    <link rel="stylesheet" href="{{ asset('/vendor/libs/datatables/datatables.css') }}">
    {{-- <!-- <link rel="stylesheet" href="{{ asset('/css/cssprogress.min.css') }}"> --> --}}
    <link rel="stylesheet" href="{{ asset('/css/jquery-ui.css') }}">

    {{-- <!-- `perfect-scrollbar` library required by SideNav plugin -->
    <!-- <link href="http://netdna.bootstrapcdn.com/font-awesome/4.1.0/css/font-awesome.min.css" rel="stylesheet"> --> --}}
    <link rel="stylesheet" href="{{ asset('/vendor/libs/perfect-scrollbar/perfect-scrollbar.css') }}">

    @yield('styles')
    <!-- Application stylesheets -->
    <link rel="stylesheet" href="{{ asset('/css/select2.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/application.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/responsive.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/toastr.min.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/libs/ladda/ladda.css') }}">
    <link rel="stylesheet" href="{{ asset('/vendor/libs/growl/growl.css') }}">
    <script src="{{ asset('js/jquery-3.4.1.min.js') }}"></script>
    <script src="{!! asset('/js/circle-progress.js') !!}"></script>
    <script src="{!! asset('/js/jquery.circle-progress.js') !!}"></script>
    <script src="{{ asset('/magnifig-popup/jquery.magnific-popup.min.js') }}"></script>
    @livewireStyles
    @include('Frontend.partials.matomo-analysics.matomo_script_to_header')
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body>

    @yield('layout-content')

    @livewireScripts
    <!-- Core scripts -->
    <script src="{{ asset('/vendor/js/jquery-ui.js') }}"></script>
    <script src="{{ asset('/js/html5sortable/html5sortable.min.js')}}"></script>
    <script src="{{ asset('/vendor/libs/popper/popper.js') }}"></script>
    <script src="{{ asset('/vendor/js/bootstrap.js') }}"></script>
    <script src="{{ asset('/vendor/js/sidenav.js') }}"></script>
    <script src="{{ asset('/vendor/js/toastr.min.js') }}"></script>

    <script>
        $.ajaxSetup({ headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') }});
    </script>

    <!-- Libs -->
    <script src="{{ asset('/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script src="{{ asset('/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.js') }}"></script>
    <script src="{{ asset('/vendor/libs/flatpickr/flatpickr.js') }}"></script>
    <script src="{{ asset('/vendor/libs/bootstrap-daterangepicker/bootstrap-daterangepicker.js') }}"></script>
    <script src="{{ asset('/vendor/libs/bootstrap-material-datetimepicker/bootstrap-material-datetimepicker.js') }}"></script>
    <script src="{{ asset('/vendor/libs/timepicker/timepicker.js') }}"></script>
    <script src="{{ asset('/vendor/libs/minicolors/minicolors.js') }}"></script>
    <script src="{{ asset('/js/ui_tooltips.js') }}"></script>
    <script src="{{ asset('/js/editor.js') }}"></script>
    <script src="{{ asset('/js/forms_pickers.js') }}"></script>

    {{-- <!-- `perfect-scrollbar` library required by SideNav plugin --> --}}

    <script src="{{ asset('/js/forms_custom-controls.js') }}"></script>

    <script src="{{ asset('/vendor/libs/datatables/datatables.js') }}"></script>

    <script src="{{ asset('/js/tables_datatables.js') }}"></script>
    <script src="{{ asset('/vendor/js/jquery.ui.touch-punch.min.js') }}"></script>
    <script src="{{ asset('/vendor/libs/spin/spin.js') }}"></script>
    <script src="{{ asset('/vendor/libs/ladda/ladda.js') }}"></script>
    <script src="{{ asset('/js/misc_ladda.js') }}"></script>
    <script src="{{ asset('/vendor/libs/growl/growl.js') }}"></script>

    @yield('scripts')

    @include('JSBlade.productjs')

    <script src="{{ asset('/js/application.js') }}"></script>

    <script>
        @if(Session::has('message'))
            var type="{{Session::get('alert-type','info')}}"
            switch(type){
                case 'info':
                    toastr.info("{{ Session::get('message') }}");
                    break;
                case 'success':
                    toastr.success("{{ Session::get('message') }}");
                    break;
                case 'warning':
                    toastr.warning("{{ Session::get('message') }}");
                    break;
                case 'error':
                    toastr.error("{{ Session::get('message') }}");
                    break;
            }
        @endif

    </script>
    @include('Frontend.modal.generic-modal')
    @include('Frontend.partials.loader')
    {{-- Rybbit Analytics Script --}}
    @if(config('services.rybbit.site_id'))
        <script async defer
                src="{{ config('services.rybbit.instance_url', 'https://app.rybbit.io') }}/api/script.js"
                data-site-id="{{ config('services.rybbit.site_id') }}"
                data-session-replay="true"
                data-track-errors="true"
        ></script>
        @include('Frontend.partials.matomo-analysics.rybbit-auth')
    @endif
</body>

</html>
