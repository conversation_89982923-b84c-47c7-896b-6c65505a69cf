<?php

return
[
	'users' => 'Users',
	'deleted' => 'Deleted!',
	'cancelled' => 'Canceled',
	'alert_allow_msg' => 'Auto play audio not allowed',
	'alert_allow_msg_details' => 'if auto play sound not working so please allow form setting and refresh current page',
	'delete_user' => 'Are you sure to delete this :siteName user',
	'delete_user2' => 'to delete',
	'home' => 'Home',
	'not_available' => 'N/A',
	'show_cart' => 'Saved Cart',
	'setting' => 'Setting',
	'shopping_cart' => 'Temp Cart',
	'Help' => 'Help',
	'help_link' => 'Help link',
	'user_setting' => 'User setting',
	'own_database' => 'Own database',
	'cron_already_setup' => 'Selected time cron already setup.please choose another time.',
	'remote_analysis' => 'Remote treatment',
	'select_random_analysis' => 'Choose random analysis',
	'clear' => 'Clear',
	'clear_all' => 'Clear All',
	'profile_img_alert' => 'Only jpg/jpeg and png files are allowed!',
	'manage_analysis' => 'Manage Analysis',
	'full_screen' => 'Full screen',
	'exit' => 'Exit',
	'frequency_due_power' => 'Frequency',
	'music' => 'Music',
	'pdf_logo_upload' => 'PDF logo upload',
	'custom_link' => 'Custom link',
	'update_successfully' => 'Update successfully',
	'no_menus_entries' => 'No entries available',
	'click_here' => 'click here',
	'cron_rong_time_error' => 'Please select right time',
	'view_cron_draft' => 'Manage remote treatment',
	'calculation_preview_list' => 'Previewlist',
	'first_starttime' => 'You have remote treatment for this first start time',
	'second_starttime' => 'You have remote treatment for this second start time',
	'third_starttime' => 'You have remote treatment for this third start time',
	'duepow_starttime' => 'You have remote treatment for this due power start time',
	'no_treat_need' => 'No treatment needed for today',
	'topic_name' => 'Your topic',
	'today' => 'Today',
	'this_week' => 'This Week',
	'this_month' => 'This Month',
	'later' => 'Older',
	'news' => 'News',
	'user' => 'users.users',
	'save_topic' => 'Save topic',
	'delete_topic' => 'Delete topic',
	'create_new_user' => 'Create User',
	'demo_pdoruct' => 'Demo Product',
	'product_name' => 'product name',
	'days_left' => 'Days Left',
	'status' => 'status',
	'active' => 'Active',
	'active_now' => 'Active Now',
	'topic_name_error' => 'Please enter topic name',
	'add_topic_name' => 'Successfully added topic name',
	'topic_name_alert' => 'Are you sure you want to remove topic name?',
	'remove_topic_name' => 'Successfully remove topic name',
	'cart_save' => 'Basket Successfully Saved',
	'cart_not_save' => 'Basket could not be saved',
	'last_treatment' => 'My Saved Treatments',
	'not_found' => 'Not Found',
	'hello' => 'Hello',
	'welcome_msg' => 'Nice of you to come back!',
	'topic_name_placeholder' => 'Please insert your topic here',
	'percentage' => 'in',
	'no_product' => 'No products active',
	'buy_product' => 'Dear customer, unfortunately, you have no active product. Here you can buy a product.',
	'no_product_title' => 'No Data',
	'edit_user_title' => 'Update details',
	'dashboard_options' => 'Dashboard Options',
    'dashboard_designer' => 'Dashboard designer',
    'old_dashboard' => 'Average view dashboard',
	'new_dashboard' => 'Detail view dashboard',
	'topic_cart_save' => 'Topic saved successfully in cart',
	'add_to_cart' => 'Add to Cart',
	'added_cart' => 'Already Added',
	'search' => 'Search',
	'pdf_heading' => 'My Saved PDFs',
	'confirm_alt_pdf1' => 'Are you sure you want to delete',
	'confirm_alt_pdf2' => 'pdf?',
	'bar_dashboard' => 'Progress Bar',
	'manage_users' => 'Manage users',
	'affilated_short_text1' => 'Your therapist recommends',
	'affilated_short_text2' => 'this product',
	'calculation_system_dashboard' => 'Calculation',
	'date_system_dashboard' => 'Calculation date',
	'sorting_system_dashboard' => 'sorting',
	'reset_dashboard' => 'Reset dashboard',
	'widgets' => 'Widgets',
	'subuser_edit' => 'Edit details',
	'successfully_reset' => 'Dashboard options successfully reset',
	'average_view' => 'Average view',
	'smart_view' => 'Smart view',
    'not_available_yet' => 'Not available yet',
	'birthday' => 'Birthday',
	'placeholder_address' => 'Place of Birth',
	'Sound' => 'sound',
	'disable_for_snapshot' => 'Disable for snapshot',
	'calculation_date' => 'Calculation date',
	'standard_language' => 'Default language',
	'pattern_analysis' => 'Random Analysis',
	'terms_of_use' => 'Terms of Use',
	'to_save' => 'TO SAVE',
	'circle_click' => 'Click on the circle to add a profile picture',
	'remove' => 'Remove',
	'calculation_biorhythm_person' => 'Calculation biorhythm person',
	'calculation_biorhythm_symptom' => 'Calculating biorhythm symptoms',
	'first_name_error' => 'Please fill in first name',
	'last_name_error' => 'Please fill last name',
	'dob_error' => 'Please complete date of birth',
	'address_error' => 'Please enter address',
	'agree_error' => 'Please confirm the terms of use',
	'pwd_error' => 'Please fill in the password',
	'length_error' => 'Please at least 6 characters',
	'gender_error' => 'Please select your gender!',
	'cpwd_error' => 'Please fill in the password',
	'equal_error' => 'The passwords do not match',
	'email_error' => 'Please fill in the e-mail address',
	'email_formet_error' => 'invalid mail format',
	'logout' => 'Log out',
	'profile_delete_alert' => 'Are you sure you want to delete your profile picture?',
	'delete_success' => 'Deleted successfully',
	'error' => 'Something went wrong. Please try again!',
	'email_already_exist' => 'email already exists',
	'placeholder_passwrod' => 'Enter password',
	'placeholder_re_passwrod' => 'Repeat password',
	'placeholder_email' => 'Your email address',
	'placeholder_firstname' => 'first given name',
	'placeholder_lastname' => 'Surname',
	'address' => 'Place of residence',
	'enter_here' => 'Enter here',
	'choose' => 'Choose',
	'update' => 'Update',
	'max_user_error' => 'You have maximum users! Please contact the therapist for more limits.',
	'user_created' => 'User created successfully',
	'user_updated' => 'User updated successfully',
	'invalid_login' => 'invalid login details',
	'welcome' => 'Welcome',
	'update_user' => 'UPDATE USER',
	'field_error' => 'Please fill required fields correctly!',
	'some_error' => 'Some mistakes!',
	'file_error' => 'this file type is not supported',
	'if_you_email' => 'email address?',
	'login_user_name' => 'Enter a username',
	'u_name_already_exist' => 'The user name has already been taken.',
	'yes' => 'Yes',
	'no' => 'No',
	'pattern_switch' => 'Show switch',
	'switch_on' => 'On',
	'switch_off' => 'Off',
	'export_personal_data' => 'Export personal data',
	'personal_data_export' => 'Personal data export',
	'personal_data_export_for' => 'Personal Data Export for',
	'report_generated_for' => 'Report generated for',
	'for_site' => 'For site',
	'at_url' => 'At URL',
	'on' => 'On',
	'user_id' => 'UserID',
	'user_nice_name' => 'User Nice Name',
	'user_email' => 'User email',
	'user_registration_date' => 'User Registration Date',
	'user_display_name' => 'User display name',
	'user_first_name' => 'User First Name',
	'user_last_name' => 'User Last Name',
	'user_birthday' => 'User Birthday',
	'user_village' => 'User Village',
	'user_login_name' => 'User login name',
	'about' => 'About',
	'all_fileds_required' => 'Please fill in all fields',
	'group_saved' => 'Group saved successfully',
	'already_exist' => 'Group already exists',
	'groups' => 'User Groups',
	'Groups' => 'User Groups',
	'group_name' => 'Group name',
	'new_user_group' => 'New User Group',
	'add_new_group' => 'Add New Group',
	'default_group' => 'Default group',
	'name' => 'Surname',
	'enter_group_name' => 'Please enter group name',
	'select_group' => 'SelectGroup',
	'dashboard_view' => 'how Dashboard option',
	'list_users' => 'List Users',
	'new_users' => 'New users',
	'username_error' => 'Please enter username',
	'day_error' => 'Please select day',
	'month_error' => 'Please select month',
	'year_error' => 'Please select year',
	'mail_send_email' => 'Email for remote treatment',
	'street_placeholder' => 'Enter a street',
	'place_placeholder' => 'Enter a place',
	'postcode_placeholder' => 'Enter a postcode',
	'postcode' => 'Postcode',
	'cover_pdf' => 'Cover PDF',
	'pro_pic_deleted' => 'Profile Picture Deleted Successfully',
	'pro_pic_confirm' => 'Delete profile picture?',
	'all_user_groups' => 'Show all user groups',
	'staff' => 'Staff',
	'staff_group' => 'Staff Group',
	'staff_list' => 'Stafflist',
	'max_staff' => 'Max Allow Staffs',
	'assignStaff' => 'Assign Staff',
	'year' => 'Year',
	'month' => 'Month',
	'pdf_export' => 'Export PDF',
	'treat_filter' => 'Treat all topics',
	'Select' => 'Select',
	'cart_topics' => 'Topics to be treated',
	'behandein' => 'Treat',
	'duration_power' => 'Duration power',
	'basket_list' => 'Basket list',
	'stop' => 'Stop',
	'treat_all_topics' => 'Add all required topics',
	'select_filter' => 'Select Filters',
	'reset' => 'Reset',
	'only_to_be_treated' => 'Only to be treated',
	'all_red' => 'All talk',
	'all_orange' => 'All orange',
	'all_green' => 'All Green',
	'save' => 'Save',
	'body' => 'body',
	'mental' => 'Mentally',
	'show_all' => 'Show all',
	'show_more' => 'Show More',
	'areyousure' => 'Notice',
	'yes_i_do' => 'Yes, I do!',
	'yes_delete' => 'Yes, delete it!',
	'you_cant_revert' => 'You won"t be able to revert this!',
	'duplicate_convert' => 'Want to duplicate this cron?',
	'confirm_duplicate' => 'Yes!! Duplicates',
	'confirm_delete' => 'Are you sure you want to delete this cart item? ?',
	'year_description' => 'Year analysis',
	'month_description' => 'Month analysis',
	'calander_picker' => 'Calculation date',
	'analysis_not_available' => 'All analysis data loaded',
	'enter_title' => 'Enter title',
	'successfully_update' => 'Successfully updated',
	'some_error_try' => 'Some error.try again!',
	'clear_cart' => 'Clear Cart',
	'list_cart' => 'Treatmendcart save',
	'save_cart' => 'Save Cart',
	'manage_topic' => 'topic',
	'pdf_title' => 'Create PDF',
	'genrate_pdf' => 'Genrate Pdf',
	'pdf_genrated' => 'PDF Genrated Successfully',
	'pdf_deleted' => 'PDF Deleted Successfully',
	'pdf_title_error' => 'Please enter title',
	'color_range_help' => 'Please fill in all color ranges to define the thresholds for different status indicators.',
	'color_ranges' => 'Color Ranges',
	'red_range' => 'Red Range',
	'orange_range' => 'Orange Range',
	'green_range' => 'Green Range',
	'required_field' => 'This field is required',
	'complete_all_ranges' => 'Please complete all color ranges before saving',
	'range_validation_error' => 'Color ranges must be in logical order. Red < Orange < Green and ranges must not overlap.',
	'checkout_remote_analysis' => 'Remote analysis',
	'value' => 'Value',
	'dont_access_pdf' => 'You have no access pdf or some errors. please try again later.',
	'pdf_title_open_tab' => 'Save pdf and open into new tab',
	'save_pdf' => 'Save PDFS',
	'new_tab_pdf_save' => 'Save and Open in New Tab',
	'cache_clear' => 'Cache Clear',
	'wrn_cache_clear' => 'Want to clear cache?',
	'success_cache_clear' => 'Cache clear successfully!',
	'week' => 'This week',
	'cart_month' => 'This month',
	'no_saved_carts' => 'no saved carts for this week',
	'open_basket' => 'Open cart',
	'close' => 'Close',
	'empty_cart' => 'Empty cart',
	'auto_save' => 'Auto save',
	'remove_save_cart' => 'Delete saved cart',
	'remove_save_cart1' => 'Clear',
	'clearCartSession' => 'Treatment basket is deleted, are you sure?',
	'save_cart_note' => 'straight',
	'empty_trash' => 'Empty Trash',
	'currently_treatment' => 'Currently in treatment',
	'abrot' => 'Abrot',
	'start_treat' => 'Start treatment',
	'login_page_heading' => 'Please fill in your login details.',
	'pwd_error_login' => 'Please enter password',
	'usermame_error_login' => 'Please enter username/email',
	'login_placeholder_usermame' => 'Enter username/email',
	'login_placeholder_password' => 'Enter password',
	'login' => 'Log in',
	'error_therapist' => 'You are not able to log in',
	'link_send' => 'Send password reset link',
	'backtologin' => 'Back to login',
	'receivemail' => 'You have received an email with the link to reset your password.',
	'page_heding' => 'Welcome to :siteName',
	'paragraph' => 'so that we can analyze you perfectly, we need some more data from you!',
	'dob' => 'Date of birth',
	'dop' => 'Birthplace',
	'note_save' => 'Note successfully saved.',
	'note_couldnt_save' => 'Note couldn"t save.',
	'no_more_data' => 'All data loaded',
	'delete_saved_cart_alert' => 'Are you sure to delete the treatment cart?',
	'go_back_saved_cart' => 'Go back',
	'confirm_alt_treatmanet1' => 'Do you want to delete the treatment',
	'confirm_alt_treatmanet2' => '?',
	'confirm_alt' => 'Are you sure to delete your topic?',
	'cancel' => 'Cancel',
	'cancel_msg' => 'Process canceled',
	'demo_user' => 'Demo fashion',
	'ok' => 'Yes',
	'delete_msg' => 'Topic deleted!',
	'confirm_clear_shopcart' => 'Are you sure you want to delete the shopping cart?',
	'confirm_clear_basket' => 'Are you sure you want to clear the treatment basket?',
	'basket_deleted' => 'Treatment basket deleted!',
	'confirm_alt_user' => 'Are you sure you want to delete this user?',
	'confirm_alt_submenu1' => 'Do you want to delete the submenu',
	'confirm_alt_submenu2' => '?',
	'pdf_logo_delete_alert' => 'Are you sure you want to delete the pdf?',
	'confirm_alt_linked_user1' => 'Do you want to unlink',
	'confirm_alt_linked_user2' => '?',
	'confirm_alt_reset_menu' => 'Are you sure you want to reset the menu?',
	'reset_menu_success' => 'Menu reset successfully',
	'unlinked_success' => 'User unlinked successfully',
	'unlinked' => 'Unlinked',
	'added_successfully' => 'Added successfully',
	'linked_heading1' => 'You want to add',
	'linked_heading2' => 'as a linked user',
	'already_linked_user' => 'Already linked user',
	'linked_user' => 'Linked users',
	'add' => 'Add',
	'addImage' => 'Add image',
	'this_email_taken_warning' => 'This email has already been taken',
	'required' => 'required',
	'add_new_analyasis' => 'Add New Analysis',
	'update_analyasis' => 'Update analysis',
	'analysis_created' => 'User input created successfully',
	'analysis_name' => 'User input name',
	'sub_title' => 'Sub title',
	'main_desc' => 'Description',
	'body_desc' => 'Body description',
	'mental_desc' => 'Mental description',
	'bioryth' => 'Bioryth',
	'min_value' => 'Minimum value',
	'max_value' => 'Maximum value',
	'analysis_name_placeholder' => 'User input name',
	'sub_title_placeholder' => 'Sub title',
	'body_desc_placeholder' => 'Body description',
	'mental_desc_placeholder' => 'Mental description',
	'bioryth_placeholder' => 'Bioryth',
	'min_value_placeholder' => '10',
	'max_value_placeholder' => '20',
	'analysis_name_error' => 'Please enter analysis name',
	'my_analysis' => 'His own input',
	'analysis_updated' => 'User input updated successfully',
	'list_analysis' => 'List user input',
	'overview_analysis' => 'Overview Analysis',
	'sub_menu' => 'Submenu',
	'main_menu' => 'Main menu',
	'min_red' => 'Min Red value',
	'max_red' => 'Max Red value',
	'min_orange' => 'Min Orange value',
	'max_orange' => 'Max Orange value',
	'min_green' => 'Min Green value',
	'max_green' => 'Max Green value',
	'submenu_name_error' => 'Please enter submenu name',
	'new_own_analysis' => 'Add New User Input',
	'sub_menu_placeholder' => 'Submenu',
	'min_red_placeholder' => 'Min Red value',
	'max_red_placeholder' => 'Max Red value',
	'min_orange_placeholder' => 'Min Orange value',
	'max_orange_placeholder' => 'Max Orange value',
	'min_green_placeholder' => 'Min Green value',
	'max_green_placeholder' => 'Max Green value',
	'list_submenu' => 'View Submenu',
	'action' => 'Action',
	'sno' => 'S.No.',
	'confirm_alt_analysis_audio' => 'Are you sure to delete user input audio?',
	'confirm_alt_analysis_img' => 'Are you sure to delete user input image?',
	'confirm_alt_analysis1' => 'Are you sure to delete',
	'confirm_alt_analysis2' => 'user input?',
	'body_img' => 'Body image',
	'body_audio' => 'Body Audio',
	'mental_img' => 'Mental image',
	'mental_audio' => 'Mental Audio',
	'desc_img' => 'DescriptionImage',
	'all_audio' => 'Audio',
	'select_submenu' => 'Select submenu',
	'new_analysis' => 'New user input',
	'submenu' => 'Submenu',
	'select_format' => 'Select format',
	'download_sample' => 'Download samples',
	'analysis_export_import' => 'Analysis export/import',
	'analysis_impo_success' => 'Analysis data imported successfully',
	'import' => 'import',
	'export' => 'export',
	'import_analysis' => 'Import analysis',
	'export_analysis' => 'Export Analysis',
	'upload_file' => 'Upload file',
	'up_csv_excel' => 'Upload CSV or Excel file.',
	'bioryth_p' => 'Bioryth P',
	'bioryth_m' => 'Bioryth M',
	'pmin_value' => 'Time Min Value (only numbers)',
	'pmaxmax_value' => 'Time Max value (only numbers)',
	'bioryth_p_placeholder' => 'Bioryth P',
	'bioryth_m_placeholder' => 'Bioryth M',
	'pmin_value_placeholder' => 'Minimum value',
	'pmax_value_placeholder' => 'Maximum value',
	'menu_setting_saved' => 'Menu setting saved successfully',
	'menu_setting' => 'Menu Setting',
	'submenu_created' => 'Submenu created successfully',
	'file_php_extension_blocked' => 'File extension not allowed',
	'global_setting' => 'Global setting',
	'submenu_setting' => 'Submenu Settings',
	'submenu_error' => 'Please select submenu',
	'bioryth_p_error' => 'Please enter bioryth p',
	'bioryth_m_error' => 'Please enter bioryth m',
	'pmin_value_error' => 'Please enter minimum time',
	'pmax_value_error' => 'Please enter maximum time',
	'menu_setting_list' => 'Add New Menu',
	'add_analysis_data' => 'Add user input data',
	'main_menu_error' => 'Please select main menu',
	'own_analyse_name' => 'Analysis name',
	'own_menu_name_placeholder' => 'Please enter menu name',
	'go_back' => 'Go back',
	'pmin_value_add' => 'Time Min Value (only numbers in seconds 1 min => 60 seconds)',
	'pmax_value_add' => 'Time Max value (only numbers in seconds 1 min => 60 seconds)',
	'ownmenucreatesuccess' => 'Main menu created successfully',
	'combo_status' => 'Combo status',
	'add_subemnu' => 'Add New Submenu',
	'recycle_delete_msg' => 'Recycle bin has been deleted.',
	'preview_from' => 'Preview of',
	'selectet_analysis' => 'Selected Analysis',
	'selectet_subemnus' => 'Selected submenus',
	'enable_customer_link' => 'Enable Customer Link',
	'enable_customer_notivications' => 'Enable Custom notifications (start and end email)',
	'enable_pdf_export' => 'Enable PDF export',
	'topic' => 'topic',
    'external_note' => 'External note',
    'internal_note' => 'Internal note',
	'start_btn' => 'begin',
	'analysis_data' => 'Analysis Data',
	'cron_user' => 'users.users',
	'cron_birthday' => 'Birthday',
	'cron_village' => 'Birthplace',
	'cron_email' => 'e-mail',
	'cron_abbort' => 'abortion',
	'cron_save' => 'Save/Start',
	'cron_done' => 'Done',
	'delete_all_closed' => 'Delete all closed',
	'confirm_alt_delete_closed_cron' => 'Do you want to delete the closed Cron',
	'done' => 'Done',
	'confirm_alt_delete_cron' => 'Do you want to delete the Cron',
	'cron_setting_updated' => 'Cron Setting updated successfully',
	'cron_setting_created' => 'Cron Setting created successfully',
	'cron_update' => 'Cron updated successfully',
	'available_merge_fields' => 'Available merge fields',
	'sender_mail_address' => 'Sender email address',
	'smtp_host' => 'SMTP host',
	'smtp_port' => 'SMTP port',
	'smtp_user_name' => 'STMP username',
	'smtp_host_password' => 'SMTP password',
	'smtp_test_connection' => 'Test Connection',
	'smtp_connection_success' => 'SMTP connection success',
	'smtp_connection_error' => 'Invalid SMTP Connection',
	'merge_fields_firstname' => 'First name',
	'merge_fields_lastname' => 'LastName',
	'merge_fields_treatment_link' => 'Treatment link',
	'merge_fields_start_date' => 'Start date',
	'merge_fields_end_date' => 'End date',
	'smtp_crypto' => 'SMTP crypto',
	'confirm_alt_delete_cron_series' => 'Do you want to delete the Cron series',
	'delete_series' => 'Delete series',
	'delete_single' => 'Delete single',
	'cron_setting_from' => 'From',
	'cron_setting_to' => 'To',
	'cron_from_to_title' => 'Values ​​select (from 0 to 0 => all red values)',
	'sender_name' => 'Channel name',
	'cron_prgress' => 'Setup from Cron in progress',
	'cron_add_new_treatment' => 'Add new remote treatment',
	'cron_start_date' => 'Start date time',
	'cron_end_date' => 'End Date Time (Only for due power)',
	'cron_due_power' => 'Due Power',
	'cron_due_power1' => 'Max',
	'cron_due_power2' => 'minutes',
	'cron_end_rong_time1' => 'Please select another Time Range - you can setup maximum',
	'cron_end_rong_time2' => 'min.',
	'cron_customer_link' => 'Enable Customer Link',
	'cron_email_notivications' => 'Enable Custom notifications (start and end email)',
	'cron_export' => 'Enable PDF export',
	'cron_topic' => 'Add a topic',
	'cron_note' => 'Note: only Red values ​​​​are in this Type from Analysis are added to the treatment list',
	'cron_note_placeholder' => 'Enter topic here.',
	'cron_preview' => 'Preview',
	'cron_created' => 'Cron created successfully',
	'cron_users_error' => 'Please select one user',
    'cron_submenu_placeholder' => 'No analysis selected',
    'cron_submenu_error' => 'Please select analysis',
    'cron_analyses_placeholder' => 'Select analysis',
    'cron_analyses_error' => 'Please select analysis',
	'cron_select_user' => 'Select User',
	'preview_cron' => 'Preview cron details',
	'cron_start_error' => 'Please select start date',
	'cronSelectUser' => 'Select User',
	'cronTreatmentType' => 'Select the type of treatment',
	'cron' => 'Cron',
	'causes' => 'Causes',
	'medium' => 'medium',
	'tipp' => 'Tip',
	'selected_analysis' => 'Selected Analysis',
	'length' => 'Length',
	'frequency' => 'Frequency',
	'day' => 'Day',
	'auto_genrate' => 'Auto generated',
	'due_power_setting' => 'Enable due power',
	'calculation_next_day' => 'Apply calculation on next day',
	'cron_remote_treatmend' => 'Remote treatment',
	'cron_total_uses' => 'Total uses',
	'finished' => 'Finished',
	'open' => 'Open',
	'due_power' => 'Due Power',
	'single' => 'single',
	'select_length' => 'SelectLength',
	'length_type_error' => 'Please select length type',
	'second_start_time' => 'Second start time',
	'third_start_time' => 'Third start time',
	'cron_length_error' => 'Please enter length',
	'cron_usr_time2_error' => 'Enter second time',
	'cron_usr_time3_error' => 'Enter third time',
	'cron_end_date_error' => 'Please select end date',
	'save_draft' => 'Save Draft',
	'ok_stop_user' => 'OK',
	'confirm_alt_delete_cron_users' => 'really, you want to stop cron jobs?',
	'confirm_alt_select_user' => 'Please select at least one user',
	'stop_user_cron' => 'Stop cron',
	'pdf_attechment_subject' => 'Cron pdf',
	'pdf_attechment_message' => 'Hi, please find cron attechment pdf',
	'cron_view' => 'View Crons',
	'alert_max_submenu_hgeading' => 'Max submenu alert',
	'alert_max_submenu_details' => 'Are you sure to add more than %s Analysis Data?',
	'ok_menu_error' => 'OK',
	'stop_remote_treat' => 'Remote Treatment Stopped',
	'active_remote_treat' => 'Remote Treatment Activated',
	'stop_remote_email' => 'Remote Treatment Stop Sending Email',
	'active_remote_email' => 'Remote Treatment Activated Sending Email',
	'no_fixed_cron_time' => 'No fixed start time',
	'no_treatment_needed' => 'No treatment needed',
	'no_analysis' => 'No Analysis Found',
	'download_pdf_text' => 'Download your treatment as pdf',
	'download_pdf' => 'Download PDF',
	'no_access1' => 'Sorry! you cant access treatment. Please contact your therapist',
	'no_access2' => '(admin user)',
	'close_browser' => 'Close Browser',
	'medium_mittel_tipp' => 'medium, cause, tip',
	'resend_treatment' => 'Are you sure to resend this treatment again to client?',
	'resend_btn' => 'Resend',
	'treatment_finished' => 'Optimized Result Save and Treatment finished',
	'confirm_alt_group1' => 'Do you want to delete the group',
	'confirm_alt_group2' => '?',
	'group_updated' => 'Group update successfully',
	'manage_groups' => 'Management Group',
	'cron_setup' => 'Remote treatment setup',
	'cron_cart' => 'Cron Cart',
	'cron_setting' => 'Cron setting',
	'cron_default_settings' => 'Default settings',
	'cron_template_title_start' => 'Title start of the e-mail templates',
	'cron_start_title_placeholder' => 'Her energetic treatment is ready',
	'cron_template_start' => 'Change the start of the e-mail template',
	'cron_start_placeholder' => 'Change the start of the e-mail template',
	'cron_template_title_stop' => 'Email Template Change title stop',
	'cron_stop_title_placeholder' => 'Her energetic treatment is ready',
	'cron_template_stop' => 'Change the email template',
	'cron_stop_placeholder' => 'Change the start of the email template',
	'cron_topic_placeholder' => 'Enter your topic here',
	'cron_available_placeholder' => 'Press to display the available placeholders',
	'no_email_setup' => 'No email setup',
	'cron_send_email_time' => 'Before cron send email time',
	'cron_in_minutes' => '(in minutes)',
	'cron_send_email_time_placeholder' => '10',
	'start_email_due_power' => 'Start email due to power',
	'end_email_due_power' => 'End email due to power',
	'start_email_1x' => 'Start email 1x',
	'end_email_1x' => 'End email 1x',
	'cron_blank_title_placeholder' => 'Title blank of the e-mail templates',
	'cron_blank_placeholder' => 'Change the blank of the e-mail template',
	'cron_template_blank' => 'Change the blank of the e-mail template',
	'cron_template_title_blank' => 'Title blank of the e-mail templates',
	'cron_main_global_footer_title' => 'Change the footer of the email template',
	'cron_main_global_footer_placeholder' => 'Enter email footer design',
	'global_footer_message' => 'Global Footer Message',
	'first_name' => 'first name',
	'last_name' => 'LastName',
	'user_name' => 'Surname',
	'email' => 'E-mail',
	'user_registered' => 'User Registered',
	'edit_at' => 'Edit at',
	'user_language' => 'User language',
	'edit' => 'Edit',
	'delete' => 'Delete',
	'user_groups' => 'User Groups',
	'account_type' => 'Account status',
	'allow_login' => 'Allow Login',
	'select_sub_menu' => 'Select Sub menu',
	'07_days' => '07 Days',
	'14_days' => '14 days',
	'30_days' => '30 days',
	'60_days' => '60 days',
	'90_days' => '90 Days',
	'365_days' => '365 days',
	'cron_days' => 'Days',
	'all' => 'Everything',
	'share' => 'Share',
	'day_left' => 'Day Left',
	'terms_download' => 'Terms',
	'download' => 'Download',
	'currently_allow' => 'Currently allow for',
	'user_setting_table_heading' => 'Share Product',
	'user_setting_table_row' => 'Share Options',
	'expired' => 'Expired',
	'english' => 'English',
	'german' => 'German',
	'demo_mode' => 'Demo fashion',
	'affilated_product' => 'Affiliate Product',
	'display_name' => 'Display name',
	'sort_default' => 'Default sorting',
	'not_start_msg' => 'Your remote treatment starts at',
	'expire_msg1' => 'Your remote treatment was on',
	'expire_msg2' => 'please contact your therapist for a new session',
	'cart_from_pdf' => 'Treatment from',
	'for_user_pdf' => 'For users',
	'topic_pdf' => 'Own Topic',
	'info_body_pdf' => 'Body description',
	'info_mental_pdf' => 'Mental description',
	'ursache_pdf' => 'Cause',
	'mittel_pdf' => 'medium',
	'tipp_pdf' => 'Tip',
	'missing_analysis_pdf' => 'no Analysis Data added to this',
	'created_pdf' => 'Created',
	'for_pdf' => 'For',
	'analyse_pdf' => 'Analysis',
	'date_pdf' => 'Dob',
	'address_pdf' => 'Address',
	'notes_pdf' => 'Notes',
	'pdf_note1' => 'We would like to point out that all our methods are not effective according to the scientific criteria of conventional medicine',
	'pdf_note2' => 'According to the scientific criteria of conventional medicine, the products can only have a placebo effect on the health and well-being of users.',
	'pdf_note3' => 'In case of illness or health problems, you should definitely seek the advice of your doctor. The use of our products should not cause you to interrupt or stop your doctors care.',
	'pdf_note4' => 'All tested parameters are an information and energy analysis, which was determined on the basis of the biorhythm. These results provide evidence of blockages and energy deficiencies and showin which areas we ourselves can and should contribute.',
	'pdf_note5' => 'The aim is not to replace the doctor or therapist, but this evaluation should contribute to actively contributing to one"s own well-being.',
	'pdf_note6' => ':siteName is a great way to combine it with all other methods.',
	'pdf_note7' => ':siteName scans the energy and information field of biological systems (human, animal, ...) in just a few seconds and displays the current status. After the analysis, all tested parameters are corrected and optimized. :siteName uses frequencies determined for correction, thus strengthening the necessary information and vibrations,thereby optimizing body, mind and soul. At the same time, the vibrations are stored on the carrier (magnetic card and bracelet)',
	'pdf_note8' => 'You may feel that the effect is slowing down after a while. In this case, it is advisable to create a new analysis and correction, or a new information carrier, as the needs or the blockages have changed.',
	'pdf_note9' => 'interpretation',
	'pdf_note10' => 'The percentage shows the energy level.<br/>A `red` male symbol indicates that the cause is physical.<br/>A `red` heart symbol indicates that the cause is on the emotional level.<br/>If both symbols (male and heart) are `red`, the cause is acute on the physical and mental level.<br/>If both symbols are `grey`, they are old (chronic) blockages.',
	'no_data_datatable' => 'No data available in tables',
	'no_records_available' => 'No records available',
	'per_page' => 'Display _MENU_ records per page',
	'showing_page' => 'Showing _START_ to _END_ of _TOTAL_ records',
	'filtered_records' => 'filtered from _MAX_ total records',
	'search_datatable' => 'Search',
	'first_datatable' => 'First',
	'last_datatable' => 'load',
	'next_datatable' => 'Next',
	'previous_datatable' => 'Previous',
	'started_treatments' => 'Started treatments',
	'start' => 'Start time',
	'end_date' => 'End date',
	'completed' => 'Completed',
	'clock' => 'oh watch',
	'details_view' => 'Details View',
	'open_treatment' => 'Open treatment',
	'one_time' => 'One time',
	'selected_user' => 'Selected users',
	'start_date' => 'Start date',
	'cron_type' => 'CronType',
	'added_date' => 'Added date',
	'draft_cron' => 'Draft Cron',
	'view_draft_cron' => 'Remote Treatment View/Draft',
	'due_power_draft' => 'Due Power',
	'single_draft' => 'single',
	'series_draft' => 'Series',
	'draft_days' => 'days',
	'draft_and' => 'and',
	'edit_start_date_heading' => 'update cron start date',
	'start_date_error_cron' => 'please enter date or try again',
	'ser_no' => 'Serial no.',
	'cron_partially_open' => 'Partially open',
	'mail_status' => 'Email status',
	'mail_false' => 'false.false',
	'cron_pdf' => 'Cron pdf',
	'cron_attechment' => 'Please find pdf attachment',
	'hi' => 'Hi',
	'affilate_product' => 'Affiliate Product',
	'affilate_link' => 'Affiliate link',
	'update_link' => 'Update link',
	'remove_link' => 'Remove link',
	's_no' => 'S.No.',
	'link' => 'link',
	'confirm_alt_remove_affilated' => 'Are you sure you want to delete it?',
	'affilated_update_successfully' => 'Update successfully',
	'please_add_link_error' => 'Please add link',
	'page_title_new_user' => 'New user',
	'page_title_edit_admin' => 'Edit admin',
	'page_title_reset_password' => 'Reset password',
	'page_title_personal_information' => 'Personal information',
	'page_title_lead_system' => 'Lead_system',
	'page_title_admin_edit' => 'Admin edit',
	'page_title_dashboard' => 'Dashboard',
	'page_title_other_details' => 'Other details',
	'page_title_update_admin' => 'Update admin',
	'page_title_edit_user' => 'Edit user',
	'page_title_analysis' => 'Analysis',
	'page_title_premium_product' => 'Premium product',
	'page_title_show_cart' => 'Show cart',
	'page_title_show_cart_single' => 'Show cart single',
	'page_title_dauer_power' => 'Continuous power',
	'page_title_to_treat' => 'To treat',
	'page_title_direkt_mini' => 'Directly mini',
	'page_title_efit_direkt' => 'Efit directly',
	'page_title_users_list' => 'User list',
	'page_title_manage_groups' => 'Manage groups',
	'page_title_new_cron' => 'New cron',
	'page_title_cron_preview' => 'Cron preview',
	'page_title_cron_setting' => 'Remote treatment setting',
	'page_title_cron_remote' => 'Cron remote',
	'page_title_cron_treatment' => 'Cron treatment',
	'page_title_cron_treatment_single' => 'Cron treatment single',
	'page_title_cron_draft' => 'Cron draft',
	'page_title_cron_view' => 'Cron view',
	'page_title_used_cron' => 'Used cron',
	'page_title_menu_setting' => 'Menu setting',
	'page_title_new_user_input' => 'New user input',
	'page_title_my_user_input' => 'My user input',
	'cron_select_analysis' => 'No analysis data selected',
	'page_title_activity_log' => 'Activity log',
	'cron_count_selected' => 'Items selected',
	'page_title_cron_trash' => 'Cron trash',
	'full_name_cron_view' => 'Full name',
	'page_heading1_cron_view' => 'List view',
	'page_heading2_cron_view' => 'Remote treatment',
	'select_type' => 'SelectType',
	'select_status' => 'SelectStatus',
	'save_as_fillter_cron_view' => 'save as default filter',
	'delete_all_finished_cron_view' => 'Delete all finished',
	'start_time_cron_view' => 'Start time',
	'end_time_cron_view' => 'End time',
	'run_time_cron_view' => 'Runtime',
	'open_by_user_cron_view' => 'Open by user',
	'show_data_cron_view' => 'ShowData',
	'allow_cron_view' => 'allow',
	'stop_cron' => 'Break up',
	'start_cron' => 'Allow',
	'start_cron_view' => 'begin',
	'delete_series_cron_view' => 'delete series',
	'due_power_cron_view' => 'due power',
	'single_cron_view' => 'single',
	'series_cron_view' => 'Series',
	'details_first' => '#',
	'to' => 'to',
	'edit_email' => 'Edit email',
	'start_sending_email' => 'Start sending email',
	'stop_sending_email' => 'Stop sending email',
	'change_email_for' => 'Change email for',
	'new_email_address' => 'New email address',
	'allow_single' => 'allow single',
	'resend_pdf' => 'Resend PDF',
	'new_tab_pdf' => 'View PDF in New Tab',
	'you_sure_delete_signle1' => 'You sure to delete',
	'you_sure_delete_signle2' => 'are you sure to delete this entry?',
	'yes_cron_view' => 'Yes',
	'no_cron_view' => 'No',
	'mail_send_success' => 'Email sent successfully!',
	'you_sure_delete_series1' => 'You be sure to delete the series',
	'you_sure_delete_series2' => 'You sure to delete the',
	'you_sure_delete_series3' => 'series?',
	'you_sure_allow' => 'you are sure to allow',
	'you_sure_allow1' => 'series?',
	'resend_pdf_alert1' => 'You are sure to resend PDF via email to:',
	'resend_pdf_alert2' => 'Are you sure to send the PDF?',
	'stop_sending_alert1' => 'You sure to',
	'stop_sending_alert2' => 'sending email for this',
	'stop_sending_alert3' => 'cron?',
	'stop_sending_alert4' => 'Are you sure? the client gets no email from us. we don"t delete it',
	'setting_saved_succfully' => 'Setting saved successfully',
	'email_blank_error' => 'Please enter valid email address',
	'invalid_email_address' => 'invalid email address',
	'pdf_send_successfully' => 'PDF send successfully',
	'email_changed_succfully' => 'Email changed successfully',
	'online' => 'On-line',
	'draft' => 'Draft',
	'you_sure_all_finished' => 'are you sure to delete all finished cron jobs?',
	'pdf_not_send' => 'pdf not send .please enable email setting',
	'no_data_found' => 'No data found.',
	'set_filter' => 'Set filters',
	'cron_not_open' => 'Not open',
	'cron_open' => 'Open',
	'restore_cron' => 'Restore cron',
	'cron_trash' => 'Recycle on',
	'page_heading1_cron_trash' => 'Recycle on',
	'page_heading1_trash_cron_view' => ':siteName remote treatment',
	'you_sure_restore_cron' => 'Are you sure you want to restore this cron?',
	'restore_cron_success' => 'Cron restore successfully',
	'you_sure_restore_cron_series' => 'Are you sure you want to restore this series cron?',
	'activity_desc' => 'Description',
	'activity_added' => 'Added',
	'activity_log' => 'Activity log',
	'share_user_error1' => 'Dear Therapist you have only',
	'share_user_error2' => 'Users free to share, please contact the Support Team to order more shared accounts.',
	'share_menu_error' => 'Dear Therapist you have only %s Database free to select, pls contact the Support Team to order more..',
	'max_share_user_error' => 'You have added the maximum number of users',
	'hilfe_center' => 'Help center',
	'tutorial_start' => 'Tutorial start',
	'search_hifi_post' => 'Search our help',
	'genral_text_intro1' => 'Hello to the tutorial of the analysis!',
	'year_month_tutorial' => 'With month / year you can create an annual analysis. With month, you get the current month.',
	'calendar_tutorial' => 'Here you can analyze the day on a special date',
	'datepicker_tutorial' => 'Enter your desired date of calculation here.',
	'datepickersave_tutorial' => 'Save the date, the analysis will be recalculated automatically',
	'datepickerclose_tutorial' => 'Close the window without changes',
	'datepickerreset_tutorial' => 'Reset the date to the current date',
	'filter_tutorial' => 'Here you can change the display of the analysis. Default worst value up to the maximum value.',
	'mobile_calendar_tuto' => 'Here you will find all date settings',
	'manage_pdf_tuto' => 'Here you create a PDF, the data in the treatment basket are transferred to the PDF. in the Dashboard you can download this PDF.',
	'manage_topic_tuto' => 'Here you can change / add your own topic',
	'clear_cart_tuto' => 'Delete the treatment basket',
	'save_cart_tuto' => 'Save treatment basket',
	'show_cart_tuto' => 'Find your saved treatment baskets',
	'remote_analysis_tuto' => 'Start a remote treatment with the existing treatment basket',
	'normal_treat_tuto' => 'Start normal treatment (1 run)',
	'dur_treat_tuto' => 'Duration Power start (ends after some time)',
	'analysis_box_tuto' => 'Analysis BOX - Here you see the first entry of the analysis',
	'analysis_title_tuto' => 'Title of the analysis, if you keep your mouse on the title, you will see the entire text.',
	'material-switch_tuto' => 'Switch to Stochastic Analysis',
	'cartbutton_tuto' => 'Add this entry to your treatment basket.',
	'male_heart_tuto' => 'With the heart and body, you can see if it has a psychological or a physical cause. The symbols are red.',
	'progressbar-text' => 'Value of the analysis. 0-100 => Noticeable and should be treated.',
	'tendency_tuto' => 'Tendency display the next days',
	'panel-footer_tuto' => 'Find the cause, remedy or tip',
	'intro_next' => 'Next',
	'intro_prev' => 'Beacon',
	'intro_skip' => 'Skip',
	'intro_done' => 'Done',
	'topictobecoverd' => 'Topics to be covered',
	'normal' => 'Normal',
	'cart' => 'Cart',
	'add_to_burger' => 'Add to burgers',
	'hide_system_burger' => 'Hide system burgers',
	'create_burger' => 'Create burgers',
	'burger_setting' => 'Burger setting',
	'topic_intro1' => 'Here you find your own topic.',
	'topic_intro2' => 'You can add there your topic, this topic will change the calculations',
	'topic_intro3' => 'With save topic, the topic will be saved.',
	'topic_intro4' => 'With delete topic, you remove it.',
	'treatment_intro1' => 'you will find the latest Treatment for the selected User here.',
	'treatment_intro2' => 'by pressing to the title, you can have a look into the analysis data.',
	'treatment_intro3' => 'you can quickly add into a new treatment basket.',
	'treatment_intro4' => 'you can remove this cart short from the system.',
	'news_intro_title' => 'you can find the latest product updates in the news box.',
	'pdf_intro1' => 'you can find all saved PDF from selected users here',
	'pdf_intro2' => 'by clicking to title, you can download the PDF.',
	'pdf_intro3' => 'by clicking on the icon, you delete the PDF from system.',
	'big_circle_intro_whole1' => 'Here you will find an overview of an analysis, which is summed up.',
	'big_circle_intro_whole2' => 'The round, large circle shows you how the values ​​are split up and how the values ​​are distributed.',
	'mini_circle_intro_whole1' => 'In the small circle, they find for an analysis the number of tests, and how these split up.',
	'mini_circle_intro_whole2' => 'This is the exact name of the analysis, by clicking on the title you open this analysis and can further evaluate it.',
	'users_intro1' => 'Here you can find all users',
	'users_intro2' => 'by Pressing to the Icon, you switch to this user',
	'users_intro3' => 'by press - you delete the user',
	'users_intro4' => 'by press + you add a New user',
	'users_intro5' => 'by press edit you edit the selected user',
	'burger_tutorial' => 'Here you will find your products and the menu',
	'home_tutorial' => 'Home Button, quickly return to the dashboard',
	'checked_tutorial' => 'Here you will find all stored treatment baskets',
	'setting_tutorial' => 'Here you can change your setting by the user. (Primary user)',
	'search_hifi_tutorial' => 'Here you will find the search and the tutorials',
	'right_side_tutorial' => 'Here you see the selected user. As well as the extended administration menu.',
	'oldDashBoard_tutorial' => 'This analysis displays a circle with a calculated average.',
	'newDashBoard_tutorial' => 'This analysis shows the exact number of red, green and orange values.',
	'progressDashBoard_tutorial' => 'Lists presentation for therapists',
	'dashBoardoption_tutorial' => 'Show dashboard options',
	'subuser_intro1' => 'this is own details box',
	'subuser_intro2' => 'by press edit you edit your own details',
	'system_intro1' => 'With this box you can quickly change the analysis settings',
	'system_intro2' => 'Select the calculation year month',
	'system_intro3' => 'Select the calculation date',
	'system_intro4' => 'Select the sorting of the analysis data',
	'system_intro5' => 'Save the changes to the analysis settings',
	'system_intro6' => 'Set the date of the calculation back to the daily value.',
	'reset_button_widgets' => 'You can reset the Dashboard elements to the default view',
	'checkbox_widgets' => 'you can hide / show with the checkboxes the Dashboard elements',
	'system_setting' => 'System settings',
	'reset_calculation_date' => 'reset calculation date',
	'info_current_allow_for' => 'info- User can not log in because login has been disabled.',
	'select_release_time' => 'Select the release time',
	'api_settings' => 'API settings',
	'waiting_heading1' => 'Countdown to my remote treatment',
	'waiting_days' => 'Days',
	'waiting_hours' => 'Hours',
	'waiting_minutes' => 'minutes',
	'waiting_seconds' => 'Seconds',
	'page_heading1_own_analysis_list' => 'Own database',
	'page_heading2_own_analysis_list' => ':siteName Own database',
	'page_heading1_menu_list' => 'Own menu',
	'page_heading2_menu_list' => ':siteName Own database',
	'page_heading1_user_list' => 'User list',
	'page_heading2_user_list' => ':siteName therapist',
	'page_heading1_group_list' => 'Group List',
	'page_heading2_group_list' => ':siteName therapist',
	'view' => 'View',
	'page_heading1_cron_setting' => 'Cron Setting',
	'page_heading2_cron_setting' => ':siteName remote treatment',
	'page_heading1_activity_log' => 'Activity Log',
	'page_heading2_activity_log' => ':siteName',
	'page_heading1_affilated' => 'Affiliate Product',
	'page_heading2_affilated' => ':siteName therapist',
	'select_analysis' => 'Select Analysis',
	'available_placeholders' => 'Available placeholders',
	'note_cron_view' => 'straight',
	'cron_note2' => 'Add a note',
	'cron_placeholder_note2' => 'Add a note placeholder',
	'cron_duplicate' => 'Duplicates',
	'options' => 'Options',
	'cron_options' => 'Options',
	'you_sure_allow_crons' => 'Are you sure to allow crons?',
	'you_sure_allow_crons_alert' => 'Cron allow done',
	'cron_archive' => 'Archives',
	'dashboard_details' => 'Details View Dashboard',
	'dashboard_progress' => 'Progress bar view',
	'sub_user' => 'Sub user',
	'therapist' => 'therapist',
	'treatment_expired' => 'Your remote treatment has expired.',
	'stop_sharing' => 'Stop sharing',
	'success_order' => 'Order successfully saved',
	'checkenergiefeld' => 'Energy field test',
	'deleteView' => 'Delete view',
	'infotest' => 'Information test',
	'not_right_access' => 'You don"t have access!!',
	'treatment_finished_cron' => 'Remote treatment completed successfully.',
	'select_due_time' => 'Runtime/Time select:',
	'therapistuser' => 'Admin',
	'admin' => 'users.users',
	'subuser' => 'Sub user',
	'unauthorised' => 'Unauthorized access request!!!',
	'option_changed' => 'Option Has Changed',
	'hide_reaction' => 'Click for Hide reaction value',
	'hide_pdf' => 'Click Hide PDF for reaction value',
	'show_reaction' => 'Click for Show reaction value',
	'show_pdf' => 'RA PDF option',
	'createdby' => 'Created By :',
	'select_pool' => 'Select pool',
	'submit' => 'Submit',
	'form_gnerator' => 'Shape generator',
	'generator_form_code' => 'Generator form code',
	'sign_up_form_code' => 'Sign up form code',
	'group_updated_successfully' => 'Group Updated Successfully',
	'something_went_wrong' => 'Something went wrong',
	'want_to_save_PDF' => 'Do you want to Save PDF',
	'you_want_to_delete' => 'Do you want to Delete',
	'package_deleted_successfully' => 'Package Deleted Successfully',
	'removed_successfully' => 'Removed Successfully',
	'topic_saved_successfully' => 'Topic Saved Successfully',
	'cart_package_added_successfully' => 'Cart Package Added Successfully',
	'cart_clear_successfully' => 'Cart Clear Successfully',
	'group_added_successfully' => 'Group Added Successfully',
	'thank_you_for_purchasing' => 'Thank you for purchasing',
	'thank_you_for_regi' => 'Thank you for registering',
	'stay_with_us' => 'Stay with us',
	'your_login_information' => 'Your login information',
	'login_url' => 'Login url',
	'thanks' => 'Thanks',
	'add_user' => 'ADD USER',
	'general' => 'general',
	'info' => 'info',
	'profile_picture' => 'Profile Picture',
	'password' => 'Password',
	'language' => 'Language',
	'telephone_number' => 'Telephone number',
	'accept_terms_condition' => 'Accept Terms & Conditions',
	'i_accept_the' => 'I accept that',
	'red_values' => 'Red Values',
	'select_time' => 'Select time',
	'no_topic_added' => 'no topic added',
    'no_note_added' => 'no note added',
	'your_access_contact_your_therapist' => 'Your Access Limit For Treatment was Finished.Contact Your Therapist',
	'refresh' => 'Clear filters',
	'select_value' => 'Select value',
	'note_here' => 'Enter note here',
	'add_user_group' => 'Add User Group',
	'add_own_analysis' => 'Add your own analysis',
	'update_menu' => 'Update menu',
	'timezone' => 'TimeZone',
	'noUrl' => 'No URL Found!!',
	'package_view' => 'Saved Cart',
	'empty_package' => 'No Saved Cart Found!',
	'updated_profile' => 'Profile Updated Successfully',
	'enter_srteet' => 'Enter Street',
	'save_changes' => 'Save changes',
	'main_menu_setting' => 'Main menu setting',
	'add_new_analysis' => 'Add New Analysis',
	'treatment_in_given_time' => 'Already you have setup a treatment in given time',
	'cron_setup_successfully' => 'Cron Setup Successfully',
	'item_moved_to_trash' => 'Item moved to trash',
	'ttem_restored_successfully' => 'Item Restored Successfully',
	'confirm' => 'Confirm',
	'back_to_login' => 'Back To Login',
	'Select_Image' => 'SelectImage',
	'you_have_no_treatment_yet' => 'You have no treatment yet',
	'you_have_no_pdf_yet' => 'You don"t have a PDF yet',
	'Body' => 'body',
	'Mental' => 'Mentally',
	'Name' => 'Surname',
	'Value' => 'Value',
	'Update_Password' => 'Update password',
	'Snapshot_analysis' => 'Snapshot analysis',
	'ON' => 'ON',
	'enter_password_if_you_want_to_update' => 'enter password if you want to update',
	'Select_Pool' => 'Select pool',
	'Sub_Title' => 'Sub Title',
	'Body_Description' => 'Body description',
	'Body_Description_Image' => 'Body description image',
	'Mental_Description' => 'Mental description',
	'Body_Audio' => 'Body Audio',
	'Mental_Audio' => 'Mental Audio',
	'Add_Mental_Audio' => 'Add mental audio',
	'Price/Time' => 'Price/Time',
	'Min_Value' => 'Minimum value',
	'Max_Value' => 'Max value',
	'Upload_Body_Images' => 'Upload Body Images',
	'Upload_Mental_Images' => 'Upload Mental Images',
	'Upload_Body_Audio' => 'Upload_Body_Audio',
	'Upload_Mental_Audio' => 'Upload mental audio',
	'Add_Audio' => 'Add audio',
	'Upload_Audio' => 'Upload audio',
	'Add_Body_Audio' => 'Add body audio',
	'Back' => 'Beacon',
	'Are_you_sure?' => 'Notice',
	'warning' => 'Warning',
	'Yes_delete_it' => 'Yes, delete it!',
	'Your_file_has_been_deleted' => 'Your file has been deleted.',
	'Success' => 'Success!',
	'p_list' => 'PACKAGE LIST',
	'o_package' => 'Old Packages',
	'add_length_not' => 'Add Length no more 31 then Days',
	'old_Remotetreatmends' => 'Old Remote Treatments',
	'device_error' => 'Your simultaneous login limit has been reached. Please log out of a device.',
	'account_switch' => 'Successfully Switch',
	'user_basic_information' => 'User basic information',
	'basic_info_for' => 'Basic Information for :siteName and Calculation',
	'therapist_settings' => 'Therapist settings',
	'i_accept_the_terms_of_use' => 'I accept the Terms of Use',
	'general_setting_for' => 'General Setting for PDF',
	'user_login' => 'User login',
	'setting_for_user_login' => 'Setting for user login on :siteName',
	'never_share' => '"We will never share your email with anyone else"',
	'user_settings' => 'User settings',
	'setting_for_view' => 'Setting for View and Display',
	'cover_PDF' => 'Cover PDF',
	'red_marked_are_required' => 'Red Marked are required',
	'show_per_default' => 'Show the Button Dashboard option',
	'play_audio' => 'Music/Frequency playback on treatment',
	'old_carts' => 'Old carts',
	'pfd_text1' => 'We would like to point out that all our methods are not effective according to the scientific criteria of conventional medicine',
	'pfd_text2' => 'According to the scientific criteria of conventional medicine, the products can only have a placebo effect on the health and well-being of users.',
	'pfd_text3' => 'In case of illness or health problems, you should definitely seek the advice of your doctor.The use of our products should not cause you to interrupt or stop your doctors care.',
	'pfd_text4' => 'All tested parameters are an information and energy analysis, which was determined on the basis of the biorhythm. These results provide evidence of blockages and energy deficiencies and showin which areas we ourselves can and should contribute.',
	'pfd_text5' => 'The aim is not to replace the doctor or therapist, but this evaluation should contribute to actively contributing to one"s own well-being.',
	'pfd_text6' => ':siteName is a great way to combine it with all other methods.',
	'pfd_text7' => ':siteName scans the energy and information field of biological systems (human, animal,...) in just a few seconds and displays the current status. After the analysis, all tested parameters are corrected and optimized. :siteName uses frequencies determined for correction, thus strengthening the necessary information and vibrations,thereby optimizingbody, mind and soul. At the same time, the vibrations are stored on the carrier (magnetic card and bracelet)',
	'You_may_feel' => 'You may feel that the effect is slowing down after a while. In this case, it is advisable to create a new analysis and correction, or a new information carrier, as the needs or the blockages have changed.',
	'red_min' => 'Min Red Value',
	'red_max' => 'Max Red Value',
	'orange_min' => 'Min Orange Value',
	'orange_max' => 'Max Orange Value',
	'green_min' => 'Min Green value',
	'green_max' => 'Max Green value',
	'show_per_default_random' => 'All analysis values ​​are displayed at the moment.',
	'if_you_upload' => 'If you upload image it should be replaced',
	'youe_browser' => 'Your browser does not support the audio element.',
	'menu_locked' => 'Menu Locked',
	'menu_unlocked' => 'Menu Unlocked',
	'Add_New_Audio' => 'Add New Audio',
	'switch_button' => 'Display Switch for Pattern Analysis',
	'Edit_Group_Name' => 'Edit Group Name',
	'Efit_Causes' => 'Efit Causes',
	'title' => 'Title',
	'This_field_is_required' => 'This field is required',
	'Cause_Title' => 'Cause title',
	'Add_Group' => 'AddGroup',
	'Edit_Causes_Type' => 'Edit Causes Type',
	'gender' => 'Gender',
	'male' => 'times',
	'female' => 'Female',
	'other' => 'Other',
	'gendernotfound' => 'Gender not found',
	'add_new_member' => 'Add new member on',
	'save_session' => 'SaveSession',
	'open_session' => 'Open session',
	'save_pdf_reaction' => 'Save as PDF',
	'save_send_pdf' => 'Save PDF and Send',
	'previous' => 'Previous',
	'next' => 'Next',
	'f_to' => 'to',
	'f_of' => 'of',
	'send' => 'Send',
	'sl' => 'SL',
	'session_name' => 'Session Name',
	'pdf_name' => 'PDF name',
	'pdf_second_page_title' => 'PDF Second Page Design',
	'chose_menu' => 'Chose menu',
	'showing' => 'Showing',
	'create_pdf_send_mail' => 'Create PDF and send as email',
	'multiple_group' => 'Multiple Group Selected',
	'reactionValue' => 'Reaction value',
	'dashboardgo' => 'Go to Dashboard',
	'congratulationReactionMSG' => 'Congratulations! all values ​​are optimized for today.',
	'optimize' => 'Optimize',
	'digitalcon&focusPDF' => 'Digital Constellation & Energy Field PDF',
	'multigroupselect' => 'Multiple Group Selected',
	'selectedsubmenus' => 'Analysis Selected',
	'nothingselected' => 'Nothing Selected',
	'page' => 'page',
	'document' => 'Document',
	'selectbgimage' => 'Select Background Image',
	'groupselected' => 'Group Selected',
	'einfluss' => 'Influence',
	'focus_pdf' => 'Focus',
	'top9' => 'Top 9',
	'datasavedmsg' => 'Data Saved Successfully',
	'saveing' => 'Savings',
	'send&save' => 'Send and save',
	'showbasket' => 'Show Treatmend basket',
	'rancheckedmsg' => 'Random Analysis Active',
	'successfullychange' => 'Changed successfully',
	'nothingSelected' => 'Nothing Selected',
	'backgroundanimation' => 'Background animation',
	'time' => 'Time',
	'reactionattechment' => 'Reaction Attachment',
	'ownMenuSingle' => 'Own Menu List',
	'ownMenuCombo' => 'Own Combo Menu List',
	'ownMenuName' => 'Menu Name',
	'systemMenu' => 'All program',
	'ownMenu' => 'Own menu',
	'autosave' => 'automatic save',
	'randomimage' => 'Random Image Select',
	'defaultimage' => 'Default image',
	'enfluselect' => 'Nothing Selected',
	'breakupTitle' => 'Remote treatment ends!',
	'breakupTitle2' => 'Remote treatment activated!',
	'breakupContent' => 'The remote treatment has been completed successfully, no further emails will be sent and no further treatments will be carried out.',
	'breakupContent2' => 'Remote successfully activated and is being executed again.',
	'reactionvalue' => 'Reaction value',
	'loginactive' => 'You need to be active to log in.',
	'body_disc' => 'Description of the body',
	'analysisdate' => 'Analysis data',
	'session_error' => 'Delete all old sessions',
	'focus_cicrle_info' => 'Double click to use the <b>Marker</b>',
	'show_ra' => 'Without RA value',
	'show_ra_on' => 'Loading mode on',
	'show_ra_off' => 'Loading mode off',
	'show_pdf_on' => 'Show PDF for response value.',
	'show_pdf_off' => 'Hide PDF for response value.',
	'show_cron_pdf_ra' => 'Show Reaction PDF',
	'correct' => 'Correct',
	'information_tested' => 'Information is being tested',
	'info_test' => 'Info test',
	'payment_due_message' => 'Dear :siteName user!<br>
    <span class="text-warning">Energy follows attention.</span><br>
    Unfortunately, you are using our software without having a valid order. Since we also have costs for maintaining and maintaining this software and its server infrastructure, we would like to see you again as a customer.<br>
    An :siteName license / access is available from 99 € per year or for family customers from 49 € per month and can be ordered <a href="mailto:<EMAIL>">here</a>.<br>
    Best regards<br>
    Jürgen Bergauer, MSc',
	'payment_alert' => 'GRADE',
	'apiPayHead' => 'Hello: {$name}',
	'api_payment_due_message' => '<div class="card text-black mb-3">
            <div class="card-body">
                <h6>License / subscription expired</h6>
                <p class="card-text">
                Dear user!<br>
                Your subscription has expired, if you want to continue working with :siteName, please extend your subscription. </br>You can also extend your subscription online in our <a href="https://:siteName" target="_blank" rel="nofollow">shop</a>.
                </p>
                Best regards<br>
                :siteName team
                <p></p>
            </div>
        </div>',
	'beckToAdmin' => 'Back to Admin',
	'apiOrderTemplate' => '<h2>Hello {$NAME}! </h2><br>

        Your access to :siteName is already set up.<br>

        You can log in with the following data:<br>

        <pre>
        Username {$EMAIL}<br>
        Login URL: <a href="{$LOGIN_URL}">Click here</a><br>
        </pre><br>

        You can find help for our program at  <a href="https://:siteName/hilfe">Help<a><br>

        If you have any questions, please contact the :siteName support team at info@:siteName.<br>

        <mark>kind regards</mark><br>
        <strong>:siteName team</strong>',
	'privecypolicy' => 'Privacy policy',
	'accept' => 'Accept',
	'terms&condition' => 'Terms & Conditions',
	'gdpr_checkbox' => 'I accept the terms of use',
	'forhelpcontact' => 'For Help contact our support: info@:siteName',
	'finddocument' => 'You can find the full documentation on https://hilfe.:siteName',
	'fillthisfrom' => 'Please fill in this from',
	'headerContent' => 'so that we can analyze you perfectly, we still need some data from you!',
	'click' => 'Click',
	'check_me' => 'Check me out',
	'youraccessinfo' => 'your access data to',
	'email/username' => 'Username/email address',
	'send_pass_link_intro1' => 'You are receiving this email because we received a password reset request for your account.',
	'send_pass_link_intro2' => 'This password reset link will expire in 60 minutes.',
	'send_pass_link_intro3' => 'If you did not request a password reset, no further action is required.',
	'send_pass_link_regards' => 'Regards,',
	'send_pass_link_regards2' => ':siteName support team',
	'send_pass_link_btn' => 'Reset password',
	'Type' => 'Type',
	'Comboentrie' => 'Combo entry',
	'Singleentrie' => 'Single entry',
	'sortingno' => 'Sorting No.',
	'screenshot' => 'Screenshot',
	'save_ss' => 'Screenshot Saved',
	'upload_own_img' => 'Upload your own image',
	'delete_widget' => 'Widget delete',
	'save_session_msg' => 'Please enter session name',
	'save_sessionpdf_msg' => 'Please Enter PDF name and Select user',
	'save_sessionpdf2_msg' => 'Please Enter PDF Name, Email and Select User',
	'auto_direction_dc' => 'Auto Direction View',
	'image' => 'image',
	'own_image' => 'Own images',
	'failed_msg' => 'Failed',
	'success_msg' => 'Success',
	'ip_address' => 'IP Address',
	'log_desc' => 'Description',
	'log_module' => 'Modules',
	'log_status' => 'status',
	'log_response' => 'Response',
	'log_datetime' => 'Date & Time',
	'ei_note1' => 'Add Analysis ID in column ID if you want to update any analysis.',
	'ei_note2' => 'left column ID blank if you want to add/insert Analysis',
	'lta' => 'LTA',
	'lta_exit' => 'EXIT LTA',
	'lta_abbr' => 'Long term analyses',
	'lta_exit_abbr' => 'Exit Long Term Analysis',
	'lta_choose_days' => 'Choose Days',
	'lta_days' => 'days',
	'choose_days' => 'Please Choose Days',
	'remove_message' => 'Signed out everywhere!',
	'ask_message' => 'Signed out everywhere?',
	'signout' => 'Sign Out',
	'table_nodata' => 'No records available.',
	'table_first' => 'First',
	'table_back' => 'Beacon',
	'table_next' => 'Next',
	'table_latest' => 'Latest',
	'table_showdata' => 'Show entries',
	'table_norecord' => 'No data available in the table',
	'table_loading' => 'Loading...',
	'table_entries' => 'Entries',
	'table_pleasewait' => 'Please wait...',
	'trans_expo_impo' => 'Translation export/import',
	'trans_expo' => 'Translation Export',
	'trans_impo' => 'Translation Import',
	'processing' => 'Processing...',
	'reaction_switch_alrt' => 'You Want to Change Loading Mode!!',
	'reaction_switch_accept' => 'Loading Mode,Change!',
	'reaction_switch_alrt_pdf' => 'You Want to Change RA PDF Option!!',
	'reaction_switch_accept_pdf' => 'RA PDF Option,Change!',
	'austria' => 'Austria',
	'germany' => 'Germany',
	'switzerland' => 'Switzerland',
	'selected_analyses' => 'Selected Analyzes for',
	'cron_note_header' => 'straight',
	'cron_note_title' => 'Add a note for internal purposes',
	'cron_note_client_title' => 'Add a note for Client',
	'cron_theme_header' => 'Theme',
	'cron_theme_title' => 'Add topic that is calculated with',
	'general_setting_header' => 'General settings',
	'link_email_setting_header' => 'Link and email settings',
	'link_email_setting_title' => 'E-mail sending of the start e-mails and end e-mails (completed series)',
	'advanced_setting_header' => 'Advanced Settings',
	'advanced_setting_title' => 'View PDF for analysis',
	'add_cause_remedy_title' => 'Add cause, remedy, and tip',
	'apply_the_cal_to_all' => 'Apply the calculation to all following days',
	'no_fixed_cron_time_title' => 'Allow the client to open the treatment at any time on the same day',
	'action_value_setting_header' => 'Action value settings',
	'action_value_setting_title' => 'Switch off response value',
	'show_cron_pdf_ra_title' => 'View PDF for reaction value',
	'default_setting_header' => 'Default settings',
	'default_setting_title' => 'Here you can select the standard checkboxes that are created for a new long-distance transaction',
	'email_connection_setting_header' => 'Email connection settings',
	'special_setting_header' => 'Special settings',
	'standard_days' => 'Standard days',
	'start_email_template_remote_handle_header' => 'Start email template - remote handling',
	'end_email_template_remote_handle_header' => 'END Email Template - Remote Treatment',
	'red_day_email_template_remote_handle_header' => 'Red Day Email Template - Remote Treatment',
	'subjct_line_title' => 'Subject line',
	'pre_select_setting_header' => 'Preset setting',
	'pre_select_submenu_title' => 'Pre-select submenu',
	'submenus_not_found' => 'Submenus Not Found!!!',
	'per_select_days' => 'Pre-Select Days',
	'pre_select_time' => 'Pre-Select Start Time',
	'pre_time_select' => 'Select Time HH:MM',
	'entry_delete' => 'Delete Entry?',
	'dc_back' => 'Back view',
	'dc_front' => 'Front view',
	'dc_left' => 'Left',
	'dc_right' => 'Right',
	'dc_default' => 'default',
	'dc_top_left' => 'Top left',
	'dc_top_right' => 'Top right',
	'dc_bottom_left' => 'Bottom left',
	'dc_bottom_right' => 'Bottom right',
	'dc_select_icon' => 'Select icon',
	'dc_select_size' => 'Select size',
	'dc_enter_name' => 'Insert name',
	'dc_select_color' => 'Close Select',
	'dc_append' => 'Append',
	'dc_foot' => 'Foot',
	'dc_cone' => 'Cone',
	'dc_cylinder' => 'Cylinder',
	'dc_shape' => 'shape',
	'dc_switch_view' => 'Switch view',
	'dc_anchor_box' => 'Anchor Point',
	'dc_color' => 'Color selection',
	'small' => 'Small',
	'large' => 'Large',
	'rectangle' => 'Rectangle',
	'star' => 'star',
	'oval' => 'Oval',
	'all_red_values' => 'All Red Values ​​Will Add To Cart!',
	'to_treat_all_red' => 'To treat all red values!',
	'open_own_images' => 'Open Own Images',
	'image_upload_successfull' => 'Image uploaded successfully',
	'save_image' => 'Saved image',
	'save_upload' => 'Saved upload',
	'no_image_found' => 'No Image Found',
	'add_all_red_values_to_cart' => 'Add all red values ​​to cart.',
	'buy_now' => 'Buy now',
	'delete_all' => 'Delete All',
	'lta_body_gray' => 'Body Counted Gray Elements',
	'lta_body_red' => 'Body Counted Red Elements',
	'lta_heart_gray' => 'Heart Counted Gray Elements',
	'lta_heart_red' => 'Heart Counted Red Elements',
	'lta_red_text' => 'Red Count',
	'lta_green_text' => 'Green Count',
	'lta_mm' => 'M',
	'lta_yy' => 'Y',
	'lta_ob' => 'O',
	'lta_ll' => 'L',
	'lta_ob_abbr' => 'Observer Effect (Moment Analysis)',
	'lta_ll_abbr' => 'Long-term analysis',
	'subscription_warning' => '<div class="card text-black mb-3">
            <div class="card-body">
                <h4 class="card-title text-danger text-bold"><i>Note</i></h4>
                <h6>License/subscription expired</h6>
                <p class="card-text">
                    Hello dear user!<br>
                    Your subscription has expired, if you would like to continue working with :siteName, please extend your subscription. You can also extend your subscription online in our <a href="https://www.:siteName/shop/" target="_blank" rel="nofollow">Shop</a>.
                </p>
                <p>
                Kind regards<br>
                :siteName team</p>
            </div>
        </div>',
	'no_email_find_error' => 'We can find any user with this email',
	'smtp_customize' => 'Customize',
	'smtp_not_set_msg' => 'System SMTP Applied',
	'smtp_alert_for_not_therapist' => 'Only Therapists Users can setup their own SMTP details!',
	'change_image' => 'ReplaceImage',
	'email_template_setting' => 'Email template',
	'resend_email_template' => 'Resend email template',
	'resend_email_template_content_title' => 'Change send email template',
	'resend_email' => 'Resend email',
	'smtp_header' => 'SMTP headers',
	'smtp_setting_title' => 'SMTP setting',
	'all_orange_values' => 'All Orange Values ​​Will Add To Cart!',
	'add_all_orange_values_to_cart' => 'Add all orange values ​​to cart.',
	'all_green_values' => 'All Green Values ​​Will Add To Cart!',
	'add_all_green_values_to_cart' => 'Add all green values ​​to cart.',
	'user_type_title' => 'User type',
	'user_type_1' => 'person',
	'first_name_1' => 'first name',
	'first_name_2' => 'Company type',
	'first_name_3' => 'Animal type',
	'first_name_4' => 'House type',
	'first_name_5' => 'Object',
	'user_type_2' => 'Company',
	'last_name_1' => 'LastName',
	'last_name_2' => 'Company Name',
	'last_name_3' => 'Animal Name',
	'last_name_4' => 'Village',
	'last_name_5' => 'Surname',
	'user_type_3' => 'Animal',
	'birthday_1' => 'Birthday',
	'birthday_2' => 'Establish date',
	'birthday_3' => 'Birthday',
	'birthday_4' => 'Construction date',
	'user_type_4' => 'House',
	'user_type_5' => 'Thing',
	'place_of_birth_1' => 'Village of Birth',
	'place_of_birth_2' => 'Place of Foundation',
	'place_of_birth_3' => 'Village of Birth',
	'place_of_birth_4' => 'Location',
	'date_dob' => 'YRS',
	'cart_package_content' => 'Cart package content',
	'pdf_view' => 'PDF view',
	'email_template' => 'Email template',
	'sajib' => 'sajib',
	'animal_type_error' => 'Please fill animal type',
	'animal_name_error' => 'Please fill animal name',
	'company_type_error' => 'Please fill company type',
	'company_name_error' => 'Please fill company name',
	'company_establishment_error' => 'Please enter establishment date',
	'house_type_error' => 'Please fill house type',
	'house_name_error' => 'Please fill village name',
	'house_construction_error' => 'Please enter construction date',
	'cart_max_allow_alert' => 'Maximum cart allowed 200.',
	'datatable_to' => 'to',
	'datatable_show' => 'show',
	'datatable_filtered_result' => '(filtered from _MAX_ total entries)',
	'error_for_max_user_expire' => 'Unfortunately, this function is not available. (Possible causes: maximum number of users reached, downgrade from your account). If you have any questions about your limitations, please contact our support.',
	'already_have_treatment_alert' => 'You already have remote treatment for this selected DateTime',
	'change' => 'Change',
	'change_need_message' => 'User Language not same as user setting!!',
	'timeChangeAlert' => 'According to UTC Setting Minimum Time:',
	'sure_alert' => 'Are you sure?',
	'ss_not_store_alert' => 'Are you sure to leave the page without storing the screenshots?',
	'uploaded_pdf_logo' => 'Uploaded PDF logo',
	'auto_menu_hide' => 'Menu auto hide updated successfully',
	'msg_menu_auto_hide' => 'Menu Auto Hide',
	'create_pdf' => 'Create PDF Now',
	'click_for_link' => 'Click here for open terms',
	'play_treatment' => 'Play treatment',
	'treatment_list' => 'Your treatment list',
	'view_pdf' => 'View PDF',
	'therapist_info' => 'Therapist information',
	'dash_chart_top_left' => 'Health',
	'dash_chart_top_right' => 'Vocation',
	'dash_chart_bottom_left' => 'Relationship',
	'dash_chart_bottom_right' => 'Finance',
	'dash_progress_name_one' => 'Spirit level',
	'dash_progress_name_two' => 'Emotional level',
	'dash_progress_name_three' => 'Mental level',
	'dash_progress_name_four' => 'Biochemistry levels',
	'dash_progress_name_five' => 'Body Level',
	'dash_right_progress' => 'Vitality, self-healing, detoxification, immune system, fitness, relaxation',
	'less' => 'Less',
	'more' => 'More',
	'note_tag' => 'Note',
	'customer_note' => 'Customer Note',
	'dislike_feedback_massage' => 'Please write here why feeling bad?',
	'share_cron' => 'Share',
	'feedback' => 'Feedback',
	'feedback_from' => 'From',
	'feedback_url' => 'Click Here To Open URL',
	'session_id_text' => 'Current Session ID',
	'green_day_email_template_remote_handle_header' => 'Green Day Email Template - Remote Treatment',
	'cron_green_placeholder' => 'Change the Green of the e-mail template',
	'cron_red_day' => 'Red Day',
	'cron_green_day' => 'Green day',
	'cron_tutorial' => 'Tutorial',
	'end_email_the_therapist' => 'End mail for Therapist',
	'action.close_dc_infotest' => 'Close',
	'close_dc_infotest' => 'Close',
	'email_subscription' => 'Email Subscription',
	'frequency_placeholder' => '250-10,000 Hz',
	'seconds_placeholder' => '5-3600 seconds',
	'invalid_time' => 'Invalid time. Please enter a time between 5 and 3600 seconds.',
	'frequency_time_both_required' => 'Both frequency and time must be provided together, or leave both empty.',
	'unit_hertz' => 'Hz',
	'unit_seconds' => 'sc',
	'calculate_harmonics' => 'Calculate harmonics',
	'frequency_generator' => 'Frequency Generator',
	'frequency_too_low' => 'Frequency too low! Click to fix (minimum 250 Hz)',
	'calculating_frequency' => 'Calculating frequency',
	'cart_add_failed' => 'Failed to add to cart',
	'cart_service_unavailable' => 'Cart service unavailable',
	'topic_save_failed' => 'Failed to save topic',
	'topic_delete_failed' => 'Failed to delete topic',
	'topic_deleted_successfully' => 'Topic deleted successfully',
	'too_many_cart_additions' => 'Too many cart additions. Try again in :seconds seconds.',
	'frequency_calculation_failed' => 'Frequency calculation failed',
	'min_less_than_max' => 'Minimum value must be less than maximum value',
	'max_greater_than_min' => 'Maximum value must be greater than minimum value',
    'missing_fields' => 'Please complete the menu settings first before using this feature.',
    'missing_fields_title' => 'Missing Fields',
    'complete_menu_settings' => 'Complete own menu settings',
    'auto_saving_settings' => 'Your settings are being saved automatically...',
    'auto_saving' => 'Auto Save',
    'settings_being_saved' => 'Your settings are being saved...',
    'default_values_notice' => 'Default Values:',
    'please_customize_ranges' => 'Please customize these ranges according to your needs.',
    'default_values' => 'DEFAULT',
	'analyses' => 'Analyses',
	'no_analyses' => 'No analyses found',
	'modal-analyses-count-message' => 'Analyses selected',
	'max_time' => 'Max Time',
	'edit_hour' => 'Edit Treatment Hour',
	'edit_minute' => 'Edit Treatment Minute',
	'edit_second' => 'Edit Treatment Second',
	'hearable_area' => 'Hearable Area: 250 - 10,000 Hz',
	'video_player' => 'Play Video',
	'item_not_found' => 'Item not found',
];