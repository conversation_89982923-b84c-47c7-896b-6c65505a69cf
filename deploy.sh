#!/bin/bash

# Load user environment to ensure PATH includes Go and other installed tools
source ~/.bashrc 2>/dev/null || source ~/.profile 2>/dev/null || true

# Ensure Go is in PATH
export PATH=$PATH:/usr/local/go/bin:/usr/bin:/snap/bin

# Exit immediately if a command fails
set -e

# Define variables
REPO="https://enfit:$<EMAIL>/enfit/start.git"
APP_DIR="$(pwd)"  # Automatically set to the current directory
USER="$(whoami)"  # Automatically set to the current user
BRANCH="main"  # Change if using a different branch

echo "Starting Laravel deployment..."

# Enable Laravel maintenance mode
echo "Enabling maintenance mode..."
php artisan down || true  # If it's already down, continue

# Get the current branch from the local repository
CURRENT_BRANCH=$(git symbolic-ref --short HEAD)
echo "Using current branch: $CURRENT_BRANCH"

# Fetch the latest changes
echo "Pulling latest changes from Gitea..."
git pull $REPO $CURRENT_BRANCH

# Set correct permissions (avoiding permission issues)
echo "Setting correct permissions..."
chown -R $USER:$USER $APP_DIR
chmod -R ug+rwx $APP_DIR/storage $APP_DIR/bootstrap/cache

# Install/update dependencies
echo "Installing composer dependencies..."
composer install
# composer install --no-dev --prefer-dist --optimize-autoloader

# Run database migrations
echo "Running database migrations..."
php artisan migrate --force

# Install NPM dependencies and build assets
echo "Installing NPM dependencies and building assets..."
npm ci
npm run build

# Clear cache and optimize
echo "Clearing cache and optimizing application..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Restart queue workers
echo "Restarting queue workers..."
supervisorctl restart all

# Reload Supervisor
echo "Reloading Supervisor..."
supervisorctl reload

# Clear Opcache
chmod +x cachetool.phar
./cachetool.phar opcache:reset --fcgi=127.0.0.1:18001

# Run Go build script
echo "Building Go calculators..."
cd app/Services/Go
sh build.sh
cd $APP_DIR

# Disable Laravel maintenance mode
echo "Disabling maintenance mode..."
php artisan up

echo "Laravel deployment completed successfully! 🎉"
