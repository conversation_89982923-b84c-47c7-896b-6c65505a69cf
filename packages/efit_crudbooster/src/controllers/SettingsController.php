<?php namespace crocodicstudio\crudbooster\controllers;

use CRUDBooster;
use App\Enums\ActionType;
use App\Model\Dashboards\V2Dashboard;
use App\Model\AllSingleAction;
use App\Enums\ModuleDashboardBox;
use Illuminate\Support\Facades\DB;
use App\Model\DashboardWidgetSetting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;
use App\Traits\PoolAnalysesGeneralFunctions;
use App\Services\AdminSettings\AdminSettingsService;

class SettingsController extends CBController
{
    use PoolAnalysesGeneralFunctions;
    private $adminSettingsService;

    public function __construct(AdminSettingsService $adminSettingsService)
    {
        $this->adminSettingsService = $adminSettingsService;
    }

    public function cbInit()
    {
        $this->module_name = "Settings";
        $this->table = 'cms_settings';
        $this->primary_key = 'id';
        $this->title_field = "name";
        $this->index_orderby = ['name' => 'asc'];
        $this->button_delete = true;
        $this->button_show = false;
        $this->button_cancel = false;
        $this->button_import = false;
        $this->button_export = false;

        $this->col = [];
        $this->col[] = ["label" => "Nama", "name" => "name", "callback_php" => "ucwords(str_replace('_',' ',%field%))"];
        $this->col[] = ["label" => "Setting", "name" => "content"];

        $this->form = [];

        if (Request::get('group_setting')) {
            $value = Request::get('group_setting');
        } else {
            $value = 'General Setting';
        }

        $this->form[] = ['label' => 'Group', 'name' => 'group_setting', 'value' => $value];
        $this->form[] = ['label' => 'Label', 'name' => 'label'];

        $this->form[] = [
            "label" => "Type",
            "name" => "content_input_type",
            "type" => "select",
            "dataenum" => ["text", "number", "email", "textarea", "wysiwyg", "upload_image", "upload_document", "datepicker", "radio", "select"],
        ];
        $this->form[] = [
            "label" => "Radio / Select Data",
            "name" => "dataenum",
            "placeholder" => "Example : abc,def,ghi",
            "jquery" => "
			function show_radio_data() {
				var cit = $('#content_input_type').val();
				if(cit == 'radio' || cit == 'select') {
					$('#form-group-dataenum').show();	
				}else{
					$('#form-group-dataenum').hide();
				}					
			}
			$('#content_input_type').change(show_radio_data);
			show_radio_data();
			",
        ];
        $this->form[] = ["label" => "Helper Text", "name" => "helper", "type" => "text"];
    }

    function getShow()
    {
        $this->cbLoader();

        if (! CRUDBooster::isSuperadmin()) {
            CRUDBooster::insertLog(cbLang("log_try_view", ['name' => 'Setting', 'module' => 'Setting']));
            CRUDBooster::redirect(CRUDBooster::adminPath(), cbLang('denied_access'));
        }

        $data['page_title'] = urldecode(Request::get('group'));
        $data['pools'] = DB::table('pools')->select('pool_name', 'id')->get();
        $data['widgetSettings'] = DashboardWidgetSetting::with([
            'pool:id',
            'pool.analyses' => function ($query) {
                $query->orderBy('id', 'asc')->take(7);
            },
            'barChartPool:id',
            'barChartPool.analyses' => function ($query) {
                $query->orderBy('id', 'asc')->take(7);
            }
        ])->first();

        $data['analyses'] = $this->processAnalysisData($data['widgetSettings']);
        $data['isActiveNewDashboard'] = AllSingleAction::getFlag(ActionType::NEW_DASHBOARD_V2_ACTIVE);
        $data['isActiveGoMode'] = AllSingleAction::getFlag(ActionType::V3_ACTIVATE_GO_MODE);
        $data['isActiveGeneralGoMode'] = AllSingleAction::getFlag(ActionType::ACTIVATE_GENERAL_GO_MODE);
        $data['isActiveNewDashboardProductView'] = AllSingleAction::getFlag(ActionType::NEW_DASHBOARD_PRODUCT_VIEW);

        return view('crudbooster::setting', $data);
    }

    function hook_before_edit(&$posdata, $id)
    {
        $this->return_url = CRUDBooster::mainpath("show")."?group=".$posdata['group_setting'];
    }

    private function removeCache(){
         
        if(Cache::has('brandLogo')){  
            Cache::forget('brandLogo');
        }
        if(Cache::has('brandName')){  
            Cache::forget('brandName');
        }
        if(Cache::has('brandFavicon')){  
            Cache::forget('brandFavicon'); 
        }
    }

    function getDeleteFileSetting()
    {
        $id = g('id');
        $row = CRUDBooster::first('cms_settings', $id);
        Cache::forget('setting_'.$row->name);
        if (Storage::exists($row->content)) {
            Storage::delete($row->content);
        }
        DB::table('cms_settings')->where('id', $id)->update(['content' => null]);
        CRUDBooster::redirect(Request::server('HTTP_REFERER'), cbLang('alert_delete_data_success'), 'success');
    }

    function postSaveSetting()
    {

        if (! CRUDBooster::isSuperadmin()) {
            CRUDBooster::insertLog(cbLang("log_try_view", ['name' => 'Setting', 'module' => 'Setting']));
            CRUDBooster::redirect(CRUDBooster::adminPath(), cbLang('denied_access'));
        }

        $group = Request::get('group_setting');
        $setting = DB::table('cms_settings')->where('group_setting', $group)->get();

        foreach ($setting as $set) {

            $name = $set->name;

            $content = Request::get($set->name);

            if (Request::hasFile($name)) {

                if ($set->content_input_type == 'upload_image') {
                    CRUDBooster::valid([$name => 'image|max:10000'], 'view');
                } else {
                    CRUDBooster::valid([$name => 'mimes:doc,docx,xls,xlsx,ppt,pptx,pdf,zip,rar|max:20000'], 'view');
                }

                $file = Request::file($name);
                $ext = $file->getClientOriginalExtension();

                //Create Directory Monthly
                $directory = 'uploads/'.date('Y-m');
                Storage::makeDirectory($directory);

                //Move file to storage
                $filename = md5(str_random(5)).'.'.$ext;
                $storeFile = Storage::putFileAs($directory, $file, $filename);
                if ($storeFile) {
                    $content = $directory.'/'.$filename;
                }
            }

            DB::table('cms_settings')->where('name', $set->name)->update(['content' => $content]);

            Cache::forget('setting_'.$set->name);

            if(Cache::has($set->name)){ 
                Cache::put($set->name, $content);
            }

        }
        // Update Diagram Type and pool type create new
        if (request()->diagram_type && request()->pool_type ||  request()->pool_for_chart) {
           $this->adminSettingsService->handelUpdateOrCreateDiagramSettings(request()->all());
        }

        // Save V2 Dashboard flag status
        AllSingleAction::updateFlag(
            ActionType::NEW_DASHBOARD_V2_ACTIVE,
            request()->active_dashboard == 'on'
        );
        // Save V3 Activate GO Mode flag status
        AllSingleAction::updateFlag(
            ActionType::V3_ACTIVATE_GO_MODE,
            request()->v3_activate_go_mode == 'on'
        );

        // Save General Go Mode flag status
        AllSingleAction::updateFlag(
            ActionType::ACTIVATE_GENERAL_GO_MODE,
            request()->activate_general_go_mode == 'on'
        );

        //save new Dashboard product view flag status
        AllSingleAction::updateFlag(
            ActionType::NEW_DASHBOARD_PRODUCT_VIEW,
            request()->active_dashboard_product_view == 'on'
        );

        $this->removeCache();
        return redirect()->back()->with(['message' => 'Your setting has been saved !', 'message_type' => 'success']);
    }

    function hook_before_add(&$arr)
    {
        $arr['name'] = str_slug($arr['label'], '_');
        $this->return_url = CRUDBooster::mainpath("show")."?group=".$arr['group_setting'];
    }

    function hook_after_edit($id)
    {
        $row = DB::table($this->table)->where($this->primary_key, $id)->first();

        /* REMOVE CACHE */
        Cache::forget('setting_'.$row->name);
    }
                       
    public function postDeleteWidgetSetting()
    {
        $id = request()->id;
        DB::table('dashboard_widget_settings')->where('id', $id)->delete();
        cache()->forget('dashboard_widget_setting_exists');
    }
    
    public function postSortingDiagram(){
        return $this->adminSettingsService->sortingDiagram(request()->all());
    }

    private function processAnalysisData($widgetSettings): array
    {
        return [
            'widgetAnalyses' => $this->processDiagram($widgetSettings->sorted_diagram, $widgetSettings->pool),
            'barChartAnalyses' => $this->processDiagram($widgetSettings->bar_sorted_diagram, $widgetSettings->barChartPool),
        ];
    }

    public function getV2DashboardSettings(){

        $pools = DB::table('pools')->select('pool_name', 'id')->get();
        // Define diagram types
        $diagramTypes = ['polarArea', 'radar', 'bar', 'progress'];
        // Create an instance of ModuleHeadDashboard
        $moduleHeadDashboard = new V2Dashboard();
        // Fetch the contents
        $chakraContent = $moduleHeadDashboard->chakraContent();
        $rightContent1 = $moduleHeadDashboard->rightContent1();
        $rightContent2 = $moduleHeadDashboard->rightContent2();

        return view('Admin.partials.dashboard-section-settings', compact('pools', 'diagramTypes', 'chakraContent', 'rightContent1', 'rightContent2'));
    }

    public function postV2DashboardSettings(){
        return $this->adminSettingsService->saveDashboardSettings(request(), V2Dashboard::class);
    }

    public function postDeleteV2DashboardSettings(){
        return $this->adminSettingsService->deleteDashboardSettings(request(), V2Dashboard::class);
    }

    public function getSortingModal()
    {
        return $this->adminSettingsService->getSortingModalBody(request(), V2Dashboard::class);
    }

    public function postV2DashboardPoolSorting()
    {
        return $this->adminSettingsService->updatePoolAnalaysesSorting(request(), V2Dashboard::class);
    }
}
