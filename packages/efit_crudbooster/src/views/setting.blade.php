@extends('crudbooster::admin_template')
@section('content')
@push('bottom')
<script src="{{asset('vendor/laravel-filemanager/js/lfm.js')}}"></script>
<script src="//cdn.tinymce.com/4/tinymce.min.js"></script>
<!-- Select2 CSS -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
<!-- Select2 JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<style>
    .pool-type-input span.select2 {
        width: 90% !important;
    }

    .pgr {
        position: relative;
    }

    .pgr button {
        display: inline-block;
        width: 40px;
    }

    .pgr .form-group.pool-type-input {
        display: inline-block;
        width: calc(100% - 60px);
    }
    .active-new-dashboard {
        position: relative;
        display: flex;
        justify-content: start;
        gap: 5px;
        align-items: center;
        margin: 0px auto 15px;
        padding: 20px;
        border-radius: 5px;
        background-color: #ededed;
    }
    
    .active-new-dashboard label {
      margin: 0px;
    }
    
    .active-new-dashboard input {
      margin: 0px;
    }

    .purpose-server-box {
        border: 1px solid #ccc;
        padding: 20px;
        border-radius: 8px;
    }

    .purpose-server-box > label {
        font-size: 20px;
        margin-bottom: 15px;
    }
</style>
<script>
    $(function () {
        $('.label-setting').hover(function () {
            $(this).find('a').css("visibility", "visible");
        }, function () {
            $(this).find('a').css("visibility", "hidden");
        })
    })
    var editor_config = {
        path_absolute: "{{asset('/')}}",
        selector: ".wysiwyg",
        height: 250,
        {{ ($disabled)?"readonly:1,":"" }}
        plugins: [
            "advlist autolink lists link image charmap print preview hr anchor pagebreak",
            "searchreplace wordcount visualblocks visualchars code fullscreen",
            "insertdatetime media nonbreaking save table contextmenu directionality",
            "emoticons template paste textcolor colorpicker textpattern"
        ],
        toolbar: "insertfile undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image media",
        relative_urls: false,
        file_browser_callback: function (field_name, url, type, win) {
            var x = window.innerWidth || document.documentElement.clientWidth || document.getElementsByTagName('body')[0].clientWidth;
            var y = window.innerHeight || document.documentElement.clientHeight || document.getElementsByTagName('body')[0].clientHeight;

            var cmsURL = editor_config.path_absolute + 'laravel-filemanager?field_name=' + field_name;
            if (type == 'image') {
                cmsURL = cmsURL + "&type=Images";
            } else {
                cmsURL = cmsURL + "&type=Files";
            }

            tinyMCE.activeEditor.windowManager.open({
                file: cmsURL,
                title: 'Filemanager',
                width: x * 0.8,
                height: y * 0.8,
                resizable: "yes",
                close_previous: "no"
            });
        }
    };

    tinymce.init(editor_config);

    
    $(document).ready(function() {
        $('#poolType, #diagramType, #poolForChart').select2();
        $('#diagramType').val() == null ? $('#poolType').prop('disabled', true) : $('#poolType').prop('disabled', false);
        $('#diagramType').change(function () {
            if ($(this).val() !== '') {
              // Enable the poolType select box
              $('#poolType').prop('disabled', false);
            }
        });
    });
    
    function confirmDelete(event, id) {
        // Prevent the default form submit action
        event.preventDefault();
        swal({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        }, function (isConfirm) {
            if (isConfirm) {
                $.ajax({
                    url: "{{ CRUDBooster::mainpath('delete-widget-setting') }}",
                    method: 'POST',
                    data: {
                        id: id,
                        _token: "{{ csrf_token() }}"
                    },
                    success: function (response) {
                        swal("Deleted!", "Your Diagram seetings has been deleted.", "success");
                        location.reload();
                    }
                });
            }
        });
    }

    $(document).ready(function () {
        $('#diagram-sort, #diagram-sort-barChartAnalyses').sortable({
            update: function (event, ui) {
              // Get the sorted list
                var sortedList = $(this).sortable('toArray', { attribute: 'data-name' });
                var sortedIdList = $(this).sortable('toArray', { attribute: 'data-id' });
                
                // Get the data-identify value from the first sorted item (assuming there is at least one item)
                var identify = $(ui.item[0]).data("identify");
                
                // Send the sorted list to the backend using AJAX
                $.ajax({
                    url: "{{CRUDBooster::mainpath('sorting-diagram')}}",
                    method: 'POST',
                    data: {
                        sortedList: JSON.stringify(sortedList),
                        sortedIdList: JSON.stringify(sortedIdList),
                        identify: identify,
                    },
                    success: function (res) {
                        if (res.success) {
                            swal('Success!', 'Successfully changed sorting', 'success');
                        } else {
                            swal('Oops', 'Something went wrong, try again', 'warning');
                        }
                    },
                    error: function (err) {
                        console.error(err);
                        swal('Error', 'Failed to send data to the server', 'error');
                    }
                });
            }
        });
    });

    function v2DashboardSettings(){
        $.ajax({
            url: "{{ CRUDBooster::mainpath('v2-dashboard-settings') }}",
            type: 'GET',
            success: function (response) {
                $('#v2DashboardSettings .modal-body').empty().html(response);
                $('#v2DashboardSettings').modal('show');
            },
            error: function (xhr, status, error) {
                console.error(xhr.responseText);
            }
        });
    }

    $("#submitV2DashboardSettingsForm").on('click',function(e){
        e.preventDefault();
        // Create FormData object
        var formData = new FormData(document.getElementById("formV2DashboardSettings"));
        // Submit form data using AJAX
        $.ajax({
            type: 'POST',
            url: $('#formV2DashboardSettings').attr('action'),
            data: formData,
            processData: false, 
            contentType: false,
            success: function(response) {
                if(response.success == true){
                    swal({ title:'Success!',text: response.message,type: 'success',timer: 1500 })
                    $('#v2DashboardSettings').modal('hide');
                }else{
                    swal({ title:'warning!',text: response.message,type: 'warning',  })
                    $('#v2DashboardSettings').modal('hide');
                }
            },
            error: function(xhr, status, error) {
                // Handle error response
                console.error('Error:', error);
                swal({ title:'Warning!', text: error,type: 'warning',timer: 1500})
            }
        });
    });

    function deleteModuleHeadContent(id,box_type){
        $.ajax({
            type: 'POST',
            url: "{{ CRUDBooster::mainpath('delete-v2-dashboard-settings') }}",
            data: {id: id, box_type: box_type},
            success: function(response) {
                if(response.success == true){
                    swal({ title:'Success!',text: response.message,type: 'success',timer: 1500 })
                    $('#v2DashboardSettings').modal('hide');
                }else{
                    swal({ title:'warning!',text: response.message,type: 'warning',  })
                    $('#v2DashboardSettings').modal('hide');
                }
            },
            error: function(xhr, status, error) {
                // Handle error response
                console.error('Error:', error);
                swal({ title:'Warning!', text: error,type: 'warning',timer: 1500})
            }
        });
    }


    function getSortingModal(id,){
        $.ajax({
            url: "{{ CRUDBooster::mainpath('sorting-modal') }}",
            type: "GET",
            data: { id: id},
            success: function(response) {
                $('#getSortingModal .modal-body').empty().html(response);
                $('#getSortingModal').modal('show');
                initializeSorting();
            },
            error: function(xhr) {
                // Handle errors
                console.error(xhr);
            }
        });
    }

    // Function to initialize sorting
    function initializeSorting() {
        $('#module-head-pool-sort').sortable({
            update: function(event, ui) {
                let sortedList = $(this).sortable('toArray', { attribute: 'data-name' });
                let sortedIdList = $(this).sortable('toArray', { attribute: 'data-id' });
                var id = $(this).data('module-head-id');
                $.ajax({
                    url: "{{ CRUDBooster::mainpath('v2-dashboard-pool-sorting') }}",
                    type: 'POST',
                    data: {
                        id: id,
                        sortedList: sortedList,
                        sortedIdList: sortedIdList,
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(res) {
                        if (res.status) {
                            swal('Success!', 'Successfully changed sorting', 'success');
                        } else {
                            swal('Oops', 'Something went wrong, try again', 'warning');
                        }
                    },
                    error: function(err) {
                        console.error(err);
                        swal('Error', 'Failed to send data to the server', 'error');
                    }
                });
            }
        });
    }
</script>
@endpush

<div style=" ">
    <p align="right">
        <a title='Add Field Setting' class='btn btn-sm btn-primary'
            href='{{route("SettingsControllerGetAdd")."?group_setting=".$page_title}}'>
            <i class='fa fa-plus'></i>
            Add Field Setting
        </a>
    </p>
    <div class="panel panel-default">
        <div class="panel-heading">
            <i class='fa fa-cog'></i> {{$page_title}}
        </div>
        <div class="panel-body">
            <form method='post' id="form" enctype="multipart/form-data"
                action='{{CRUDBooster::mainpath("save-setting?group_setting=$page_title")}}'>
                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                <input type="hidden" name="widget_settings_id" value="{{$widgetSettings->id}}">
                <div class="box-body">
                    <?php
                        $set = DB::table('cms_settings')->where('group_setting', $page_title)->get();
                        foreach($set as $s):

                        $value = $s->content;

                        if (! $s->label) {
                            $label = ucwords(str_replace('_', ' ', $s->name));
                            DB::table('cms_settings')->where('id', $s->id)->update(['label' => $label]);
                            $s->label = $label;
                        }

                        $dataenum = explode(',', $s->dataenum);
                        if ($dataenum) {
                            array_walk($dataenum, 'trim');
                        }

                        ?>
                    <div class='form-group'>
                        <label class='label-setting' title="{{$s->name}}">{{$s->label}}
                            <a style="visibility:hidden" href='{{CRUDBooster::mainpath("edit/$s->id")}}'
                                title='Edit This Meta Setting' class='btn btn-box-tool'><i class='fa fa-pencil'></i></a>
                            <a style="visibility:hidden" href='javascript:;' title='Delete this Setting'
                                class='btn btn-box-tool'
                                onClick='swal({   title: "Are you sure?",   text: "You will not be able to recover {{$s->label}} and may be can cause some errors on your system !",   type: "warning",   showCancelButton: true,   confirmButtonColor: "#DD6B55",   confirmButtonText: "Yes, delete it!",   closeOnConfirm: false }, function(){  location.href="{{CRUDBooster::mainpath("delete/$s->id")}}" });'><i
                                    class='fa fa-trash'></i></a>
                        </label>
                        <?php
                            switch ($s->content_input_type) {
                                case 'text':
                                    echo "<input type='text' class='form-control' name='$s->name' value='$value'/>";
                                    break;
                                case 'number':
                                    echo "<input type='number' class='form-control' name='$s->name' value='$value'/>";
                                    break;
                                case 'email':
                                    echo "<input type='email' class='form-control' name='$s->name' value='$value'/>";
                                    break;
                                case 'textarea':
                                    echo "<textarea name='$s->name' class='form-control'>$value</textarea>";
                                    break;
                                case 'wysiwyg':
                                    echo "<textarea name='$s->name' class='form-control wysiwyg'>$value</textarea>";
                                    break;
                                case 'upload':
                                case 'upload_image':
                                    if ($value) {
                                        echo "<p><a href='".asset($value)."' target='_blank' title='Download the file of $s->label'><i class='fa fa-download'></i> Download the File  of $s->label</a></p>";
                                        echo "<input type='hidden' name='$s->name' value='$value'/>";
                                        echo "<div class='pull-right'><a class='btn btn-danger btn-xs' onclick='if(confirm(\"Are you sure want to delete ?\")) location.href=\"".CRUDBooster::mainpath("delete-file-setting?id=$s->id")."\"' title='Click here to delete'><i class='fa fa-trash'></i></a></div>";
                                    } else {
                                        echo "<input type='file' name='$s->name' class='form-control'/>";
                                    }
                                    echo "<div class='help-block'>File support only jpg,png,gif, Max 10 MB</div>";
                                    break;
                                case 'upload_file':
                                    if ($value) {
                                        echo "<p><a href='".asset($value)."' target='_blank' title='Download the file of $s->label'><i class='fa fa-download'></i> Download the File  of $s->label</a></p>";
                                        echo "<input type='hidden' name='$s->name' value='$value'/>";
                                        echo "<div class='pull-right'><a class='btn btn-danger btn-xs' onclick='if(confirm(\"Are you sure want to delete ?\")) location.href=\"".CRUDBooster::mainpath("delete-file-setting?id=$s->id")."\"' title='Click here to delete'><i class='fa fa-trash'></i></a></div>";
                                    } else {
                                        echo "<input type='file' name='$s->name' class='form-control'/>";
                                    }
                                    echo "<div class='help-block'>File support only doc,docx,xls,xlsx,ppt,pptx,pdf,zip,rar, Max 20 MB</div>";
                                    break;
                                case 'datepicker':
                                    echo "<input type='text' class='datepicker form-control' name='$s->name' value='$value'/>";
                                    break;
                                case 'radio':
                                    if ($dataenum):
                                        echo "<br/>";
                                        foreach ($dataenum as $enum) {
                                            $checked = ($enum == $value) ? "checked" : "";
                                            echo "<label class='radio-inline'>";
                                            echo "<input type='radio' name='".$s->name."' value='$enum' $checked > $enum";
                                            echo "</label>";
                                        }
                                    endif;
                                    break;
                                case 'select':
                                    echo "<select name='$s->name' class='form-control'><option value=''>** Please select $s->label</option>";
                                    if ($dataenum):
                                        foreach ($dataenum as $enum) {
                                            $selected = ($enum == $value) ? "selected" : "";
                                            echo "<option $selected value='$enum'>$enum</option>";
                                        }
                                    endif;
                                    echo "</select>";
                                    break;
                            }
                            ?>
                        <div class='help-block'>{{$s->helper}}</div>
                    </div>
                    <?php endforeach;?>
                    @if (Request::input('group') === 'Application Setting')
                    <div class="active-new-dashboard">
                        <input type="checkbox" id="active-new-dashboard" name="active_dashboard" @if($isActiveNewDashboard == true) checked @endif >
                        <label for="vehicle3"> Activate New Dashboard V2</label>
                        <button type="button" class="btn btn-warning btn-sm" onclick="v2DashboardSettings()">
                            <i class="fa fa-cog"></i>
                            Setup Dashboard
                        </button>
                    </div>
                    <div class="active-new-dashboard">
                        <input type="checkbox" name="v3_activate_go_mode" id="v3_activate_go_mode" @if($isActiveGoMode == true) checked @endif>
                        <label for="v3_activate_go_mode">Activate GO Mode for V3</label>
                    </div>
                        <div class="active-new-dashboard">
                            <input type="checkbox" name="activate_general_go_mode" id="activate_general_go_mode" @if($isActiveGeneralGoMode == true) checked @endif>
                            <label for="activate_general_go_mode">Activate General GO Mode</label>
                        </div>
                    <div class="purpose-server-box">
                        <label for="vehicle3">Purpose Match Server</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group" >
                                            <label>Diagram type</label>
                                            <select class="form-control select2" name="diagram_type" id="diagramType" tabindex="-1" aria-hidden="true">
                                                <option disabled="disabled" selected>Select a diagram type</option>
                                                @php
                                                $diagramTypes = ['polarArea', 'radar'];
                                                @endphp
                                                @foreach($diagramTypes as $type)
                                                    <option value="{{ $type }}" {{ $widgetSettings->diagram_type == $type ? 'selected' : '' }}>{{ ucwords(str_replace('_', ' ', $type)) }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label>Pool type</label>
                                            <div style="display: flex">
                                                <select class="form-control select2" name="pool_type" id="poolType"
                                                    tabindex="-1" aria-hidden="true" style="display: inline-block">
                                                    <option disabled="disabled" selected>Select a Pool type</option>
                                                    @foreach ($pools ?? [] as $pool)
                                                        <option value="{{ $pool->id }}" {{ $widgetSettings->pool_id == $pool->id ? 'selected' : '' }}>{{ $pool->pool_name }}</option>
                                                    @endforeach
                                                </select> 
                                                <!-- Delete button -->
                                                <div style="padding-left: 10px; display: inline-block">
                                                    <i id="sortIcon" class="fa fa-sort btn btn-success btn-xs" aria-hidden="true"
                                                        onclick="$('#widgetAnalyses').modal('show');">
                                                    </i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div> 
                            </div>
                            <div class="col-md-6">
                                <div class="pgr">
                                    <div class="form-group pool-type-input" >
                                        <label>Pool type for bar chart</label>
                                        <select class="form-control select2" name="pool_for_chart" id="poolForChart"
                                            style="width: 45%;" tabindex="-1" aria-hidden="true">
                                            <option selected value="">Select a Pool type</option>
                                            @foreach ($pools ?? [] as $pool)
                                            <option value="{{ $pool->id }}" {{ $widgetSettings->bar_chart_pool_id == $pool->id ? 'selected' : '' }}>{{
                                                $pool->pool_name }}</option>
                                            @endforeach
                                        </select>
                                        <!-- Delete button -->
                                        <div style="padding-left: 10px; display: inline-block">
                                            <i id="sortIcon" class="fa fa-sort btn btn-success btn-xs" aria-hidden="true"
                                                onclick="$('#barChartAnalyses').modal('show');">
                                            </i>
                                        </div> 
                                    </div>

                                    <button onclick="confirmDelete(event, {{ $widgetSettings->id }})"
                                        style="border: none; background: none; cursor: pointer; display: inline-block">
                                        <i class="fa fa-trash btn btn-danger btn-xs" aria-hidden="true"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="active_dashboard_product_view">
                            <input type="checkbox" id="active_dashboard_product_view" name="active_dashboard_product_view" @if($isActiveNewDashboardProductView==true) checked @endif>
                            <label for=""> Activate Dashboard Product View</label>
                        </div>
                    </div>
                    @endif
                </div><!-- /.box-body -->

                <div class="box-footer">
                    <div class='pull-right'>
                        <input type='submit' name='submit' value='Save' class='btn btn-success' />
                    </div>
                </div><!-- /.box-footer-->
            </form>
            <!--section for sorting diagram setting-->
            <!-- The modal -->
            <div class="modal fade" id="widgetAnalyses" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            <h4 class="modal-title" id="modalLabel">Sorting Analysis</h4>
                        </div>
                        <div class="modal-body">
                           <div class="pd-details-items-wrapper">
                                <ul class="list-group" id="diagram-sort">
                                    @forelse ($analyses['widgetAnalyses'] as $key => $value)
                                        <li class="list-group-item" data-identify="widgetAnalyses" data-id="{{$key}}" data-name="{{$value}}">{{$value}}</li>
                                    @empty
                                        <li class="list-group-item text-warning">No analysis found!!</li>
                                    @endforelse
                                </ul>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
            <!--section End diagram setting-->
            <!--section for barChartAnalyses setting-->
            <!-- The modal -->
            <div class="modal fade" id="barChartAnalyses" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            <h4 class="modal-title" id="modalLabel">Sorting Analysis</h4>
                        </div>
                        <div class="modal-body">
                            <div class="pd-details-items-wrapper">
                                <ul class="list-group" id="diagram-sort-barChartAnalyses">
                                    @forelse ($analyses['barChartAnalyses'] as $key => $value)
                                    <li class="list-group-item" data-identify="barChartAnalyses" data-id="{{$key}}" data-name="{{$value}}">{{$value}}</li>
                                    @empty
                                    <li class="list-group-item text-warning">No analysis found!!</li>
                                    @endforelse
                                </ul>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
            <!--section End barChartAnalyses setting-->
            <!-- Modal Dashboard V2 Settings-->
            <div class="modal fade" id="v2DashboardSettings" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <!-- Modal content-->
                    <form id="formV2DashboardSettings" class="formDashboardSettings" 
                        action="{{ CRUDBooster::mainpath('v2-dashboard-settings') }}"
                        method="POST" 
                        enctype="multipart/form-data">
                        @csrf
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                <h4 class="modal-title">Dashboard V2 Settings</h4>
                            </div>
                            <div class="modal-body" style="overflow-y: auto;max-height:650px;"></div>
                            <div class="modal-footer">
                                <button class="btn btn-primary" id="submitV2DashboardSettingsForm">Save</button>
                                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <!--End Modal Dashboard V2 Settings-->
            <!-- Modal analyses sorting-->
            @include('Admin.partials.analysesSortingModal')
            <!--End Modal analyses sorting-->
        </div>
    </div>
</div>

@endsection