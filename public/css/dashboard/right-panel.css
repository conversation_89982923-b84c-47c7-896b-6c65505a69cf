.ion.ion-ios-list {
    height: 1rem;
    display: flex;
    align-items: center;
}

.invalid-input::placeholder {
    color: #dc3545 !important;
    opacity: 1;
}

.invalid-input::-webkit-input-placeholder {
    color: #dc3545 !important;
}

.invalid-input::-moz-placeholder {
    color: #dc3545 !important;
    opacity: 1;
}

.invalid-input:-ms-input-placeholder {
    color: #dc3545 !important;
}

.invalid-input::-ms-input-placeholder {
    color: #dc3545 !important;
}

.navigation-buttons {

    button,
    select {
        all: unset !important;
        display: flex !important;
        align-items: center !important;
        border: 1px solid #d8d1d1 !important;
        color: #4E5155 !important;
        padding: 10px 20px !important;
        background-color: #fff !important;
        border-radius: 3px !important;
        gap: 6px !important;
        font-size: 1rem !important;
        transition: all 0.3s ease !important;

        &.dashActive_btn {
            border-color: #2FAB66 !important;
            color: #2FAB66 !important;
        }

        &.dashSelectActive_btn {
            border-color: #2FAB66 !important;
            color: #2FAB66 !important;
        }

    }

    select#longday {
        background: #fff url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAYCAYAAACfpi8JAAAABGdBTUEAALGPC/xhBQAAAKBJREFUSA3t1kEKgCAQBVCTDtciulJ2pWjRiapTaPwokUEjRW0zgaHSOI9PQWLbD4Mhfr7kz/1te4bYKO4JJ8KJ0ATouqUbqeuuH5TRekypb6Scsr2s6zIrHBgLQQ1qs0EAiMU8CNRmhcRgXEQRyBcMRRSDvGF8iKIQHyaEwLOixm8APm2Mq2HoVgMS6u3uZ/9q3MNj5gyhaXEinAhNgK5PpzhEO/hSMK4AAAAASUVORK5CYII=") no-repeat right 0.875rem center/17px 12px !important;
        border-color: #2FAB66 !important;
        color: #2FAB66 !important;
    }

    button:not(:disabled),
    [type="button"]:not(:disabled),
    [type="reset"]:not(:disabled),
    [type="submit"]:not(:disabled) {
        cursor: pointer !important;
    }
}

html .navigation-buttons {
    display: flex;
    gap: 0.25rem;

    button,
    select {
        transition: all 0.3s ease, transform 0.4s ease-in-out, order 0.4s ease-in-out !important;
    }
}

@media (max-width: 1950px) and (min-width: 1440px) {
    html .navigation-buttons {
        flex-direction: column;
        transition: all 0.3s ease !important;
    }
}



@media (max-width: 1440px) {
    html .navigation-buttons {
        flex-direction: column;
        transition: all 0.3s ease !important;
    }

    html .navigation-buttons {

        button,
        select {
            font-size: 12px !important;
        }
    }
}

@media (min-width: 992px) and (max-width: 1600px) {
    html:not(.layout-collapsed) {
        .tab-contents {
            .card-body {

                .topic-btns,
                .frequency-btns {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    gap: 4px;
                }
            }
        }
    }
}

@media (min-width: 1200px) and (max-width: 1400px) {
    .tab-contents {
        .card-body {

            .topic-btns,
            .frequency-btns {
                .btn {
                    padding-inline: 8px;
                }
            }
        }
    }
}

@media (min-width: 992px) and (max-width: 1200px) {
    .tab-contents {
        .card-body {

            .topic-btns,
            .frequency-btns {
                .btn {
                    font-size: 12px;
                    padding-inline: 6px;
                }
            }
        }
    }
}

@media (max-width: 992px) {
    html .navigation-buttons {
        flex-direction: row;
    }

    .navigation-buttons {

        button,
        select {
            span {
                max-width: 100% !important;
            }
        }
    }
}

@media (max-width: 576px) {
    html .navigation-buttons {
        flex-direction: column;
        transition: all 0.3s ease !important;
    }

    .tab-contents {
        .card-body {

            .topic-btns,
            .frequency-btns {
                .btn {
                    font-size: 12px;
                    padding-inline: 6px;
                }
            }
        }
    }
}

#frequencyGeneratorTab {
    background: #fff;
    background-clip: padding-box;
    border-color: #02a065 !important;
    display: none;
}

/* Modern Frequency Time Controls */
.frequency-time-controls {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
    align-items: start;
}

.frequency-input-group,
.time-input-group {
    display: flex;
    flex-direction: column;
}

.frequency-input-group .input-group,
.time-input-group .input-group {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
}

.frequency-input-group .form-control,
.time-input-group .form-control {
    border: none;
    border-radius: 0;
    box-shadow: none;
    font-weight: 500;
}

.frequency-input-group .form-control:focus,
.time-input-group .form-control:focus {
    border-color: #2FAB66;
    box-shadow: 0 0 0 0.2rem rgba(47, 171, 102, 0.25);
}

.frequency-input-group .input-group-text,
.time-input-group .input-group-text {
    background-color: #f8fafc;
    border: none;
    color: #64748b;
    font-weight: 600;
    font-size: 0.875rem;
    min-width: 45px;
    justify-content: center;
}

/* Large Desktop - Upper Range (1441px - 1599px) */
@media (min-width: 1441px) and (max-width: 1599px) {
    .frequency-time-controls {
        grid-template-columns: 2fr 1fr;
        gap: 0.875rem;
    }
}


@media (min-width: 1200px) and (max-width: 1440px) {
    html:not(.layout-collapsed) .frequency-time-controls {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    html.layout-collapsed .frequency-time-controls {
        grid-template-columns: 2fr 1fr;
        gap: 0.875rem;
    }
}

/* Medium Desktop - Layout Not Collapsed (992px - 1200px) */
@media (min-width: 992px) and (max-width: 1200px) {
    html:not(.layout-collapsed) .frequency-time-controls {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    html.layout-collapsed .frequency-time-controls {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
}

/* Tablet Portrait & Small Desktop (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .frequency-time-controls {
        grid-template-columns: 2fr 1fr;
        gap: 0.875rem;
    }
}

/* Mobile Landscape (577px - 767px) */
@media (min-width: 577px) and (max-width: 767px) {
    .frequency-time-controls {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
}

/* Mobile Portrait (≤576px) */
@media (max-width: 576px) {
    .frequency-time-controls {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .harmonics-btn {
        min-width: 45px;
        padding: 0.5rem;
    }

    .frequency-input-group .input-group-text,
    .time-input-group .input-group-text {
        min-width: 40px;
        font-size: 0.8rem;
    }
}

/* .card {
    border:none !important;
} */