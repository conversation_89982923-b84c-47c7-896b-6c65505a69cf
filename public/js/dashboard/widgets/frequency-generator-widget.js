/**
 * FrequencyGenerator Widget JavaScript
 * Single responsibility: Handle FrequencyGenerator widget functionality only
 */


class FrequencyGeneratorWidget {
    constructor(widgetId = 'default') {
        this.widgetId = widgetId;
        this.debounceTimeout = null;
        this.frequencyCalculationInProgress = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupGlobalVariables();
    }

    setupGlobalVariables() {
        // Setup global variables needed for this widget
        if (!window.routes) {
            console.warn('Routes not defined globally for FrequencyGenerator widget');
        }
        
        if (!window.biorhythDetails) {
            console.warn('BiorhythDetails not defined globally for FrequencyGenerator widget');
        }

        if (!window.translations) {
            console.warn('Translations not defined globally for FrequencyGenerator widget');
        }
    }

    setupEventListeners() {
        // Topic input event listener
        const topicInput = document.getElementById(`nav-widget-frequency-topic-${this.widgetId}`);
        if (topicInput) {
            topicInput.addEventListener('input', (e) => {
                this.debouncedUpdateTopicFrequency(e.target);
            });
        }

        // Frequency input event listener
        const freqInput = document.getElementById(`nav-widget-frequency-hz-${this.widgetId}`);
        if (freqInput) {
            freqInput.addEventListener('input', (e) => {
                this.updateFrequencyDisplay(e.target);
            });
        }

        // Time input event listener
        const timeInput = document.getElementById(`nav-widget-frequency-time-${this.widgetId}`);
        if (timeInput) {
            timeInput.addEventListener('input', (e) => {
                this.updateTimeDisplay(e.target);
            });
        }

        // Harmonics button event listener
        const harmonicsBtn = document.querySelector(`#nav-widget-frequencyGeneratorTab-${this.widgetId} .harmonics-btn`);
        if (harmonicsBtn) {
            harmonicsBtn.addEventListener('click', () => {
                this.calculateHarmonics();
            });
        }
    }

    toggleTab() {
        const tab = document.getElementById(`nav-widget-frequencyGeneratorTab-${this.widgetId}`);
        const button = document.getElementById(`nav-widget-frequency-generator-button-${this.widgetId}`);
        
        // Find the grid instance directly from the DOM, which is more robust.
        const gridContainer = button ? button.closest('.grid-stack') : null;
        const grid = gridContainer?.gridstack;
        const gridItem = button ? button.closest('.grid-stack-item') : null;

        if (!grid || !gridItem) {
            console.error("Grid or GridItem not found. Aborting toggle.");
            return;
        }

        const isOpening = tab.style.display === 'none' || tab.style.display === '';
        const cardBody = tab.querySelector('.card-body');
        
        if (isOpening) {
            // Store the current height on the element itself before expanding
            const currentHeight = gridItem.getAttribute('gs-h');
            gridItem.dataset.initialHeight = currentHeight;

            button.classList.add('dashActive_btn');
            tab.style.display = 'block';
            gridItem.classList.add('is-expanded');
            if (cardBody) cardBody.style.maxHeight = 'none'; // Temporarily disable max-height

            // Use requestAnimationFrame for smoother rendering, preventing layout thrashing.
            requestAnimationFrame(() => {
                const contentEl = gridItem.querySelector('.grid-stack-item-content');
                if (!contentEl) return;

                const desiredPixelHeight = contentEl.scrollHeight;
                const gridOpts = grid.opts;
                const cellHeight = gridOpts.cellHeight;
                const margin = gridOpts.verticalMargin ?? gridOpts.margin ?? 0;
                const newHeightInRows = Math.ceil((desiredPixelHeight + margin) / (cellHeight + margin));
                const finalHeight = Math.max(newHeightInRows, parseInt(currentHeight) || 2);
                
                // Writing the update in the next frame ensures the browser has processed the read.
                requestAnimationFrame(() => {
                    grid.update(gridItem, { h: finalHeight });
                });
            });

        } else {
            // Restore the stored initial height
            const initialHeight = parseInt(gridItem.dataset.initialHeight) || 2;

            tab.style.display = 'none';
            button.classList.remove('dashActive_btn');
            gridItem.classList.remove('is-expanded');
            if (cardBody) cardBody.style.maxHeight = ''; // Restore default max-height

            // Add a small delay to allow CSS changes to apply before updating the grid height.
            setTimeout(() => {
                grid.update(gridItem, { h: initialHeight });
            }, 50);
        }
        
        tab.classList.add('widget-fade-in');
        setTimeout(() => tab.classList.remove('widget-fade-in'), 300);
    }

    updateFrequencyDisplay(element) {
        element.value = element.value.replace(/[^0-9]/g, '');
        
        const frequency = element.value;
        const inputGroup = element.closest('.input-group');
        const placeholderElement = inputGroup ? inputGroup.nextElementSibling : element.nextElementSibling;
        
        const harmonicsBtn = document.querySelector(`#nav-widget-frequencyGeneratorTab-${this.widgetId} .harmonics-btn`);
        const clickIndicator = harmonicsBtn ? harmonicsBtn.querySelector('.click-indicator') : null;

        if (frequency && frequency >= 250 && frequency <= 20000) {
            element.style.setProperty('border-color', '#28a745', 'important');
            element.style.color = '#28a745';
            element.classList.remove('invalid-input');
            if (inputGroup) inputGroup.style.setProperty('border-color', '#28a745', 'important');
            if (placeholderElement && placeholderElement.classList.contains('form-text')) {
                placeholderElement.classList.add('d-none');
            }
            if (harmonicsBtn) {
                harmonicsBtn.classList.remove('harmonics-needed');
                harmonicsBtn.setAttribute('title', window.translations?.calculate_harmonics || 'Calculate Harmonics');
                const icon = harmonicsBtn.querySelector('i');
                if (icon) icon.className = 'fas fa-wave-square';
                if (clickIndicator) clickIndicator.classList.add('d-none');
            }
        } else if (frequency) {
            element.style.setProperty('border-color', '#dc3545', 'important');
            element.style.color = '#dc3545';
            element.classList.add('invalid-input');
            if (inputGroup) inputGroup.style.setProperty('border-color', '#dc3545', 'important');
            if (placeholderElement && placeholderElement.classList.contains('form-text')) {
                placeholderElement.classList.remove('d-none');
                placeholderElement.classList.remove('text-muted');
                placeholderElement.style.setProperty('color', '#dc3545', 'important');
            }
            if (harmonicsBtn && frequency < 250) {
                harmonicsBtn.classList.add('harmonics-needed');
                harmonicsBtn.setAttribute('title', window.translations?.frequency_too_low || 'Frequency too low');
                const icon = harmonicsBtn.querySelector('i');
                if (icon) icon.className = 'fas fa-exclamation-triangle';
                if (clickIndicator) clickIndicator.classList.remove('d-none');
            }
        } else {
            element.style.removeProperty('border-color');
            element.style.color = '';
            element.classList.remove('invalid-input');
            if (inputGroup) inputGroup.style.removeProperty('border-color');
            if (placeholderElement && placeholderElement.classList.contains('form-text')) {
                placeholderElement.classList.add('d-none');
            }
            if (harmonicsBtn) {
                harmonicsBtn.classList.remove('harmonics-needed');
                harmonicsBtn.setAttribute('title', window.translations?.calculate_harmonics || 'Calculate Harmonics');
                const icon = harmonicsBtn.querySelector('i');
                if (icon) icon.className = 'fas fa-wave-square';
                if (clickIndicator) clickIndicator.classList.add('d-none');
            }
        }
    }

    updateTimeDisplay(element) {
        element.value = element.value.replace(/[^0-9]/g, '');

        const timeSeconds = element.value;
        const inputGroup = element.closest('.input-group');
        const placeholderElement = inputGroup ? inputGroup.nextElementSibling : element.nextElementSibling;
        
        if (timeSeconds && timeSeconds >= 5 && timeSeconds <= 3600) {
            element.style.setProperty('border-color', '#28a745', 'important');
            element.style.color = '#28a745';
            element.classList.remove('invalid-input');
            if (inputGroup) inputGroup.style.setProperty('border-color', '#28a745', 'important');
            if (placeholderElement && placeholderElement.classList.contains('form-text')) {
                placeholderElement.classList.add('d-none');
            }
        } else if (timeSeconds) {
            element.style.setProperty('border-color', '#dc3545', 'important');
            element.style.color = '#dc3545';
            element.classList.add('invalid-input');
            if (inputGroup) inputGroup.style.setProperty('border-color', '#dc3545', 'important');
            if (placeholderElement && placeholderElement.classList.contains('form-text')) {
                placeholderElement.classList.remove('d-none');
                placeholderElement.classList.remove('text-muted');
                placeholderElement.style.setProperty('color', '#dc3545', 'important');
            }
        } else {
            element.style.removeProperty('border-color');
            element.style.color = '';
            element.classList.remove('invalid-input');
            if (inputGroup) inputGroup.style.removeProperty('border-color');
            if (placeholderElement && placeholderElement.classList.contains('form-text')) {
                placeholderElement.classList.add('d-none');
            }
        }
    }

    calculateHarmonics() {
        const frequencyInput = document.getElementById(`nav-widget-frequency-hz-${this.widgetId}`);
        
        if (!frequencyInput) {
            console.warn('Frequency input not found for harmonics calculation');
            return;
        }
        
        const f = parseFloat(frequencyInput.value);
        if (!f || f <= 0) return;

        const MIN = 250, MAX = 20000, BASE = 50;
        let harmonic;

        if (f < BASE) harmonic = f * BASE;
        else if (f < 200) harmonic = MIN;
        else if (f < MIN) harmonic = Math.round(f/BASE)*BASE;
        else harmonic = Math.round(f/BASE)*BASE;

        frequencyInput.value = Math.max(MIN, Math.min(MAX, harmonic));
        this.updateFrequencyDisplay(frequencyInput);
    }

    calculateFrequency(text, callback) {
        if (!text || text.trim() === '') {
            this.frequencyCalculationInProgress = false;
            this.updateCartButtonsState();
            callback(0);
            return;
        }
        
        this.frequencyCalculationInProgress = true;
        this.updateCartButtonsState();
        
        fetch(window.routes?.calculateFrequency || '/calc/calculateFrequency', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.csrfToken || document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
            },
            body: JSON.stringify({
                text: text,
                _token: window.csrfToken || document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
            })
        })
        .then(response => response.json())
        .then(data => {
            this.frequencyCalculationInProgress = false;
            this.updateCartButtonsState();
            callback(data.frequency || 0);
        })
        .catch(error => {
            console.error('Error calculating frequency:', error);
            this.frequencyCalculationInProgress = false;
            this.updateCartButtonsState();
            callback(0);
        });
    }

    updateCartButtonsState() {
        const cartButton = document.querySelector(`#nav-widget-frequencyGeneratorTab-${this.widgetId} .btn-primary`);
        if (cartButton) {
            if (this.frequencyCalculationInProgress) {
                cartButton.style.opacity = '0.5';
                cartButton.style.pointerEvents = 'none';
                cartButton.setAttribute('title', window.translations?.frequency_calculation_in_progress || 'Frequency calculation in progress');
            } else {
                cartButton.style.opacity = '1';
                cartButton.style.pointerEvents = 'auto';
                cartButton.removeAttribute('title');
            }
        }
    }

    debouncedUpdateTopicFrequency(element) {
        if (this.debounceTimeout) {
            clearTimeout(this.debounceTimeout);
        }
        
        this.debounceTimeout = setTimeout(() => {
            this.updateTopicFrequency(element);
        }, 300);
    }

    updateTopicFrequency(element) {
        const topicText = element.value;
        
        if (!topicText || topicText.trim() === '') {
            const freqInput = document.getElementById(`nav-widget-frequency-hz-${this.widgetId}`);
            const timeInput = document.getElementById(`nav-widget-frequency-time-${this.widgetId}`);
            
            if (freqInput) freqInput.value = '';
            if (timeInput) timeInput.value = '';
            return;
        }
        
        this.calculateFrequency(topicText, (frequency) => {
            const biorhythDetails = window.biorhythDetails || { min: 5, max: 300 };
            const randomTime = Math.floor(Math.random() * (biorhythDetails.max - biorhythDetails.min + 1)) + biorhythDetails.min;
            
            const freqInput = document.getElementById(`nav-widget-frequency-hz-${this.widgetId}`);
            const timeInput = document.getElementById(`nav-widget-frequency-time-${this.widgetId}`);
            
            if (freqInput) {
                if (frequency > 0) {
                    freqInput.value = frequency;
                    this.updateFrequencyDisplay(freqInput);
                } else {
                    freqInput.value = '';
                }
            }
            if (timeInput) {
                timeInput.value = randomTime;
                this.updateTimeDisplay(timeInput);
            }
        });
    }

    addToCart() {
        // Cart validation - same as original
        if (typeof cartCount !== 'undefined' && cartCount >= 200) {
            return toastr.warning(window.translations?.cart_max_allow_alert || 'Cart maximum allowed reached');
        }
        
        // Increment cart count - same as original
        if (typeof cartCount !== 'undefined') {
            cartCount++;
            const shoppingCart = document.getElementById('shoppingCart');
            const cardValue = document.getElementById('cardValue');
            
            if (shoppingCart) {
                shoppingCart.style.display = cartCount == 0 ? 'none' : 'block';
            }
            if (cardValue) {
                cardValue.textContent = cartCount;
            }
        }
        
        // Get values from inputs - adapted for widget IDs
        const topicInput = document.getElementById(`nav-widget-frequency-topic-${this.widgetId}`);
        const freqInput = document.getElementById(`nav-widget-frequency-hz-${this.widgetId}`);
        const timeInput = document.getElementById(`nav-widget-frequency-time-${this.widgetId}`);
        
        const content = topicInput?.value || '';
        const frequency = freqInput?.value || '';
        const time = timeInput?.value || '';
        
        // Validation - same as original
        if (content == null || content == '') {
            return toastr.warning(window.translations?.note_couldnt_save || 'Note couldn\'t be saved');
        }

        if (!frequency || !time) {
            return toastr.warning(window.translations?.frequency_time_both_required || 'Both frequency and time are required');
        }
        
        if (time && (time < 5 || time > 3600)) {
            return toastr.warning(window.translations?.invalid_time || 'Invalid time range');
        }

        // AJAX request - same as original
        const action = window.routes?.add2Cart || '/Sajax/add2Cart';
        
        $.ajax({
            type: 'POST',
            url: action,
            beforeSend: function() {
                Swal.fire({
                    title: window.translations?.processing || 'Processing...',
                    imageUrl: (window.assetPath || '/') + "ezgif.com-gif-maker.webp",
                    showConfirmButton: false,
                    allowOutsideClick: false,
                    showLoaderOnConfirm: false,
                    width: '300px'
                });
            },
            data: {
                ana_id: 1,
                name: content,
                submenu_id: '',
                proID: '',
                calculation: '',
                male: '',
                heart: '',
                price: '',
                causes_id: '',
                medium_id: '',
                tipp_id: '',
                color: '',
                type: 'Topic',
                frequency: frequency,
                time: time,
                _token: window.csrfToken || document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
            },
            dataType: "json",
            success: function(data) {
                if (data.success == true) {
                    const dataShow = `<li class="rem_${data.cart_id}"> <div> <p data-cartprice="${data.cart_data.price}"><a href="">${data.result} &nbsp;&nbsp;&nbsp;${data.cart_data.analysisName}</a></p> </div>` +
                        `<div class="treatment-list-icon">` +
                        `<a href="javascript:void(0)" onclick="removeCid('${data.cart_id}',${data.cart_data.analysisID})"> <i class="fas fa-times-circle danger-color"></i></a>` +
                        `</div></li>`;
                    
                    const treatmentList = document.querySelector('.treatment-list');
                    if (treatmentList) {
                        treatmentList.insertAdjacentHTML('beforeend', dataShow);
                    }
                    
                    Swal.close();
                    toastr.success(window.translations?.topic_cart_save || 'Topic added to cart successfully');
                } else if (data.success == false) {
                    // Revert cart count on error
                    if (typeof cartCount !== 'undefined') {
                        const cardValue = document.getElementById('cardValue');
                        if (cardValue) {
                            cardValue.textContent = cartCount - 1;
                        }
                    }
                    
                    Swal.fire({
                        title: data._alert,
                        html: data.message,
                        icon: data._alert_type,
                        showCancelButton: true,
                        confirmButtonColor: '#fb0404',
                        cancelButtonColor: '#d33'
                    });
                }
            },
            error: function(data) {
                console.error('Error adding to cart:', data);
                Swal.close();
                toastr.error(window.translations?.error_occurred || 'An error occurred');
            }
        });
    }
}

// Global functions for backward compatibility
window.FrequencyGeneratorWidgets = window.FrequencyGeneratorWidgets || {};

function createFrequencyGeneratorWidget(widgetId) {
    if (!widgetId) {
        console.warn('No widgetId provided for FrequencyGeneratorWidget');
        widgetId = 'default';
    }
    
    if (!window.FrequencyGeneratorWidgets[widgetId]) {
        try {
            window.FrequencyGeneratorWidgets[widgetId] = new FrequencyGeneratorWidget(widgetId);
        } catch (error) {
            console.error('Error creating FrequencyGeneratorWidget:', error);
            return null;
        }
    }
    return window.FrequencyGeneratorWidgets[widgetId];
}

function toggleFrequencyTab(widgetId) {
    // Ensure DOM is ready and function is available
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            toggleFrequencyTab(widgetId);
        });
        return;
    }
    
    const widget = createFrequencyGeneratorWidget(widgetId);
    if (widget) {
        widget.toggleTab();
    }
}

// Explicitly assign to window object to ensure global accessibility
window.toggleFrequencyTab = toggleFrequencyTab;
window.createFrequencyGeneratorWidget = createFrequencyGeneratorWidget;
window.handleAddToCart = handleAddToCart;

function btnAddCart(button, widgetId) {
    // Extract widgetId from button context if not provided
    if (!widgetId) {
        const tab = button.closest('[id*="frequencyGeneratorTab"]');
        if (tab && tab.id) {
            widgetId = tab.id.split('-').pop() || 'default';
        } else {
            widgetId = 'default';
        }
    }
    
    const widget = createFrequencyGeneratorWidget(widgetId);
    widget.addToCart();
}

// Direct handler using AJAX approach like original system
function handleAddToCart(widgetId, livewireComponent) {
    const widget = createFrequencyGeneratorWidget(widgetId);
    widget.addToCart();
}

// Auto-initialize widgets when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Find all frequency generator tabs and initialize widgets
    const tabs = document.querySelectorAll('[id*="frequencyGeneratorTab"]');
    tabs.forEach(tab => {
        const widgetId = tab.id.split('-').pop() || 'default';
        createFrequencyGeneratorWidget(widgetId);
    });
}); 