#!/bin/bash

# Load user environment to ensure PATH includes Go and other installed tools
source ~/.bashrc 2>/dev/null || source ~/.profile 2>/dev/null || true

# Ensure Go is in PATH
export PATH=$PATH:/usr/local/go/bin:/usr/bin:/snap/bin

# Exit immediately if a command fails
set -e

# Define variables
REPO="https://enfit:$<EMAIL>/enfit/start.git"
APP_DIR="$(pwd)"  # Automatically set to the current directory
USER="$(whoami)"  # Automatically set to the current user
BRANCH="main"  # Change if using a different branch

echo "Starting Laravel deployment..."

# Get the current branch from the local repository
CURRENT_BRANCH=$(git symbolic-ref --short HEAD)
echo "Using current branch: $CURRENT_BRANCH"

# Fetch the latest changes
echo "Pulling latest changes from Gitea..."
git pull $REPO $CURRENT_BRANCH

# Run database migrations
echo "Running database migrations..."
php artisan migrate --force

# Install NPM dependencies and build assets
echo "Installing NPM dependencies and building assets..."
npm ci
npm run build

# Clear cache and optimize
echo "Clearing cache and optimizing application..."
php artisan optimize

# Restart queue workers
echo "Restarting queue workers..."
supervisorctl restart all

# Reload Supervisor
echo "Reloading Supervisor..."
supervisorctl reload

# Run Go build script
echo "Building Go calculators..."
cd app/Services/Go
sh build.sh
cd $APP_DIR

# Clear Opcache
chmod +x cachetool.phar
./cachetool.phar opcache:reset --fcgi=127.0.0.1:18001

echo "Laravel deployment completed successfully! 🎉"
