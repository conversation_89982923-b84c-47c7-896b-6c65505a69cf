{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"barryvdh/laravel-dompdf": "^2.2", "doctrine/dbal": "*", "dompdf/dompdf": "*", "firebase/php-jwt": "*", "gordalina/cachetool": "^1.1", "guzzlehttp/guzzle": "^7.9", "intervention/image": "^3.9", "intervention/image-laravel": "^1.3", "ixudra/curl": "*", "laravel/framework": "^11.40", "laravel/tinker": "*", "laravel/ui": "*", "league/flysystem-aws-s3-v3": "^3.29", "livewire/livewire": "^3.5.1", "maatwebsite/excel": "*", "mantix/livewire-jodit-text-editor": "^1.6", "mikemclin/laravel-wp-password": "*", "phpoffice/phpspreadsheet": "*", "phpseclib/phpseclib": "~3.0", "predis/predis": "*", "sentry/sentry-laravel": "^4.16", "stichoza/google-translate-php": "*", "swiftmailer/swiftmailer": "^6.3", "symfony/translation": "*", "yajra/laravel-datatables-oracle": "^11.1"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"kylekatarnls/update-helper": false, "php-http/discovery": false}}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "autoload": {"psr-4": {"App\\": "app/", "crocodicstudio\\crudbooster\\": "packages/efit_crudbooster/src/", "Gloudemans\\Shoppingcart\\": "packages/shoppingcart/src/"}, "files": ["app/Helper/GeneralHelper.php", "app/Helper/functions.php", "app/Helper/DashboardHelper.php", "app/Helper/CartHelper.php", "app/Helper/CalculationHelper.php", "app/Helper/CronHelper.php", "app/Helper/FocusHelper.php", "app/Helper/ActivityLogHelper.php"], "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "require-dev": {"barryvdh/laravel-debugbar": "^3.13", "beyondcode/laravel-dump-server": "^2.0", "fakerphp/faker": "^1.23", "filp/whoops": "^2.15", "laravel/telescope": "^5.0", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.2"}}