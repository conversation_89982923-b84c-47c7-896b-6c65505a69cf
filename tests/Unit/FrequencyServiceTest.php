<?php

namespace Tests\Unit;

use Tests\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use App\Services\Calculation\FrequencyService;
use App\Services\Calculation\CalculationService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Ajax\SajaxController;

class FrequencyServiceTest extends TestCase
{
    protected FrequencyService $frequencyService;
    /** @var CalculationService&MockObject */
    protected $calculationService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->calculationService = $this->createMock(CalculationService::class);
        $this->frequencyService = new FrequencyService($this->calculationService);
    }

    public function test_calculate_frequency_returns_valid_result(): void
    {
        $topic = 'test topic';
        $options = [
            'cache_ttl' => 3600,
            'min_frequency' => 250,
            'max_frequency' => 10000,
            'apply_transformations' => true
        ];
        $expectedFrequency = 440;

        $this->calculationService->expects($this->once())
            ->method('calculateFrequency')
            ->with($topic, $options)
            ->willReturn($expectedFrequency);

        $frequency = $this->frequencyService->calculateFrequency($topic, $options);

        $this->assertEquals($expectedFrequency, $frequency);
    }

    public function test_calculate_frequency_safely_with_fallback(): void
    {
        $frequency = $this->frequencyService->calculateFrequencySafely('', [
            'fallback_frequency' => 250,
            'min_frequency' => 250,
            'max_frequency' => 10000
        ]);

        $this->assertEquals(250, $frequency);
    }

    public function test_calculate_frequency_with_rate_limit(): void
    {
        $topic = 'test topic';
        $userId = 1;
        $options = [
            'rate_limit_max' => 10,
            'rate_limit_window' => 60
        ];

        // Don’t stub RateLimiter facade directly — just mock CalculationService result
        $this->calculationService->expects($this->once())
            ->method('calculateFrequencyWithRateLimit')
            ->with($topic, $userId, $options)
            ->willReturn([
                'success' => true,
                'frequency' => 440,
                'rate_limited' => false,
                'retry_after' => 0,
                'message' => ''
            ]);

        $result = $this->frequencyService->calculateFrequencyWithRateLimit($topic, $userId, $options);

        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
        $this->assertEquals(440, $result['frequency']);
        $this->assertFalse($result['rate_limited']);
    }
    
    public function test_get_calculation_stats(): void
    {
        $userId = 1;

        RateLimiter::shouldReceive('attempts')
            ->once()
            ->with('frequency_calc_' . $userId)
            ->andReturn(5);

        RateLimiter::shouldReceive('remaining')
            ->once()
            ->with('frequency_calc_' . $userId, 10)
            ->andReturn(5);

        RateLimiter::shouldReceive('availableIn')
            ->once()
            ->with('frequency_calc_' . $userId)
            ->andReturn(0);

        $stats = $this->frequencyService->getCalculationStats($userId);

        $this->assertArrayHasKey('attempts', $stats);
        $this->assertArrayHasKey('remaining', $stats);
        $this->assertArrayHasKey('available_in', $stats);
        $this->assertArrayHasKey('rate_limited', $stats);
        $this->assertEquals(5, $stats['attempts']);
        $this->assertEquals(5, $stats['remaining']);
        $this->assertEquals(0, $stats['available_in']);
        $this->assertFalse($stats['rate_limited']);
    }

    public function test_update_frequency_from_topic(): void
    {
        $topic = 'test topic';
        $userId = 1;
        $successCalled = false;
        $successCallback = function ($frequency) use (&$successCalled) {
            $successCalled = true;
        };

        $this->calculationService->expects($this->once())
            ->method('calculateFrequencyWithRateLimit')
            ->with($topic, $userId, $this->anything())
            ->willReturn([
                'success' => true,
                'frequency' => 440,
                'rate_limited' => false,
                'retry_after' => 0,
                'message' => ''
            ]);

        $result = $this->frequencyService->updateFrequencyFromTopic(
            $topic,
            $userId,
            $successCallback
        );

        $this->assertTrue($successCalled);
        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
        $this->assertEquals(440, $result['frequency']);
    }

    public function test_transform_frequency_applies_business_rules(): void
    {
        $frequencies = [100, 150, 80];
        $expectedResults = [300, 300, 250];

        $this->calculationService->expects($this->exactly(3))
            ->method('transformFrequency')
            ->willReturnOnConsecutiveCalls(...$expectedResults);

        foreach ($frequencies as $index => $frequency) {
            $result = $this->frequencyService->transformFrequency($frequency);
            $this->assertEquals($expectedResults[$index], $result);
        }
    }

    public function test_generate_harmonics(): void
    {
        $fundamental = 440.0;
        $harmonicCount = 3;
        $expectedHarmonics = [
            ['name' => 'Subharmonic (F/2)', 'value' => 220.0, 'note' => 'A3'],
            ['name' => 'Fundamental (F)', 'value' => 440.0, 'note' => 'A4'],
            ['name' => '1st Harmonic (2F)', 'value' => 880.0, 'note' => 'A5'],
            ['name' => '2nd Harmonic (3F)', 'value' => 1320.0, 'note' => 'E6'],
            ['name' => '3rd Harmonic (4F)', 'value' => 1760.0, 'note' => 'A6']
        ];

        $this->calculationService->expects($this->once())
            ->method('generateHarmonics')
            ->with($fundamental, $harmonicCount)
            ->willReturn($expectedHarmonics);

        $harmonics = $this->frequencyService->generateHarmonics($fundamental, $harmonicCount);

        $this->assertEquals($expectedHarmonics, $harmonics);
    }

    public function test_frequency_to_note_conversion(): void
    {
        $this->calculationService->expects($this->exactly(2))
            ->method('frequencyToNote')
            ->willReturnOnConsecutiveCalls('A4', '');

        $this->assertEquals('A4', $this->frequencyService->frequencyToNote(440.0));
        $this->assertEquals('', $this->frequencyService->frequencyToNote(0));
    }

    public function test_validate_business_rules(): void
    {
        $topic = 'test topic';
        $time = 30;
        $biorythDetails = (object)[
            'gs_min_price' => 5,
            'gs_max_price' => 3600
        ];

        $this->calculationService->expects($this->once())
            ->method('validateBusinessRules')
            ->with($topic, $time, $biorythDetails)
            ->willReturn([
                'valid' => true,
                'errors' => [],
                'warnings' => [],
                'time_valid' => true
            ]);

        $result = $this->frequencyService->validateBusinessRules(
            $topic,
            $time,
            $biorythDetails
        );

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
        $this->assertEmpty($result['warnings']);
        $this->assertTrue($result['time_valid']);
    }

    public function test_validate_business_rules_with_empty_topic(): void
    {
        $topic = '';
        $time = 30;
        $biorythDetails = (object)[
            'gs_min_price' => 5,
            'gs_max_price' => 3600
        ];

        $this->calculationService->expects($this->once())
            ->method('validateBusinessRules')
            ->with($topic, $time, $biorythDetails)
            ->willReturn([
                'valid' => true,
                'errors' => [],
                'warnings' => ['Topic should not be empty'],
                'time_valid' => true
            ]);

        $result = $this->frequencyService->validateBusinessRules(
            $topic,
            $time,
            $biorythDetails
        );

        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
        $this->assertContains('Topic should not be empty', $result['warnings']);
        $this->assertTrue($result['time_valid']);
    }

    public function test_validate_business_rules_with_invalid_time(): void
    {
        $topic = 'test topic';
        $time = 1; // Too low
        $biorythDetails = (object)[
            'gs_min_price' => 5,
            'gs_max_price' => 3600
        ];

        $this->calculationService->expects($this->once())
            ->method('validateBusinessRules')
            ->with($topic, $time, $biorythDetails)
            ->willReturn([
                'valid' => false,
                'errors' => ['Time must be between 5 and 3600 seconds'],
                'warnings' => [],
                'time_valid' => false
            ]);

        $result = $this->frequencyService->validateBusinessRules(
            $topic,
            $time,
            $biorythDetails
        );

        $this->assertFalse($result['valid']);
        $this->assertContains('Time must be between 5 and 3600 seconds', $result['errors']);
        $this->assertEmpty($result['warnings']);
        $this->assertFalse($result['time_valid']);
    }

    public function test_sanitize_input(): void
    {
        $input = '<script>alert("xss")</script>test topic';
        $expected = 'test topic';

        $this->calculationService->expects($this->once())
            ->method('sanitizeInput')
            ->with($input)
            ->willReturn($expected);

        $sanitized = $this->frequencyService->sanitizeInput($input);

        $this->assertEquals($expected, $sanitized);
    }

    public function test_clear_frequency_cache(): void
    {
        $topic = 'test topic';

        $this->calculationService->expects($this->once())
            ->method('clearFrequencyCache')
            ->with($topic)
            ->willReturn(true);

        $result = $this->frequencyService->clearFrequencyCache($topic);

        $this->assertTrue($result);
    }

    public function test_submit_to_sajax_controller(): void
    {
        $request = new Request();
        $request->merge([
            'ana_id' => 1,
            'name' => 'Test Topic',
            'frequency' => 440,
            'time' => 30,
            'type' => 'Topic'
        ]);

        $expectedResponse = new JsonResponse([
            'success' => true,
            'message' => 'Added to cart successfully'
        ], 200);

        $sajaxController = $this->createMock(SajaxController::class);
        $sajaxController->expects($this->once())
            ->method('add2Cart')
            ->willReturn($expectedResponse);

        app()->instance(SajaxController::class, $sajaxController);

        $result = $this->frequencyService->submitToSajaxController($request);

        $this->assertInstanceOf(JsonResponse::class, $result);
        $this->assertEquals(200, $result->getStatusCode());

        $data = json_decode($result->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertEquals('Added to cart successfully', $data['message']);
    }

    public function test_generate_random_time(): void
    {
        $biorythDetails = (object)[
            'gs_min_price' => 5,
            'gs_max_price' => 3600
        ];

        $expectedTime = 30;

        $this->calculationService->expects($this->once())
            ->method('generateRandomTime')
            ->with($biorythDetails)
            ->willReturn($expectedTime);

        $time = $this->frequencyService->generateRandomTime($biorythDetails);

        $this->assertEquals($expectedTime, $time);
    }

    protected function tearDown(): void
    {
        app()->forgetInstance(SajaxController::class);
        parent::tearDown();
    }
}
